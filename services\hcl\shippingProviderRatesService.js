const dbpool = require('../../startup/db');
const miscConstants = require("../../misc/constants");
const cache = require('../../misc/cache.js');
const helpers = require('../../misc/helpers');
const hcldb = process.env.INITIAL_CATALOG || "hcl";

class ShippingProvideRatesService {

    /**
    * Initializes or refreshes the weight slab cache
    * @param {number} shippingProviderId - Client ID
    */
    async initializeRatesCache(shippingProviderId) {
        try {
            if (!shippingProviderId) {
                throw new Error('Shipping Provider Id is required');
            }

            const query = `${hcldb}.GetShippingProviderRates`;
            const [providerRates] = await dbpool.executeProcedure(query, [shippingProviderId]);
            // console.log(providerRates);
            // Cache the results
            const cachePrefix = miscConstants.CACHE.SHIPPING_PROVIDER_RATES.KEY;
            const allKey = `${cachePrefix}_provider_${shippingProviderId}_all`;
            const cacheTTL = miscConstants.CACHE.SHIPPING_PROVIDER_RATES.TTL;
            
            cache.set(allKey, providerRates, cacheTTL);
 
        } catch (error) {
            console.error('Error in initialize weight Cache:', {
                shippingProviderId,
                error: error.message
            });
            throw error;
        }  
    } 

    /**
    * Retrieves weight slabs from cache or database
    * @param {number} shippingProviderId - Shipping Provider Id
    */
    async getRatesFromCache(shippingProviderId) {
        try {
            if (!shippingProviderId) {
                throw new Error('shippingProviderId is required');
            }
            const cachePrefix = miscConstants.CACHE.SHIPPING_PROVIDER_RATES.KEY;
            const cacheKey = `${cachePrefix}_provider_${shippingProviderId}_all`;
            let providerRates = await cache.get(cacheKey);
            
            if (!providerRates) {
                await this.initializeRatesCache(shippingProviderId);
                providerRates = await cache.get(cacheKey);
            }

            return providerRates;

        } catch (error) {
            console.error('Error in ShippingProvideRatesService.getRatesFromCache:', {
                shippingProviderId,
                error: error.message
            });
            throw error;
        }   
    }

    /**
     * Fetch rates from the cache.
     * @param {number} shippingProviderId - Shipping Provider ID.
     * @param {string} orderType - Order Type (B2C, B2B).
     * @param {string} mode - Mode of transport (air, surface, rail, dp).
    */
    async getShippingProviderRates (shippingProviderId, orderType, mode) {
        try {
            
            if (!shippingProviderId) {
                throw new Error('shippingProviderId is required');
            }
    
            const shippingProviderRates = await this.getRatesFromCache(shippingProviderId);
            if (!Array.isArray(shippingProviderRates) || shippingProviderRates.length === 0) {
                throw new Error('No rates found');
            }

            const filteredRates = shippingProviderRates.filter(rate => {
                return (
                    (orderType === null || rate.orderType === orderType) && // Check orderType only if it's provided
                    (mode === null || rate.mode === mode) // Check mode only if it's provided
                );
            });
            
            return filteredRates;

        } catch (error) {
            throw error;
        }
    }

    /**
     * get zone wise rate, get rate from cache function
     * @param {*} shippingProviderId 
     * @param {*} orderType 
     * @param {*} mode 
     */
    async getShippingProviderZoneRates (shippingProviderId, orderType, mode) {  
        try {
            
            if (!shippingProviderId) {
                throw new Error('shippingProviderId is required');
            }

            const shippingProviderRates = await this.getShippingProviderRates(shippingProviderId, orderType, mode);
            const groupedData = shippingProviderRates.reduce((result, row) => {
                const { weightSlabId, zoneCode, ...rates } = row;

                if (!result[weightSlabId]) {
                    result[weightSlabId] = {};
                }
                if (!result[weightSlabId][zoneCode]) {
                    result[weightSlabId][zoneCode] = [];
                }

                result[weightSlabId][zoneCode].push(rates);

                return result;
            }, {});
            
            return groupedData;

        } catch (error) {
            throw error;
        }
    }

  
    /**
     * 
     * @param {*} param0 
     * @param {*} createdBy 
     * @returns 
     */
    async upsertShippingProviderRates ({shippingProviderId, mode, orderType, weightSlabId, rates}, createdBy) {
        try {
            // Convert data array into a JSON string
            const jsonData = JSON.stringify(rates);
            const inputs = [ shippingProviderId, mode, orderType, weightSlabId, jsonData, createdBy ];
            const query= `${hcldb}.UpsertShippingProviderRates`;
            // Execute the stored procedure
            const results = await dbpool.executeProcedure(query, [inputs]);
            // Cache Key and Data
            // Refresh cache after successful upsert
            await this.initializeRatesCache(shippingProviderId);  
            return results;
        } catch (error) {
            throw error;
        }
    }


    /**
     * 
     * @param {*} shippingProviderId 
     * @param {*} weightSlabId 
     * @param {*} zoneId 
     * @param {*} mode 
     * @param {*} orderType 
     * @param {*} isCod 
     * @returns 
     */
    async getRatesBasedOnWeightZone( shippingProviderId, effectiveWeight, zoneId, mode = 'surface', orderType = 'B2C' ) {
        try {
            const shippingProvideRates = await this.getShippingProviderRates(shippingProviderId, orderType, mode);
            //console.log(clientZoneRates);
            // Validate that the cache is an array
            if (!Array.isArray(shippingProvideRates)) {
                throw new Error('Invalid client zone rate. Expected an array.');
            }
        
            // // return weightSlabs;
            const filteredRates = await shippingProvideRates.filter( async (slab) => 
                slab.zoneId == zoneId
            );
            // // Step 2: Sort by base_weight in descending order
            const sortedRates = filteredRates.sort((a, b) => b.weightSlab - a.weightSlab);
        
            // // Step 3: Find the first slab where base_weight <= effective_weight
            let matchingRate = await sortedRates.find(async(rate) => rate.weightSlab <= effectiveWeight);
        
            // If no matching rate is found, throw an error
            if (!matchingRate) {
                throw new Error(`No rate found for weight slab ID ${effectiveWeight} and zone ID ${zoneId}.`);
            }
            return matchingRate; // Return the matching rate
        } catch (error) {
            throw error;
        }
    }

    /**
     * 
     * @param {*} rates 
     * @param {*} userId 
     * @returns 
     */
    async upsertShippingProviderAdditionalRates (rateData, userId) {
    
        try {
            const { shippingProviderId, orderType, rates } = rateData;
            // Convert rates array to JSON string for stored procedure
            const ratesJson = JSON.stringify(rates);

            const query= `${hcldb}.UpsertShippingProviderAdditionalRates`;
            const results = await dbpool.executeProcedure(query, [shippingProviderId, orderType, ratesJson, userId]);
            const newRates = results[0];
            // Invalidate cache for this provider
            const cacheKey = `${miscConstants.CACHE.SHIPPING_PROVIDER_Additional_RATES.KEY}${shippingProviderId}:${orderType}`;
            cache.delete(cacheKey);   
            // Store in cache
            cache.set(cacheKey, newRates, miscConstants.CACHE.SHIPPING_PROVIDER_Additional_RATES.TTL);

            return newRates;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 
     * @param {*} providerId 
     * @returns 
     */
    async getShippingProviderAdditionalRates (shippingProviderId, orderType) {
        // Try to get from cache first
        const cacheKey = `${miscConstants.CACHE.SHIPPING_PROVIDER_Additional_RATES.KEY}${shippingProviderId}:${orderType}`;
        const CACHE_TTL = miscConstants.CACHE.SHIPPING_PROVIDER_Additional_RATES.TTL;
        const cachedRates = await cache.get(cacheKey);
        
        if (cachedRates) {
            return cachedRates;
        }

        try {
            const query= `${hcldb}.GetshippingProviderAdditionalRates`;
            const results = await dbpool.executeProcedure(query, [shippingProviderId, orderType]);

            const rates = results[0];
            // Store in cache
            cache.set(cacheKey, rates, CACHE_TTL);
            
            return rates;
        } catch (error) {
            throw error;
        }
    }
}

module.exports = new ShippingProvideRatesService();      