-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `createdproducts`
--

DROP TABLE IF EXISTS `createdproducts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `createdproducts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `domain` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `owner_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `sku` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `barcode` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `item_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `item_product_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `inventory_item_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `location_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0',
  `previous_inventory_quantity` int DEFAULT NULL,
  `inventory_quantity` int DEFAULT NULL,
  `image` text CHARACTER SET utf8 COLLATE utf8_bin,
  `price` decimal(10,2) DEFAULT NULL,
  `count` int unsigned NOT NULL DEFAULT '0',
  `fby_error_flag` int unsigned NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `cron_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `isChanged` tinyint DEFAULT '0',
  `description` text,
  `specialPrice` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `createdproducts_fby_user_id` (`fby_user_id`,`sku`)
) ENGINE=InnoDB AUTO_INCREMENT=112 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `createdproducts`
--

LOCK TABLES `createdproducts` WRITE;
/*!40000 ALTER TABLE `createdproducts` DISABLE KEYS */;
INSERT INTO `createdproducts` VALUES (1,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_NPN_MFH001','9957149590000','8045171835154','Portafoglio Tascabile dal design Slim - Sirolo','8045171835154','46239245500690','0',-1,100,'https://cdn.shopify.com/s/files/1/0685/0966/3506/products/NP1127114-521_1.jpg?v=1670524973',17.90,0,0,0,'create_Products_Shopify','c8f266d7-1fff-4626-9b59-a43e4913ceb7','2022-12-08 18:42:23',NULL,0,'',NULL),(2,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_NPN_MFH002','9957149590024','8045204832530','Portafoglio Tascabile Verticale dal design Slim - Urbino','8045204832530','46239325978898','0',-1,100,'https://cdn.shopify.com/s/files/1/0685/0966/3506/products/NP288037-20_1.jpg?v=1670525977',18.90,0,0,0,'create_Products_Shopify','06f72758-1d1c-4c13-873f-60524078d8c9','2022-12-08 18:59:10',NULL,0,'',NULL),(5,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_BTA_MCD471','9951310205342','8045845119250','Chelsea BATA in pelle per Uomo','8045845119250','46241063600402','0',-1,10,'https://cdn.shopify.com/s/files/1/0685/0966/3506/products/8934539-2.jpg?v=1670545464',95.00,0,0,0,'create_Products_Shopify','e8584717-6c8f-44a1-9ce0-bbe1c4961da9','2022-12-09 00:15:05',NULL,0,'',NULL),(105,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_NPN_MFH003',NULL,'8047555576082','Portafoglio Tascabile Verticale dal design Slim - Ancona','8047555576082','46244554932498','0',-1,0,'',0.00,0,0,0,'create_Products_Shopify','52045c41-af8f-4823-b606-eb155d039e9e','2022-12-09 11:45:04',NULL,0,'',NULL),(111,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_HGB_MEA001',NULL,'8050452955410','Occhiali Da Sole Uomo','8050452955410','46255729115410','0',-1,0,'',0.00,0,0,0,'create_Products_Shopify','35ea359b-fc29-401d-bc3b-d265a01ff191','2022-12-12 08:42:05',NULL,0,'',NULL);
/*!40000 ALTER TABLE `createdproducts` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:25:56
