const dbpool = require('../../startup/db');
const helpers = require('../../misc/helpers.js');
const miscConstants = require("../../misc/constants.js");
const logger = require ("../../misc/logger.js")
const { OrderStatus, PaymentStatus, PAYMENT_MODES, StatusTransitions, 
    ORDER_TYPES, SHIPMENT_TYPES } = require('../../misc/enums/orderStatusEnum');
const { DateType } = require('fast-parquet/gen-nodejs/parquet_types');
const StatusService = require('./statusService.js');
const CodPayoutService = require('./codPayoutService.js');
const WalletService = require('./walletService.js');
const hcldb = process.env.INITIAL_CATALOG || "hcl";

class OrderService {

    /**
     * 
     * @param {*} orderData 
     * @param {*} user 
     * @returns 
     */

    static async createOrder(orderData, user) {
        try {
            let sql = `${hcldb}.CreateOrder`;

            let paymentStatus = PaymentStatus.PAID;
            let paymentMode = PAYMENT_MODES.Prepaid;
            if (helpers.isPaymentModeCOD(orderData.shipmentDetails.paymentMode)) {
                paymentMode = PAYMENT_MODES.COD;
                paymentStatus = PaymentStatus.PENDING;
            }

            const orderType = user?.client?.isB2BAccess ? ORDER_TYPES.B2B : ORDER_TYPES.B2C;
            const shipmentType = orderData.shipmentDetails.originalInvoiceNo ? SHIPMENT_TYPES.REVERSE : SHIPMENT_TYPES.FORWARD;
        
            let inputs = [
                orderData.shipmentDetails.invoiceNo,
                orderData.shipmentDetails.originalInvoiceNo || null,
                orderData.orderStatus || OrderStatus.NEW,
                orderData.orderState || OrderStatus.NEW,
                orderData.paymentStatus || paymentStatus,
                orderData.shipmentDetails.numberOfPackages || 1,

                orderData.channelOrderId || '',
                orderData.supplierOrderId || '',
                orderData.channelUserId || '',

                paymentMode,
                orderData.shipmentDetails.ndd,
                orderData.shipmentDetails.express,
                orderData.shipmentDetails.order.amount,
                orderData.shipmentDetails.order.totalAmount,
                orderData.shipmentDetails.order.collectibleCod,
                orderData.shipmentDetails.order.currency,
                orderData.shipmentDetails.consigneeFirstName,
                orderData.shipmentDetails.consigneeLastName,
                orderData.shipmentDetails.consigneeEmail,
                orderData.shipmentDetails.tnc || '',
            
                orderType,
                shipmentType,
                user.id,  // created_by_id field
                user.clientId,
                user.organizationId,

                // shipping info details
                orderData.shippingInfo.shippingProvoider.id,
                orderData.shippingInfo.totalShipmentCharges,
                orderData.shippingInfo.shipmentCharges,
                orderData.shippingInfo.codCharges,
                orderData.shippingInfo.effectiveWeight,
                orderData.shippingInfo.unitMeasurement,
                JSON.stringify(orderData.shippingInfo.packageCharges),

                // JSON Arrays
                JSON.stringify(orderData.shipmentDetails.productList),
                JSON.stringify(orderData.shipmentDetails.order.taxes),
                JSON.stringify(orderData.shipmentDetails.order.extraCharges),
                JSON.stringify(orderData.shipmentDetails.packageDetails),
                JSON.stringify(orderData.shipmentDetails.addressDetails),
                JSON.stringify(orderData.shippingInfo)
            ];

            let result = await dbpool.executeProcedure(sql, inputs);

            // Log the successful request
            // await logRequest('CreateOrder', orderData, result, 'Success');
            return result;
        } catch (error) {
            // Log the error
            console.error('create order error', error);
            //await logError('CreateOrder', error, orderData);
            throw error;
        }
    }

    /**
     * 
     * @param {*} orderId 
     * @param {*} callback 
     * @returns 
     */

    static async getOrder(orderId, callback) {
        try {
            // Execute the stored procedure to fetch the data for all necessary parts of the order
            let sql = `${hcldb}.GetOrder`;
            let inputs = [orderId];
            // await dbpool.execute(sql, inputs, function (err, results, fields) {
            let results = await dbpool.executeProcedure(sql, inputs);
            let trackingDetails = await this.getTrackingEvents(orderId);
            try {
                const [orderDetails, productList, taxes, extraCharges, packageDetails, addressDetails, shippingInfoDetails] = results;
                if (orderDetails.length > 0) {
                    let shippingInfo;
                    if( shippingInfoDetails.length > 0 ) {
                        shippingInfo = {
                            id:shippingInfoDetails[0].shippingInfoId,
                            shippingProvoider: {
                                id: shippingInfoDetails[0].shippingProviderId,
                                name: shippingInfoDetails[0].shippingProviderName,
                                type: shippingInfoDetails[0].shippingProviderType
                            },
                            awb: shippingInfoDetails[0].awb,
                            tokenNumber: shippingInfoDetails[0].tokenNumber,
                            totalShipmentCharges: shippingInfoDetails[0].totalShipmentCharges,
                            shipmentCharges: shippingInfoDetails[0].shipmentCharges,
                            codCharges: shippingInfoDetails[0].codCharges,
                            isCharged: shippingInfoDetails[0].isCharged,
                            effectiveWeight: shippingInfoDetails[0].weight,
                            unitMeasurement: shippingInfoDetails[0].weightUnit,
                            packageCharges: shippingInfoDetails[0].packageCharges,
                            waybillInfo: shippingInfoDetails[0].waybillInfo,
                        };
                    }  

                    // Transform package details to nested structure grouped by packageId
                    const transformedPackageDetails = packageDetails.reduce((acc, pkg) => {
                        const packageId = pkg.packageId || pkg.package_id; // Handle both cases
                        const existingPacket = acc.find(p => p.packageId === packageId);
                        
                        const packetDetail = {
                            type: pkg.type || pkg.package_type, // Handle both cases
                            unitMeasurement: pkg.unitMeasurement || pkg.unit_measurement,
                            unit: pkg.unit || pkg.unit_value
                        };
                        
                        if (existingPacket) {
                            existingPacket.packetDetails.push(packetDetail);
                        } else {
                            acc.push({
                                packageId: packageId,
                                packetDetails: [packetDetail]
                            });
                        }
                        
                        return acc;
                    }, []);
                    
                    // Assemble the order JSON response
                    const orderJson = {
                        orderId: orderDetails[0].orderId,
                        invoiceNo: orderDetails[0].invoiceNo,
                        originalInvoiceNo: orderDetails[0].originalInvoiceNo,
                        shipmentType: orderDetails[0].shipmentType,
                        orderType: orderDetails[0].orderType,
                        clientId: orderDetails[0].clientId,
                        organizationId: orderDetails[0].organizationId,
                        awb: orderDetails[0].awb,
          
                        shippingProviderId: orderDetails[0].shippingProviderId,
                        channelOrderId: orderDetails[0].channelOrderId,
                        supplierOrderId: orderDetails[0].supplierOrderId,
                        channelUserId: orderDetails[0].channelUserId,

                        orderStatus: orderDetails[0].orderStatus,
                        orderState: orderDetails[0].orderState,
                        paymentMode: orderDetails[0].paymentMode,
                        paymentStatus: orderDetails[0].paymentStatus,
                        numberOfPackages: orderDetails[0].numberOfPackages,
                        createdOn: orderDetails[0].createdOn,
                        updatedOn: orderDetails[0].updatedOn,
                        consigneeFirstName: orderDetails[0].customerFirstName,
                        consigneeLastName: orderDetails[0].customerLastName,
                        consigneeName: orderDetails[0].customerName,
                        customerEmail: orderDetails[0].customerEmail,
                        shipmentDetails: {
                            invoiceNo: orderDetails[0].invoiceNo,
                            originalInvoiceNo: orderDetails[0].originalInvoiceNo,
                            paymentMode: orderDetails[0].paymentMode,
                            ndd: orderDetails[0].ndd,
                            express: orderDetails[0].express,
                            consigneeFirstName: orderDetails[0].customerFirstName,
                            consigneeLastName: orderDetails[0].customerLastName,
                            consigneeName: orderDetails[0].customerName,
                            consigneeEmail: orderDetails[0].customerEmail,
                            numberOfPackages: orderDetails[0].numberOfPackages,
                            productList: productList.map(product => ({
                                packageId: product.packageId,
                                productId: product.productId,
                                name: product.name,
                                quantity: product.quantity,
                                value: product.value,
                                currency: product.currency,
                                sku: product.sku,
                                gst: product.gst,
                                gstAmount: product.gstAmount,

                            })),
                            order: {
                                amount: orderDetails[0].amount,
                                totalAmount: orderDetails[0].totalAmount,
                                collectibleCod: orderDetails[0].collectibleCod,
                                currency: orderDetails[0].currency,
                                taxes: taxes.map(tax => ({
                                    type: tax.type,
                                    percentage: tax.percentage,
                                    amount: tax.amount
                                })),
                                extraCharges: extraCharges.map(charge => ({
                                    type: charge.type,
                                    percentage: charge.percentage,
                                    amount: charge.amount
                                }))
                            },
                            
                            packageDetails: transformedPackageDetails,

                            addressDetails: addressDetails.map(addr => ({
                                addressDetailType: addr.addressDetailType,
                                warehouseId: addr.warehouseId,
                                consigneeId: addr.consigneeId,
                                consigneeFirstName: addr.consigneeFirstName,
                                consigneeLastName: addr.consigneeLastName,
                                contact: {
                                    consigneeContact: addr.consigneeContact,
                                    alternateContact: addr.alternateContact,
                                    email: addr.email
                                },
                                address: {
                                    addressId: addr.addressId,
                                    addressType: addr.addressType,
                                    addressLine1: addr.addressLine1,
                                    addressLine2: addr.addressLine2,
                                    city: addr.city,
                                    state: addr.state,
                                    zip: addr.zip,
                                    country: addr.country
                                }
                            }))
                        },
                        shippingInfo,
                        trackingDetails
                    };


                    return orderJson;
                } else {
                    const orderJson = '';
                    return orderJson;
                }

            } catch (error) {
                let msg = { error: { data: error.Message } };
                throw error;
            }

        } catch (error) {
            throw error;
        }
    }


    /**  update order function taking 
        prams : orderdata
    */    

    static async updateOrder(orderId, orderData, user, callback) {
        try {

            let paymentStatus = PaymentStatus.PAID;
            let paymentMode = PAYMENT_MODES.Prepaid;
            if ( helpers.isPaymentModeCOD(orderData.shipmentDetails.paymentMode) ) {
                paymentMode = PAYMENT_MODES.COD;
                paymentStatus = PaymentStatus.PENDING;
            }

            const orderType = user?.client?.isB2BAccess ? ORDER_TYPES.B2B : ORDER_TYPES.B2C;
            const shipmentType = orderData.shipmentDetails.originalInvoiceNo ? SHIPMENT_TYPES.REVERSE : SHIPMENT_TYPES.FORWARD;

            let sql = `${hcldb}.OrderUpdate`;

            let inputs = [
                
                orderId,
                orderData.shipmentDetails.invoiceNo,
                orderData.shipmentDetails.originalInvoiceNo || null,
                orderData.orderStatus || OrderStatus.NEW,
                orderData.paymentStatus || paymentStatus,
                orderData.shipmentDetails.numberOfPackages || 1,

                orderData.channelOrderId,
                orderData.supplierOrderId,
                orderData.channelUserId,
                
                paymentMode,
                orderData.shipmentDetails.ndd,
                orderData.shipmentDetails.express,
                orderData.shipmentDetails.order.amount,
                orderData.shipmentDetails.order.totalAmount,
                orderData.shipmentDetails.order.collectibleCod,
                orderData.shipmentDetails.order.currency,
                orderData.shipmentDetails.consigneeFirstName,
                orderData.shipmentDetails.consigneeLastName,
                orderData.shipmentDetails.consigneeEmail,
                orderData.shipmentDetails.tnc || '',
            
                orderType,
                shipmentType,
                user.id, // updated_by field

                // shipping info details
                orderData.shippingInfo.shippingProvoider.id,
                orderData.shippingInfo.totalShipmentCharges,
                orderData.shippingInfo.shipmentCharges,
                orderData.shippingInfo.codCharges,
                orderData.shippingInfo.effectiveWeight,
                orderData.shippingInfo.unitMeasurement,
                JSON.stringify(orderData.shippingInfo.packageCharges),

                // JSON Arrays
                JSON.stringify(orderData.shipmentDetails.productList),
                JSON.stringify(orderData.shipmentDetails.order.taxes),
                JSON.stringify(orderData.shipmentDetails.order.extraCharges),
                JSON.stringify(orderData.shipmentDetails.packageDetails),
                JSON.stringify(orderData.shipmentDetails.addressDetails)
            ];

            let result = await dbpool.executeProcedure(sql, inputs);
            return result;

        } catch (error) {

            throw error;
        }
    }


    /**
     * Advanced order search with filtering, pagination, and security
     * @param {Object} filters - Search criteria
     * @returns {Promise<{orders: Array, totalRecords: number, page: number, pageSize: number}>}
     */
    static async searchOrders(filters = {}) {
        // Validate and normalize filters
        const {
            clientId = null,
            organizationId = null,
            orderId = null,
            invoiceNumber = null,
            awb = null,
            express = null,
            serviceProviderId = null,
            returnAddressId = null,
            pickupAddressId = null,
            status = null,
            state = null,
            paymentStatus = null,
            paymentMode = null,
            orderFrom = null,
            orderTo = null,
            dateType = 'created',
            page = 1,
            pageSize = 25
        } = filters;

        // Prepare database inputs
        const inputs = [
            clientId,
            organizationId, 
            orderId,
            invoiceNumber,
            awb,
            express,
            serviceProviderId,
            returnAddressId,
            pickupAddressId,
            status,
            state,
            paymentStatus,
            paymentMode,
            orderFrom ? new Date(orderFrom) : null,
            orderTo ? new Date(orderTo) : null,
            dateType,
            page,
            pageSize
        ];

        try {
            // 3. Execute stored procedure
            const results = await dbpool.executeProcedure(`${hcldb}.SearchOrders`, inputs);
            
            // 4. Process results
            const totalRecords = results?.[0]?.[0]?.totalRecords ?? 0;
            const orders = results?.[1] ?? [];
            
            // 5. Return standardized response
            return {
                orders,
                totalRecords,
                page,
                pageSize,
                totalPages: Math.ceil(totalRecords / pageSize)
            };
            
        } catch (error) {
            logger.logError('Order search failed', {
                error: error.message,
                filters,
                stack: error.stack
            });
            
            throw error;
        }
    }

    /**
     * Retrieves paginated orders with filtering
     * @param {Object} filters - Filter criteria {
     *   status?: string,
     *   state?: string,
     *   paymentStatus?: string,
     *   paymentMode?: string,
     *   clientId?: string,
     *   organizationId?: string,
     *   page: number,
     *   pageSize: number
     * }
     * @returns {Promise<{orders: Array, pagination: Object}>}
     */
    static async getAllOrders(filters = {}) {
        // Validate and set default values
        const {
            status = null,
            state = null,
            paymentStatus = null,
            paymentMode = null,
            clientId = null,
            organizationId = null,
            page = 1,
            pageSize = 25
        } = filters;

        try {
            // Prepare database inputs
            const inputs = [
                status,
                state,
                paymentStatus,
                paymentMode,
                clientId,
                organizationId,
                page,
                pageSize
            ];

            //  Execute stored procedure
            const results = await dbpool.executeProcedure(`${hcldb}.GetAllOrders`, inputs);
            
            //  Process results
            const totalRecords = results?.[0]?.[0]?.totalRecords ?? 0;
            const orders = results?.[1] ?? [];
            
            // Return standardized response
            return {
                orders,
                pagination: {
                    page,
                    pageSize,
                    totalRecords,
                    totalPages: Math.ceil(totalRecords / pageSize)
                }
            };
            
        } catch (error) {
            logger.logError('Order fetch failed in service', {
                error: error.message,
                filters,
                stack: error.stack
            });
            
            throw error;
        }
    }




    /**
     * 
     * @param {*} orderId 
     * @returns 
     */

    static async checkOrderExists(orderId) {
        try {
            const sql = `${hcldb}.CheckOrderExists`;
            const [rows] = await dbpool.executeProcedure(sql, [orderId]);
            return rows[0]?.order_exists === 1; // Return true if exists, false otherwise
        } catch (error) {
            throw error;
        }
    }
    
    /**
     * 
     * @param {*} currentStatus 
     * @param {*} newStatus 
     * @returns 
     */
    static isValidStatusTransition(currentStatus, newStatus) {
        const allowedTransitions = StatusTransitions[currentStatus];
        return allowedTransitions && allowedTransitions.includes(newStatus);
    }
    /**
    * 
    * @param {*} orderId 
    * @param {*} status 
    * @param {*} remarks 
    * @param {*} userId 
    * @returns 
    */
    static async orderUpdateStatus(orderId, newStatus, remarks, timestamp = null, userId = 0) {
        try {

            if (!orderId || !newStatus) {
                throw new Error('Missing required parameters: orderId or newStatus');
            }

            // Get current order status
            const currentOrder = await this.getOrder(orderId);
            if (!currentOrder) {
                throw new Error(`Order ${orderId} not found`);
            }

            // Check if status is already the same
            const currentStatus = currentOrder.orderStatus;
            if(currentStatus.toUpperCase() === newStatus.toUpperCase()) {
                return { success: false, message: `order is already in ${newStatus} status.`, orderId } ;
            }

            // Validate status transition
            if (!this.isValidStatusTransition(currentStatus, newStatus)) {
                return { success: false, message: `Invalid status transition from ${currentStatus} to ${newStatus}`, orderId } ;
            }

            // Get status details
            const statuses = await StatusService.statusesMaster();
            const statusInfo = statuses[`${newStatus}`];
            if (!statusInfo || !statusInfo[0]) {
                throw new Error(`Invalid status: ${newStatus}`);
            }
            const newState = statusInfo[0].state;

            // Update order status in database
            const sql = `${hcldb}.OrderUpdateStatus`;
            const inputs = [
                    orderId, 
                    newStatus, 
                    newState,
                    remarks, 
                    timestamp,
                    userId
                ];
  
            const results = await dbpool.executeProcedure(sql, inputs);
            // Verify the update was successful
            if (!results || !results[0] || !results[0][0]) {
                throw new Error('Failed to update order status');
            }

            const updateResult = results[0][0];

            // Perform additional actions based on new status
            try {
                await this.performStatusSpecificActions(currentOrder, newStatus, currentStatus, timestamp, userId);
            } catch (actionError) {
                console.error(`Error in performStatusSpecificActions for order ${orderId}:`, actionError);
                logger.logError(`Error in performStatusSpecificActions for order ${orderId}:`, actionError);
            }
            return updateResult;
            
        }  catch (error) {
            if (error.message.includes('database') || error.message.includes('SQL')) {
                console.error('Database error:', error);
                throw new Error('An error occurred while updating the order status');
            }
            throw error;
        }     
    }

    /**
     * 
     * @param {*} orderId 
     * @param {*} newStatus 
     * @param {*} oldStatus 
     */
    static async performStatusSpecificActions(order, newStatus, oldStatus, timestamp = null, userId) {
        try {
            const status = String(newStatus).toLowerCase();
            logger.logInfo(`Performing action for status: ${status}, Order ID: ${order?.orderId}`);
            console.log(`Performing action for status: ${status}, Order ID: ${order?.orderId}`);
    
            switch (status) {
                case 'delivered':
                    await this.handleDeliveryCompletion(order, timestamp, userId);
                    break;
                    
                case 'shipped':
                    await this.handleShipmentInitiated(order, timestamp, userId);
                    break;
                    
                case 'canceled':
                    await this.handleOrderCancellation(order, oldStatus, timestamp, userId);
                    break;
                    
                case 'rto':
                    await this.handleFailedDelivery(order, timestamp);
                    break;
                    
                default:
                    logger.logInfo(`No specific action defined for status: ${status}`);
                    console.warn(`No specific action defined for status: ${status}`);
            }
        } catch (error) {
            logger.logError(`Error performing action for status: ${newStatus}, Order ID: ${order?.orderId}`, error);
            console.error(`Error performing action for status: ${newStatus}, Order ID: ${order?.orderId}`, error);
            throw error;
        }
    }

    /**
     * 
     * @param {*} order 
     * @param {*} timestamp 
     * @param {*} userId 
     */
    static async handleDeliveryCompletion(order, timestamp, userId) {    
        const clientId = order.clientId;
        const organizationId = order.organizationId;
        const orderId = order.orderId;
        const invoiceNum = order.invoiceNo;
        const amount = order.shippingInfo.totalShipmentCharges;
        const isCharged = order.shippingInfo.isCharged;
        const awb = order.awb;
        const deliveredAT = timestamp;    
        try {
            // Update COD Payout if applicable and Only if COD Amount exists
            if (helpers.isPaymentModeCOD(order.paymentMode)) {  
                const codAmount = order.shipmentDetails.order.collectibleCod;      
                const existingPayout = await CodPayoutService.getCodPayoutByOrderId(orderId);
                
                if (!existingPayout) {
                    await CodPayoutService.createCodPayoutEntry(
                        clientId, orderId, awb, invoiceNum, codAmount, deliveredAT, organizationId
                    );
                    console.log(`COD payout entry created for OrderId: ${orderId}, Amount: ${codAmount}`);
                    logger.logInfo(`COD payout entry created for OrderId: ${orderId}, Amount: ${codAmount}`);
                } else {
                    console.warn(`COD payout already exists for OrderId: ${orderId}, skipping duplicate entry.`);
                    logger.logInfo(`COD payout already exists for OrderId: ${orderId}, skipping duplicate entry.`);
                }
            }
    
            // Wallet deduction logic
            if (isCharged === 0) {
                const amonutCharged = await WalletService.chargeForShipment(orderId, clientId, amount, awb, userId);
                if(amonutCharged.success === true) {
                    await this.updateOrderChargeStatus(orderId, 1, userId);
                }
            }                                    
        } catch (error) {
            logger.logError(`Error in delivery completion handling. OrderId: ${orderId}`, error);
            console.error(`Error in delivery completion handling for Order ID: ${orderId}`, error);
            throw error;
        }
    }

    static async handleShipmentInitiated(order, userId) {
        try {  
            // Add notication to table to be sent
             
        } catch (error) {
            console.error('Error in shipment handling:', error);
            throw error;
        }
    }

    static async handleOrderCancellation(order, previousStatus, userId) {
        try {
            if (previousStatus !== 'new') {
               // Cancel AWB and notification
            }
            // Add notication to table to be sent
  
        } catch (error) {
            console.error('Error in cancellation handling:', error);
            throw error;
        }
    }

    /**
     * 
     * @param {*} order 
     */
    static async handleFailedDelivery(order, userId) {
        try {
  
            if (!order || !order.clientId || !order.orderId || !order.shipmentDetails?.shippingInfo) {
                throw new Error('Invalid order data for RTO processing.');
            }
    
            const { clientId, orderId, awb } = order;
            const { totalShipmentCharges, isCharged } = order.shipmentDetails.shippingInfo;
    
            // Calculate RTO charge (double the forward charge)
            const amount = totalShipmentCharges * 2;

            console.log(`Processing failed delivery (RTO) for Order ID: ${orderId}, Client ID: ${clientId}, AWB: ${awb}, RTO Charge: ${amount}`);
            logger.logInfo(`Processing failed delivery (RTO) for Order ID: ${orderId}, Client ID: ${clientId}, AWB: ${awb}, RTO Charge: ${amount}`);
            
            // Wallet deduction logic
            if (isCharged === 0) {
                const amountCharged = await WalletService.chargeForShipment(orderId, clientId, amount, awb, userId);
    
                if (Boolean(amountCharged?.success)) {
                    await OrderService.updateOrderChargeStatus(orderId, 1, userId);
                    logger.logInfo(`RTO charge applied successfully for Order ID: ${orderId}`);
                } else {
                    logger.logInfo(`Failed to apply RTO charge for Order ID: ${orderId}`);
                }
            } else {
                logger.logInfo(`Order ID: ${orderId} has already been charged for RTO.`);
            }
    
            // TODO: Implement notification logic
    
        } catch (error) {
            logger.logError(`Error in failed delivery handling for Order ID: ${order?.orderId || 'Unknown'}`, error);
            throw error;
        }
    }
    /**
     * 
     * @param {*} orderId 
     * @param {*} status 
     * @param {*} remarks 
     * @param {*} userId 
     * @returns 
     */
    static async assignAWBAndUpdateOrderCharges (
        orderId, 
        shippingProvoiderId, 
        awb,
        updateStatus, 
        updateState, 
        remarks,
        shouldState, 
        totalShipmentCharges, 
        shipmentCharges, 
        codCharges,
        userId
    ) {
        try {
            const sql = `${hcldb}.AssignAWBAndUpdateOrder`;
            const inputs = [
                orderId, 
                shippingProvoiderId, 
                awb,
                updateStatus, 
                updateState,
                remarks,
                shouldState,
                totalShipmentCharges, 
                shipmentCharges, 
                codCharges,
                userId
                ];

            const results = await dbpool.executeProcedure(sql, inputs);
            if(results[0] && results[0][0].lastInsertedId > 0) {
                return true
            } else {
                return false;
            }
            
        }  catch (error) {
            throw error;
        }     
    }

    /**
     * 
     * @param {*} shippingData 
     * @returns 
     */
    static async updateShippingDetails({
        orderId,
        shippingProvider,
        awbDetails,
        statuses,
        userId
    }) {
        try {
            const shippingData = [
                orderId, 
                shippingProvider.id,
                awbDetails.awbNumber,
                awbDetails?.tokenNumber || '',
                JSON.stringify(awbDetails.waybillInfo),
                statuses.ready_to_ship[0].status, 
                statuses.ready_to_ship[0].state, 
                'Shipment Created',
                statuses.new[0].state, 
                userId
            ];  
            const sql = `${hcldb}.UpdateOrderShippingDetails`;
            const results = await dbpool.executeProcedure(sql, shippingData);
            
            if(results[0] && results[0][0].lastInsertedId > 0) {
                return true
            } else {
                throw new Error('Updation Error: Not able to update shipping details.')
            }          
        }  catch (error) {
            throw new Error(`Error updating shipping details: ${error.message}`);
        }    
    }

    static async getShippingDetailsByAWB (awb) {
        try{
            const sql = `${hcldb}.GetShippingDetailsByAWB`;
            const [results] = await dbpool.executeProcedure(sql, [awb]);
            if (results.length > 0) {
                return results[0]; // Return first result
            } else {
                return null; // No data found
            }
        } catch (error) {
            console.error("Error fetching shipping details by AWB:", error);
            throw error;
        } 
    }

    static async updateOrderChargeStatus(orderId, isCharged, updatedBy) {
        try {
            const query = `${hcldb}.UpdateOrderChargeStatus`; // Ensure this is the correct stored procedure
            const result = await dbpool.executeProcedure(query, [orderId, isCharged, updatedBy]);
    
            return { 
                success: true, 
                message: 'Order charge status updated successfully', 
                result 
            };
            
        } catch (error) {
            logger.logError('Error updating order charge status:', error);
            console.error('Error updating order charge status:', error);
    
            return { 
                success: false, 
                message: 'Failed to update order charge status', 
                error 
            };
        }
    }

    static async getOrdersByStatus(statuses) {
        try{
            const sql = `${hcldb}.GetOrdersByStatus`;
            const [orders] = await dbpool.executeProcedure(sql, [statuses]);
            if (orders.length > 0) {
                return orders; // Return first result
            } else {
                return null; // No data found
            }
        } catch (error) {
            console.error("Error fetching orders", error);
            logger.logError("Error fetching orders", error);
            throw error;
        } 
    }

    /**
     * 
     * @param {*} orderId 
     * @param {*} userId 
     * @returns deleted order
     */

    static async deleteOrder(orderId, userId) {
        try {
            const sql = `${hcldb}.DeleteOrder`;
            const inputs = [
                    orderId, 
                    userId
                ];
                
            const results = await dbpool.executeProcedure(sql, inputs);
            if (results && results[0] && results[0].length > 0) {
                return results[0][0]; // Get the first row of the first result set
            } else {
                throw new Error('No valid response from the database.');
            }
            
        }  catch (error) {
            throw error;
        }     
    }

    static async getAddressFromOrder(order, addressType = null) {
        try {
            const addressDetails = order.shipmentDetails.addressDetails;
            const pickUpAddress = helpers.filterAddress(addressDetails, "pickUpAddress");
            const deliveryAddress = helpers.filterAddress(addressDetails, "consigneeDetails");
            const returnAddress = helpers.filterAddress(addressDetails, "returnAddress");
            return { pickUpAddress, deliveryAddress, returnAddress};
        } catch (error) {
            throw error;
        }
    }

    static async getWeights(order) {
        try {
            const packageDetails = order.shipmentDetails.packageDetails;
            const weight = await helpers.filterPackageWeight(packageDetails, 'weight');
            const height = await helpers.filterPackageWeight(packageDetails, 'height');
            const width = await helpers.filterPackageWeight(packageDetails, 'width');
            const length = await helpers.filterPackageWeight(packageDetails, 'length');

            return {weight, height, width, length};

        } catch (error) {
            throw error;
        }
    }

    /**
     * Get package details from order
     * @param {Object} order - The order object
     * @returns {Array} Array of package objects
     */
    static async getPackageDetails(order) {
        try {
            if (!order.shipmentDetails?.packageDetails) {
                throw new Error('No package details found in order');
            }

            // Transform package details into consistent format
            return order.shipmentDetails.packageDetails.map(pkg => {
                const firstProduct = order.shipmentDetails.productList
                    .find(prod => (prod.packageId || 1) === pkg.packageId);
                
                if (!firstProduct) {
                    throw new Error(`No products found for package ${pkg.packageId}`);
                }

                // Extract weight with its unit
                const weightDetail = pkg.packetDetails?.find(d => 
                    d.type.toLowerCase() === 'weight'
                );
                // Extract dimension unit (assuming all dimensions use same unit)
                const dimensionDetail = pkg.packetDetails?.find(d => 
                    d.type.toLowerCase().includes('dimension')
                );

                return {
                    packageId: pkg.packageId,
                    weight: weightDetail?.unit ? parseFloat(weightDetail.unit) : 0,
                    weightUnit: weightDetail?.unitMeasurement || 'kg', // Default to kg
                    length: this._getPackageDimension(pkg, 'length'),
                    width: this._getPackageDimension(pkg, 'width'),
                    height: this._getPackageDimension(pkg, 'height'),
                    dimensionUnit: dimensionDetail?.unitMeasurement || 'cm', // Default to cm
                    value: firstProduct.value,
                    valueUnit: firstProduct.currency || 'INR' // Default to INR
                };
            });
        } catch (error) {
            logger.logError(`Failed to get package details: ${error.message}`);
            throw new Error(`Package details error: ${error.message}`);
        }
    }

    static _getPackageWeight(pkg) {
        const weightDetail = pkg.packetDetails.find(d => d.type.toLowerCase() === 'weight');
        if (!weightDetail) throw new Error(`Weight not found for package ${pkg.packageId}`);
        return weightDetail.unit;
    }

    static _getPackageDimension(pkg, dimensionType) {
        const dimDetail = pkg.packetDetails?.find(d => 
            d.type.toLowerCase() === `dimensions${dimensionType.toLowerCase()}`
        );
        if (!dimDetail) throw new Error(`${dimensionType} not found for package ${pkg.packageId}`);
        return dimDetail?.unit ? parseFloat(dimDetail.unit) : 0;
    }


    static async addTrackingEvents(orderId, trackingEventArray, batchSize = 50) {
        const connection = await dbpool.getConnection();  
        const procedureName = `${hcldb}.AddTrackingEvent`;  
    
        try {
            await connection.query('SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED');
    
            if (!Array.isArray(trackingEventArray) || trackingEventArray.length === 0) {
                throw new Error('Tracking event array must be a non-empty array.');
            }
    
            // Sort trackingEventArray by timestamp in descending order
            trackingEventArray.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

            await connection.beginTransaction();
    
            const results = [];
           
            for (let i = 0; i < trackingEventArray.length; i += batchSize) {
                const batch = trackingEventArray.slice(i, i + batchSize);
    
                const promises = batch.map(async (trackingEventObject) => {
                    if (!trackingEventObject) {
                        throw new Error('Tracking event object cannot be null or undefined.');
                    }
    
                    const {
                        warehouseId,
                        hubName, 
                        eventType,
                        status, 
                        timestamp, 
                        statusMessage, 
                        location, 
                        mode,
                        hubsTrackingType, 
                        returnReason, 
                        deliveryAgent
                    } = trackingEventObject;
    
                    if (!warehouseId || !hubName || !eventType || !timestamp || !statusMessage || !location || !mode || !hubsTrackingType) {
                        throw new Error('Missing required fields in tracking event object.');
                    }
    
                    const inputs = [
                        orderId,
                        warehouseId,
                        hubName,
                        eventType,
                        status || null,
                        timestamp,
                        statusMessage,
                        location,
                        mode,
                        hubsTrackingType,
                        returnReason || null,
                        deliveryAgent ? deliveryAgent.agentName : null,
                        deliveryAgent ? deliveryAgent.agentContact : null,
                        deliveryAgent ? deliveryAgent.agentEmail : null,
                        deliveryAgent ? deliveryAgent.agentId : null
                    ];
                    const sql = `CALL ${procedureName}(${inputs.map(() => '?').join(', ')})`;
                    const [procedureResult] = await connection.query(sql, [...inputs]);
    
                    const insertedEvent = procedureResult[0];
    
                    if (!insertedEvent) {
                        throw new Error('Failed to insert tracking event.');
                    }
    
                    return insertedEvent;  
                });
    
                const batchResults = await Promise.all(promises);
    
                results.push(...batchResults);
            }
    
            await connection.commit();

            // Get the latest status from the first element (since we sorted by timestamp)
            const latestStatus = trackingEventArray[0]?.status;
            const latestRemarks = trackingEventArray[0]?.statusMessage;
            const latestTimestamp = trackingEventArray[0]?.timestamp;

            // Update order status with the latest status
            if (latestStatus) {
                await this.orderUpdateStatus(orderId, latestStatus, latestRemarks, latestTimestamp);
            }
    
            return results;  
    
        } catch (error) {
            await connection.rollback();
            console.error(`Error adding tracking events: \n${error.message}\n${error.stack}`);
            throw error;  
    
        } finally {
            await connection.release();
        }
    }

    static async getTrackingEvents(orderId) {
        try {

            if (!orderId || isNaN(orderId)) {
                throw new Error('Invalid order ID');
            }

            const sql = `${hcldb}.GetTrackingEventsByOrderId`;
            const inputs = [
                orderId,
            ];

            const results = await dbpool.executeProcedure(sql, inputs);

            if (!results || results.length === 0) {
                throw new Error('No tracking events found for this order');
            }

            const trackingDetails = results[0].map(event => {
                const trackingEvent = {
                    eventType: event.eventType,
                    status: event.status ,
                    warehouseId: event.warehouseId,
                    hubName: event.hubName,
                    timestamp: event.timestamp,
                    statusMessage: event.statusMessage,
                    location: event.location,
                    mode: event.mode,
                    hubsTrackingType: event.hubsTrackingType
                };

                if (event.returnReason) {
                    trackingEvent.returnReason = event.returnReason;
                }

                if (event.deliveryAgentName && event.deliveryAgentContact && event.deliveryAgentEmail) {
                    trackingEvent.deliveryAgent = {
                        agentId: event.deliveryAgentId || `${event.deliveryAgentName}_${event.deliveryAgentContact}`,
                        agentName: event.deliveryAgentName,
                        agentContact: event.deliveryAgentContact,
                        agentEmail: event.deliveryAgentEmail,
                    };
                }

                return trackingEvent;
            });

            return trackingDetails;
        } catch (error) {
            throw error;  // Pass the error to the controller
        }
    }

}


module.exports = OrderService;
