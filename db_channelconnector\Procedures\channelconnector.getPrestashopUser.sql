DROP PROCEDURE IF EXISTS channelconnector.getPrestashopUser;

DELIMITER $$
CREATE PROCEDURE `getPrestashopUser`(
	IN `in_fby_user_id` INT, 
	IN `in_domain` VARCHAR(64)
	)
BEGIN
SET SQL_SAFE_UPDATES = 0;
	/*
		call channelconnector.`getPrestashopUser`(1002,prestashop.yocabe.com);
		or
		call channelconnector.`getPrestashopUser`(1002,"");
    */
  IF in_domain <> '' THEN
    SELECT * FROM prestashop_account AS PA 
	WHERE 
		PA.fby_user_id = in_fby_user_id 
		AND PA.domain = in_domain;
    ELSE
    SELECT * FROM prestashop_account AS PA 
	WHERE 
		PA.fby_user_id = in_fby_user_id;
    END IF;
	
SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;





