{"version": "0.2.0", "configurations": [{"console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "name": "nodemon", "program": "${workspaceFolder}/app.js", "request": "launch", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"name": "Attach to Chrome", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}"}, {"command": "npm start", "name": "Run npm start", "request": "launch", "type": "node-terminal"}, {"name": "Attach", "port": 9229, "request": "attach", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"name": "Attach by Process ID", "processId": "${command:PickProcess}", "request": "attach", "skipFiles": ["<node_internals>/**"], "type": "node"}, {"type": "node", "name": "Run Script: start", "request": "launch", "cwd": "${workspaceFolder}"}]}