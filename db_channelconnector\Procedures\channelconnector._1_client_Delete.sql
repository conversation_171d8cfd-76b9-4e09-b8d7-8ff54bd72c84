DROP PROCEDURE IF EXISTS channelconnector._1_client_Delete;

DELIMITER $$
CREATE PROCEDURE channelconnector.`_1_client_Delete`(
  `in_clientId` varchar(1024)
  )
BEGIN
	/*
		call channelconnector.`_1_client_Delete`(
			'Id123'
        );
    */
    DECLARE isExists TINYINT; 
    DECLARE in_ownerCode varchar(1024);
    
	SET in_clientId = LOWER(`in_clientId`);
    
    SET in_ownerCode = (
        SELECT LOWER(ownerCode) FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
        LIMIT 1 
    );
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		SELECT 1 AS isErrorNotFound;
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
        UPDATE `channelconnector`.`_1_client`
		SET
			`isActive` = 0,
			`modifiedOn` = NOW()
		WHERE 	 
			LOWER(clientId) = in_clientId 
			AND isActive = 1;
         /*   
        UPDATE `channelconnector`.`_2_channel`
		SET
			`isActive` = 0,
			`modifiedOn` = NOW()
		WHERE 	 
			LOWER(ownerCode) = in_ownerCode 
			AND isActive = 1;    
           */ 
		SET SQL_SAFE_UPDATES = 1;
        
		SELECT   
			clientId,
            `name`,
            ownerCode,
            CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn,
            1 as `isDeleted`
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			LOWER(clientId) = in_clientId 
            ORDER BY `id` DESC
            LIMIT 1;

    END IF;
    
END$$
DELIMITER ;
