// hcl/services/bulkOrderService.js
const dbpool = require('../../startup/db.js');
const miscConstants = require("../../misc/constants.js");
const CacheService = require('../../misc/cache.js');
const helpers = require('../../misc/helpers.js');
const logger = require('../../misc/logger.js');
const { PAYMENT_MODES, ORDER_TYPES } = require('../../misc/enums/orderStatusEnum');
const OrganizationService  = require('../organizations/organizationService.js');
const ShipmentService  = require('./shipmentsService.js');
const WarehouseService = require('./warehouseService.js');

const OrderService = require('./orderService');

class BulkOrderService {
    static async processBulkOrders(ordersData, user) {
        const organizationId = user.organizationId;
        const clientId = user.clientId;

        let tnc;
        try {
            const orgPage = await OrganizationService.getOrgPage(organizationId, "terms");
            tnc = orgPage?.content || "Please agree to our TNC";
        } catch (error) {
            console.error("Failed to fetch TNC:", error.message);
            tnc = "Please agree to our TNC";
        }
    
        let successOrders = [];
        let failedOrders = [];
        let batchOrders = [];
    
        let groupedOrders = {};
        for (let order of ordersData) {
            if (!groupedOrders[order.invoiceNo]) {
                groupedOrders[order.invoiceNo] = [];
            }
            groupedOrders[order.invoiceNo].push(order);
        }
    
        for (let invoiceNo in groupedOrders) {
            let products = groupedOrders[invoiceNo];
            try {
                let orderPayload = await createOrderPayload(products, invoiceNo, user, tnc);
                batchOrders.push(orderPayload);
            } catch (error) {
                console.error(`Error creating order payload for invoice ${invoiceNo}:`, error.message);
                for (let product of products) {
                    failedOrders.push({
                        ...product,
                        status: "Failed",
                        processing_status: "Failed",
                        reason: error.message || "Unknown error during order processing"
                    });
                }
            }
    
            if (batchOrders.length === 50) {
                const { success, failed } = await this.processOrderBatch(batchOrders, user);
                successOrders.push(...success);
                failedOrders.push(...failed);
                batchOrders = [];
            }
        }
    
        if (batchOrders.length > 0) {
            const { success, failed } = await this.processOrderBatch(batchOrders, user);
            successOrders.push(...success);
            failedOrders.push(...failed);
        }
    
        return { successOrders, failedOrders };
    }
    
    static async processOrderBatch(batchOrders, user) {
        let successOrders = [];
        let failedOrders = [];

        for (let orderData of batchOrders) {
            try {
                let result = await OrderService.createOrder(orderData, user);
                successOrders.push(result);
            } catch (error) {
                failedOrders.push({ order: orderData, error: error.message });
            }
        }
        return { success: successOrders, failed: failedOrders };
    }
}

async function createOrderPayload(products, invoiceNo, user, tnc) {
    try {
        // Validate and get pickup pincode
        const pickUpWarehouse = await WarehouseService.getWarehouseAddressById(products[0].pickUpWarehouseId);
        if (!pickUpWarehouse[0]?.zip) {
            throw new Error("Pickup warehouse pincode not found.");
        }
        const pickupPincode = pickUpWarehouse[0].zip;

        // Group products by package
        const packages = groupProductsByPackage(products);
        const numberOfPackages = Object.keys(packages).length;

        // Calculate shipping rates
        const bestProviderRates = await calculateBestShippingProvider(packages, user, pickupPincode);
        const packageCharges = bestProviderRates.packageRates.map(rate => ({
            packageId: rate.packageId,
            shipmentCharges: rate.shippingCharges.toFixed(2),
            codCharges: rate.codCharges.toFixed(2),
        }));

        // Calculate order amounts
        const { amount, gstAmount, totalAmount } = calculateOrderAmounts(products);
        const packageDetails = buildPackageDetails(packages);

        // Construct payload
        return {
            shipmentDetails: {
                invoiceNo,
                originalInvoiceNo: products[0].originalInvoiceNo || null,
                paymentMode: products[0].paymentMode,
                ndd: products[0].ndd,
                express: products[0].express,
                consigneeFirstName: products[0].consigneeFirstName,
                consigneeLastName: products[0].consigneeLastName,
                consigneeEmail: products[0].consigneeEmail,
                numberOfPackages,
                tnc,
                productList: products.map(product => ({
                    name: product.productName,
                    quantity: product.quantity,
                    value: product.value,
                    currency: "INR",
                    sku: product.sku,
                    gst: product.gst,
                    gstAmount: parseFloat(product.value * (product.gst / 100)).toFixed(2),
                    packageId: product.packageId || 1
                })),
                order: {
                    amount,
                    totalAmount,
                    collectibleCod: helpers.isPaymentModeCOD(products[0].paymentMode) ? totalAmount : "0.00",
                    currency: "INR",
                    taxes: [{ type: "GST", percentage: 0, amount: gstAmount }],
                    extraCharges: [{ type: "Other", percentage: 0, amount: 0 }]
                },
                packageDetails,
                addressDetails: buildAddressDetails(products)
            },
            shippingInfo: {
                shippingProvoider: { 
                    id: bestProviderRates.packageRates[0]?.providerId, 
                    name: bestProviderRates.packageRates[0]?.providerName 
                },
                awb: "",
                totalShipmentCharges: parseFloat(bestProviderRates.totalCost).toFixed(2),
                shipmentCharges: parseFloat(bestProviderRates.totalCost - bestProviderRates.totalCod).toFixed(2),
                codCharges: parseFloat(bestProviderRates.totalCod).toFixed(2),
                effectiveWeight: packageCharges.reduce((sum, p) => sum + (p.weight || 1), 0),
                unitMeasurement: "kg",
                packageCharges
            }
        };
    } catch (error) {
        console.error('create bulk order payload error', error);
        logger.logError('Not able to create bulk order', error);
        throw new Error(`Not able to create order: ${error.message}`);
    }
}

async function calculateBestShippingProvider(packages, user, pickupPincode) {
    const providerTotals = {};
    const allPackageRates = [];

    for (const [packageId, packageProducts] of Object.entries(packages)) {
        const firstProduct = packageProducts[0];
        const costs = await ShipmentService.calculateShipmentCost({
            clientId: user.clientId,
            sourcePincode: pickupPincode,
            destinationPincode: firstProduct.zip,
            weight: Number(firstProduct.packageWeight),
            height: Number(firstProduct.packageHeight),
            width: Number(firstProduct.packageWidth),
            length: Number(firstProduct.packageLength),
            mode: firstProduct.express,
            orderType: user.client.isB2BAccess ? ORDER_TYPES.B2B : ORDER_TYPES.B2C,
            isCod: helpers.isPaymentModeCOD(firstProduct?.paymentMode) 
        });

        if (costs.isServiceable !== true || costs.shippingRates.length === 0) {
            throw new Error(`No shipping rates available for package ${packageId}`);
        }

        allPackageRates.push({
            packageId: parseInt(packageId),
            rates: costs.shippingRates
        });
    }

    // Sum costs by provider
    for (const pkg of allPackageRates) {
        for (const rate of pkg.rates) {
            const providerName = rate.providerName;
            
            if (!providerTotals[providerName]) {
                providerTotals[providerName] = {
                    totalCost: 0,
                    totalCod: 0,
                    packageRates: []
                };
            }

            providerTotals[providerName].totalCost += parseFloat(rate.cost);
            providerTotals[providerName].totalCod += parseFloat(rate.codCharges || 0);
            providerTotals[providerName].packageRates.push({
                packageId: pkg.packageId,
                cost: parseFloat(rate.cost),
                codCharges: parseFloat(rate.codCharges || 0),
                shippingCharges: parseFloat(rate.shippingCharges),
                providerId: rate.providerId,
                providerName: rate.providerName
            });
        }
    }

    // Find best provider
    let bestProvider = null;
    let minTotalCost = Infinity;

    for (const [provider, data] of Object.entries(providerTotals)) {
        if (data.totalCost < minTotalCost) {
            minTotalCost = data.totalCost;
            bestProvider = provider;
        }
    }

    if (!bestProvider) {
        throw new Error("No valid shipping provider found for the order.");
    }

    return providerTotals[bestProvider];
}

function calculateOrderAmounts(products) {
    const amount = products.reduce((sum, product) => sum + product.value, 0);
    const gstAmount = products.reduce((sum, product) => sum + (product.value * (product.gst / 100)), 0);
    const totalAmount = amount + gstAmount;
    
    return {
        amount: parseFloat(amount).toFixed(2),
        gstAmount: parseFloat(gstAmount).toFixed(2),
        totalAmount: parseFloat(totalAmount).toFixed(2)
    };
}


function buildAddressDetails(products) {
    const firstProduct = products[0];
    return [
        {
            addressDetailType: "consigneeDetails",
            consigneeFirstName: firstProduct.consigneeFirstName,
            consigneeLastName: firstProduct.consigneeLastName,
            warehouseId: 0,
            contact: {
                consigneeContact: firstProduct.consigneeContact,
                alternateContact: firstProduct.alternateContact,
                email: firstProduct.consigneeEmail
            },
            address: {
                addressType: firstProduct.addressType,
                addressLine1: firstProduct.addressLine1,
                addressLine2: firstProduct.addressLine2,
                city: firstProduct.city,
                state: firstProduct.state,
                zip: firstProduct.zip,
                country: "India"
            }
        },
        {
            addressDetailType: "pickUpAddress",
            warehouseId: firstProduct.pickUpWarehouseId
        },
        {
            addressDetailType: "returnAddress",
            warehouseId: firstProduct.returnWarehouseId
        }
    ];
}

function groupProductsByPackage(products) {
    const packages = {};
    products.forEach(product => {
        const packageId = product.packageId || 1;
        if (!packages[packageId]) {
            packages[packageId] = [];
        }
        packages[packageId].push(product);
    });
    return packages;
}

function buildPackageDetails(packages) {
    return Object.entries(packages).map(([packageId, packageProducts]) => {
        const firstProduct = packageProducts[0];
        return {
            packageId: parseInt(packageId),
            packetDetails: [
                { type: "Weight", unitMeasurement: "KG", unit: firstProduct.packageWeight },
                { type: "DimensionsLength", unitMeasurement: "cms", unit: firstProduct.packageLength },
                { type: "DimensionsWidth", unitMeasurement: "cms", unit: firstProduct.packageWidth },
                { type: "DimensionsHeight", unitMeasurement: "cms", unit: firstProduct.packageHeight }
            ]
        };
    });
}

module.exports = BulkOrderService;



