JWT_KEY="kEnE)x6Te@p:u[_%NJ5?Dc=~t9\;Q,_3"

# server
PORT="3000"
IP="localhost"

# dataase
DB_HOST="127.0.0.1"
DB_PORT="3306"
DB_DATABASE="channelconnector"
DB_USER="sa"
#DB_PASSWORD="pTUg6rgK8rEkN8L"
DB_PASSWORD="Active@123"


ENV="DEV"
BASE_URL="http://localhost:3000/"
DECRYPTION_KEY="S&G]U#DJaC&cAJ'U:vzh+ey!Kh.2aSWw"

#FBY_URL
FBY_URL="https://stage.yocabe.com"
#FBY_URL="https://cloud.yocabe.com"

#STOREDEN_URL
STOREDEN_URL="https://connect.storeden.com"

# SMTP mailer
SMTP_USER=<EMAIL>
MAIL_TO=<EMAIL>
SMTP_PASSWORD=yourpassword
SMTP_HOST=smtp.1and1.com
SMTP_PORT=587

# Jobs Time
GET_SHOPIFY_PRODUCTS = 15,45 * * * *
GET_SHOPIFY_LOCATION = */25 * * * * *
SEND_PRODUCT_FBY = * * */25 * *

GET_FBY_STOCK = */45 * * * * *
PUSH_STOCK_SHOPIFY = * * */25 * *

ERROR_MANAGE =  * * */25 * *

FBY_APIS =  * * */25 * *

GET_SHOPIFY_ORDERS = 15,45 * * * *
SEND_CANCELLED_ORDERS = * * */25 * *
SEND_ORDER_FBY = * * */25 * *

GET_TRACK_NUMBER = */45 * * * * *
PUSH_TRACK_SHOPIFY = * * */25 * *
 
# Jobs Time Storeden
GET_STOREDEN_PRODUCTS = */20 * * * * *
GET_STOREDEN_ORDERS = */40 * * * * *
PUSH_STOCK_STOREDEN = * * */25 * *
PUSH_TRACK_STOREDEN = * * */25 * *

# Jobs Time Prestashop
GET_PRESTA_PRODUCTS = */20 * * * * *
PUSH_STOCK_PRESTA = * * */15 * *
GET_PRESTA_ORDERS =  */40 * * * * *
PUSH_TRACK_PRESTA = * * */25 * *

GET_WOOCOMMERCE_PRODUCTS = */20 * * * * *
PUSH_STOCK_WOOCOMMERCE = * * */15 * *
GET_WOOCOMMERCE_ORDERS = */40 * * * * *
PUSH_TRACK_WOOCOMMERCE = * * */25 * *

# Jobs Time Ebay
GET_EBAY_PRODUCTS = */20 * * * * *
PUSH_STOCK_EBAY = */40 * * * * *
GET_EBAY_ORDERS = */30 * * * * *
PUSH_TRACK_EBAY = */50 * * * * *

# Jobs Time Magento
GET_MAGENTO_PRODUCTS = * * */15 * *
PUSH_STOCK_MAGENTO = * * */15 * *
GET_MAGENTO_ORDERS = * * */15 * *
PUSH_TRACK_MAGENTO = * * */15 * *

# Jobs Time Mirakl
GET_MIRAKL_PRODUCTS = */14 * * * *
GET_MIRAKL_CARRIERS = */59  * * *
PUSH_STOCK_MIRAKL = */16 * * * *
GET_MIRAKL_ORDERS = 15,45 * * * *
PUSH_TRACK_MIRAKL = */17 * * * *

# Jobs Timers for Price and product sync from FBY to Shopify
GET_PRODUCTS_FROM_FBY_TIMER = * * */15 * *
GET_PRODUCTS_PRICE_FROM_FBY_TIMER = * * */15 * *
PUSH_PRODUCTS_TO_SHOPIFY_TIMER = * * */15 * *
PUSH_PRODUCTS_IMAGES_TO_SHOPIFY_TIMER = * * */15 * *
PUSH_PRODUCTS_VARIANTS_TO_SHOPIFY_TIMER = * * */15 * *
UPDATE_PRODUCTS_VARIANTS_TO_SHOPIFY_TIMER = */22 * * * *

# Jobs Time AMAZON
GET_AMAZON_PRODUCTS = 15 * * * *
PUSH_STOCK_AMAZON = */14 * * * *
GET_AMAZON_ORDERS =  15,45 * * * *
PUSH_TRACK_AMAZON = */16 * * * *

#Default Values
#Other Settings Values
DEFAULT_PRODUCT_LOCATION_ID = ***********

#SET TO 0 FOR PROD ENV
IS_SEND_PRODUCT_TO_FBY = 0

#SET TO 1 WHEN YOU WANT TO ENABLE SYNC TO CHANNEL AND FBY
IS_SEND_OTHER_DATA_TO_CHANNEL_AND_FBY = 0

IS_LOG_INTO_DB = 1

IS_MOCK = 1

IS_INFO_LOGGING = 0

AZURE_STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=yocabedocumentdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
AMAZON_ORDERS_CONTAINER = "amazonopenbridge"

MONGO_URL = ""