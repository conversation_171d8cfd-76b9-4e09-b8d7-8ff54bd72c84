DROP PROCEDURE IF EXISTS channelconnector.updateProductAftrSndChanl;

DELIMITER $$
CREATE PROCEDURE channelconnector.updateProductAftrSndChanl (
`in_fby_id` VARCHAR(128),
`in_sku` VARCHAR(128),
`in_crn_name` VARCHAR(60),
`in_crnid` VARCHAR(100),
`in_time` DATETIME

)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE products 
	SET 
		previous_inventory_quantity = inventory_quantity,
		count = 0,
		fby_error_flag = 0,
		cron_name = in_crn_name,
		updated_at = in_time,
		cron_id = in_crnid
	WHERE
		fby_user_id = in_fby_id 
        AND sku = in_sku;
        
	SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;