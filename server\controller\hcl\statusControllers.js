const StatusService = require('../../../services/hcl/statusService.js');
const helpers = require('../../../misc/helpers');
const OrderStatus = require('../../../misc/enums/orderStatusEnum');
const { throwError } = require('rxjs');


exports.getAllStatuses = async (req, res, next) => {
    try {

        const type = req.query.type || 'all';
        const groupBy = req.query.groupBy || 0;
        const result = await StatusService.getAllStatuses(type, groupBy);
        res.status(200).json({ data: result });
    } catch (error) {
        helpers.sendError(res, 500, 'SERVER_ERROR', error.message, req.query)
    }
    next();
};

exports.getStatesMaster = async (req, res, next) => {
    try {

        const type = req.query.type || 'all';
        const groupBy = 1;
        const result = await StatusService.getAllStatuses(type, groupBy);
        res.status(200).json({ data: result });
    } catch (error) {
        helpers.sendError(res, 500, 'SERVER_ERROR', error.message, req.query)
    }
    next();
};

exports.getStatusesMaster = async (req, res, next) => {
    try {

        const type = req.query.type || 'all';
        const result = await StatusService.statusesMaster(type);
        res.status(200).json({ data: result });
    } catch (error) {
        helpers.sendError(res, 500, 'SERVER_ERROR', error.message, req.query)
    }
    next();
};

exports.getDataMasters = async (req, res, next) => {
    try {
        const type = req.query.type || 'all';
        const result = await StatusService.getDataMasters(type);
        res.status(200).json({ data: result });
    } catch (error) {
        helpers.sendError(res, 500, 'SERVER_ERROR', error.message, req.query)
    }
    next();
};