JWT_KEY="kEnE)x6Te@p:u[_%NJ5?Dc=~t9\;Q,_3"

# server
PORT="5000"

IP="************"

# dataase
DB_HOST="yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com"
DB_PORT="3306"
DB_DATABASE="channelconnector"
DB_USER="yocabedbadmin"
DB_PASSWORD="k1TPrkU5AeFEejpL3G"

ENV="PROD"
# BASE_URL="http://channelsconnector.yocabe.com/"
BASE_URL="http://localhost:5000/"
DECRYPTION_KEY="S&G]U#DJaC&cAJ'U:vzh+ey!Kh.2aSWw"

#FBY_URL
FBY_URL="https://cloud.yocabe.com"

#STOREDEN_URL
STOREDEN_URL="https://connect.storeden.com"

# SMTP mailer
SMTP_USER=<EMAIL>
MAIL_TO=<EMAIL>
SMTP_PASSWORD=yourpassword
SMTP_HOST=smtp.1and1.com
SMTP_PORT=587

# Jobs Time
GET_SHOPIFY_PRODUCTS = */10 * * * *
GET_SHOPIFY_LOCATION = */11 * * * *
SEND_PRODUCT_FBY = */12 * * * * 

GET_FBY_STOCK = */13 * * * *
PUSH_STOCK_SHOPIFY = */14 * * * *

ERROR_MANAGE = 0 */5 * * *

FBY_APIS = 0 */5 * * * 

GET_SHOPIFY_ORDERS = */15 * * * *
SEND_CANCELLED_ORDERS = */16 * * * *
SEND_ORDER_FBY = */17 * * * * 

GET_TRACK_NUMBER = */18 * * * *
PUSH_TRACK_SHOPIFY = */19 * * * *

# Storeden Jobs Time
GET_STOREDEN_PRODUCTS = */20 * * * *
GET_STOREDEN_ORDERS =  */21 * * * *
PUSH_STOCK_STOREDEN =  */22 * * * *
PUSH_TRACK_STOREDEN = */23 * * * *


# Prestashop Jobs Time 
GET_PRESTA_PRODUCTS = */13 * * * *
PUSH_STOCK_PRESTA = */14 * * * *
GET_PRESTA_ORDERS =  */15 * * * *
PUSH_TRACK_PRESTA = */16 * * * *

GET_WOOCOMMERCE_PRODUCTS = */13 * * * *
PUSH_STOCK_WOOCOMMERCE = */14 * * * *
GET_WOOCOMMERCE_ORDERS =  */15 * * * *
PUSH_TRACK_WOOCOMMERCE = */16 * * * *

# Jobs Time Ebay
GET_EBAY_PRODUCTS = */20 * * * * *
PUSH_STOCK_EBAY = */40 * * * * *
GET_EBAY_ORDERS = */30 * * * * *
PUSH_TRACK_EBAY = */50 * * * * *

# Jobs Time Magento
GET_MAGENTO_PRODUCTS = * * */15 * *
PUSH_STOCK_MAGENTO = * * */15 * *
GET_MAGENTO_ORDERS = * * */15 * *
PUSH_TRACK_MAGENTO = * * */15 * *

# Jobs Time Mirakl
GET_MIRAKL_PRODUCTS = * * */15 * *
GET_MIRAKL_CARRIERS = */59  * * * *
PUSH_STOCK_MIRAKL = * * */15 * *
GET_MIRAKL_ORDERS = * * */15 * *
PUSH_TRACK_MIRAKL = * * */15 * *

# Jobs Timers for Price and product sync from FBY to Shopify
GET_PRODUCTS_FROM_FBY_TIMER = */10 * * * *
GET_PRODUCTS_PRICE_FROM_FBY_TIMER = */12 * * * *
PUSH_PRODUCTS_TO_SHOPIFY_TIMER = */15 * * * *
PUSH_PRODUCTS_IMAGES_TO_SHOPIFY_TIMER = */18 * * * *
PUSH_PRODUCTS_VARIANTS_TO_SHOPIFY_TIMER = */20 * * * *
UPDATE_PRODUCTS_VARIANTS_TO_SHOPIFY_TIMER = */22 * * * *

#Default Values
#Other Settings Values
DEFAULT_PRODUCT_LOCATION_ID = ***********

#SET TO 0 FOR PROD ENV
IS_SEND_PRODUCT_TO_FBY = 0

#SET TO 1 WHEN YOU WANT TO ENABLE SYNC TO CHANNEL AND FBY
IS_SEND_OTHER_DATA_TO_CHANNEL_AND_FBY = 1

IS_LOG_INTO_DB = 0

IS_MOCK = 0

IS_INFO_LOGGING = 0

AZURE_STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=yocabedocumentdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
AMAZON_ORDERS_CONTAINER = "amazonopenbridge"

MONGO_URL = ""