const dbpool = require('../../startup/db');
const hcldb = process.env.INITIAL_CATALOG || "hcl";
const miscConstants = require("../../misc/constants.js");
const cache = require('../../misc/cache.js');
const helpers = require('../../misc/helpers');

// Insert/Update Weight Slab
class ShippingProviderWeightsService {

    /**
     * Upserts (insert/update) a weight slab and refreshes the cache
     * @param {Object} data - Weight slab data
     * @param {number} data.id - Weight slab ID (null for new entries)
     * @param {number} data.shippingProviderId - Shiiping Provider ID
     * @param {string} data.mode - Shipping mode
     * @param {number} data.baseWeight - Base weight value
     * @param {number} data.additionalWeight - Additional weight value
     * @param {number} data.isActive - Active status (default: 1)
     * @param {number} userId - User performing the operation
     * @returns {Promise<Object>} Result of the upsert operation
     * @throws {ValidationError} If required fields are missing
     */
    static async upsertWeightSlab (data, userId) {   
        try {
           
            const {
                id = null,
                shippingProviderId,
                mode,
                orderType,
                baseWeight,
                additionalWeight,
                additionalWeightExtra,
                isActive = 1,
            } = data;

            // Execute upsert procedure
            const query = `${hcldb}.UpsertShippingProviderWeightSlab`;
            const [result] =  await dbpool.executeProcedure(query, [
                id,
                shippingProviderId,
                mode,
                orderType,
                baseWeight,
                additionalWeight || null,
                additionalWeightExtra || null,
                isActive,
                userId,
                userId
            ]);

            // Refresh cache after successful upsert
            await this.initializeWeightCache(shippingProviderId);         
            return result[0]; // Return the first row containing success message
        } catch (error) {
            console.error('Error in ShippingProviderWeightsService.UpsertShippingProviderWeightSlab:', {
                error: error.message,
                data,
                userId
            });
            throw error;
        }      
    }

    /**
     * Initializes or refreshes the weight slab cache
     * @param {number} shippingProviderId - Client ID
     * @param {string|null} mode - Shipping mode (null for all modes)
     * @returns {Promise<void>}
     */
    static async initializeWeightCache(shippingProviderId) {
        try {
            if (!shippingProviderId) {
                throw new Error('Shipping Provider Id is required');
            }

            const query = `${hcldb}.GetShippingProviderWeightSlabs`;
            const [weightSlabs] = await dbpool.executeProcedure(query, [shippingProviderId]);

            // Cache the results
            const cachePrefix = miscConstants.CACHE.SHIPPING_PROVIDER_WEIGHT.KEY;
            const allKey = `${cachePrefix}_provider_${shippingProviderId}_all`;
            const cacheTTL = miscConstants.CACHE.SHIPPING_PROVIDER_WEIGHT.TTL;
            
            cache.set(allKey, weightSlabs, cacheTTL);

            // Group and cache by mode
            const slabsByMode = weightSlabs.reduce((acc, weightSlab) => {
                if (!acc[weightSlab.mode]) {
                    acc[weightSlab.mode] = [];
                }
                acc[weightSlab.mode].push(weightSlab);
                return acc;
            }, {});

            Object.entries(slabsByMode).forEach(([mode, slabs]) => {
                const modeKey = `${cachePrefix}_provider_${shippingProviderId}_${mode}`;
                cache.set(modeKey, slabs, cacheTTL);
            });
 
        } catch (error) {
            console.error('Error in initialize weight Cache:', {
                shippingProviderId,
                error: error.message
            });
            throw error;
        }  
    }    

    /**
     * Retrieves weight slabs from cache or database
     * @param {number} shippingProviderId - Shipping Provider Id
     * @param {string|null} mode - Shipping mode (null for all modes)
     * @param {number} refresh - Force cache refresh flag (0: use cache, 1: refresh)
     * @returns {Promise<Array>} Weight slabs
     */
    static async getWeightSlabsByShippingProvider(shippingProviderId) {
        try {
            if (!shippingProviderId) {
                throw new Error('shippingProviderId is required');
            }
            const cacheKey = `${miscConstants.CACHE.SHIPPING_PROVIDER_WEIGHT.KEY}_provider_${shippingProviderId}_all`;
            let weightSlabs = await cache.get(cacheKey);
            
            if (!weightSlabs) {
                await this.initializeWeightCache(shippingProviderId);
                weightSlabs = await cache.get(cacheKey);
            }

            return weightSlabs;

        } catch (error) {
            console.error('Error in weightSlabService.getWeightSlabsByShippingProvider:', {
                shippingProviderId,
                error: error.message
            });
            throw error;
        }   
    }

    /**
     * 
     * @param {*} shippingProviderId 
     * @param {*} mode 
     * @returns 
     */
    static async getWeightSlabs(shippingProviderId, mode = null, orderType = null) {
        try {
            
            if (!shippingProviderId) {
                throw new Error('shippingProviderId is required');
            }
    
            const weightSlabs = await this.getWeightSlabsByShippingProvider(shippingProviderId);
            if (!Array.isArray(weightSlabs) || weightSlabs.length === 0) {
                throw new Error('No weight slabs found');
            }
    
            const filteredSlabs = weightSlabs.filter(slab => {
                return (
                    slab.isActive === 1
                    && (mode === null || slab.mode === mode) // Check mode only if it's provided
                    && (orderType === null || slab.orderType === orderType )
                );
            });

            return filteredSlabs;

        } catch (error) {
            console.error('Error in weightSlabService.getWeightSlabs:', {
                shippingProviderId,
                mode,
                error: error.message
            });
            throw error;
        }   
    }

    /**
     * Retrieves appropriate weight slab from cache based on weight criteria
     * @param {number} shippingProviderId - Shipping Provider Id
     * @param {string} mode - Shipping mode
     * @param {number} effectiveWeight - Effective weight to match
     * @returns {Promise<Object>} Matching weight slab
     * @throws {Error} If no suitable slab is found
     */
    static async getWeightSlabFromCache (shippingProviderId, mode, effectiveWeight) {
       try {
            if (!shippingProviderId || !mode || effectiveWeight === undefined) {
                throw new Error('ClientId, shippingProviderId, mode, and effectiveWeight are required');
            }
           
            const filteredSlabs = await this.getWeightSlabs(shippingProviderId, mode);         
           
            // Step 2: Sort by base_weight in descending order
            const sortedSlabs = filteredSlabs.sort((a, b) => b.baseWeight - a.baseWeight);
        
            // Step 3: Find the first slab where base_weight <= effective_weight
            const matchingSlab = sortedSlabs.find(slab => slab.baseWeight <= effectiveWeight);
        
            if (!matchingSlab) {
                throw new Error(`No suitable weight slab found for weight: ${effectiveWeight}`);
            }
        
            return matchingSlab; // Return the matched weight slab
        } catch(error) {
            console.error('Error in weightSlabService.getWeightSlabFromCache:', {
                mode,
                effectiveWeight,
                error: error.message
            });
            throw error;
        }
    }    
}

module.exports = ShippingProviderWeightsService;
