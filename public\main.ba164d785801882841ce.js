(window.webpackJsonp=window.webpackJsonp||[]).push([[1],{0:function(t,e,n){t.exports=n("zUnb")},zUnb:function(t,e,n){"use strict";function r(t){return"function"==typeof t}n.r(e);let s=!1;const i={Promise:void 0,set useDeprecatedSynchronousErrorHandling(t){if(t){const t=new Error;console.warn("DEPRECATED! RxJS was set to use deprecated synchronous error handling behavior by code at: \n"+t.stack)}else s&&console.log("RxJS: Back to a better error behavior. Thank you. <3");s=t},get useDeprecatedSynchronousErrorHandling(){return s}};function o(t){setTimeout(()=>{throw t},0)}const a={closed:!0,next(t){},error(t){if(i.useDeprecatedSynchronousErrorHandling)throw t;o(t)},complete(){}},l=(()=>Array.isArray||(t=>t&&"number"==typeof t.length))();function u(t){return null!==t&&"object"==typeof t}const c=(()=>{function t(t){return Error.call(this),this.message=t?`${t.length} errors occurred during unsubscription:\n${t.map((t,e)=>`${e+1}) ${t.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=t,this}return t.prototype=Object.create(Error.prototype),t})();let h=(()=>{class t{constructor(t){this.closed=!1,this._parentOrParents=null,this._subscriptions=null,t&&(this._ctorUnsubscribe=!0,this._unsubscribe=t)}unsubscribe(){let e;if(this.closed)return;let{_parentOrParents:n,_ctorUnsubscribe:s,_unsubscribe:i,_subscriptions:o}=this;if(this.closed=!0,this._parentOrParents=null,this._subscriptions=null,n instanceof t)n.remove(this);else if(null!==n)for(let t=0;t<n.length;++t)n[t].remove(this);if(r(i)){s&&(this._unsubscribe=void 0);try{i.call(this)}catch(a){e=a instanceof c?d(a.errors):[a]}}if(l(o)){let t=-1,n=o.length;for(;++t<n;){const n=o[t];if(u(n))try{n.unsubscribe()}catch(a){e=e||[],a instanceof c?e=e.concat(d(a.errors)):e.push(a)}}}if(e)throw new c(e)}add(e){let n=e;if(!e)return t.EMPTY;switch(typeof e){case"function":n=new t(e);case"object":if(n===this||n.closed||"function"!=typeof n.unsubscribe)return n;if(this.closed)return n.unsubscribe(),n;if(!(n instanceof t)){const e=n;n=new t,n._subscriptions=[e]}break;default:throw new Error("unrecognized teardown "+e+" added to Subscription.")}let{_parentOrParents:r}=n;if(null===r)n._parentOrParents=this;else if(r instanceof t){if(r===this)return n;n._parentOrParents=[r,this]}else{if(-1!==r.indexOf(this))return n;r.push(this)}const s=this._subscriptions;return null===s?this._subscriptions=[n]:s.push(n),n}remove(t){const e=this._subscriptions;if(e){const n=e.indexOf(t);-1!==n&&e.splice(n,1)}}}return t.EMPTY=function(t){return t.closed=!0,t}(new t),t})();function d(t){return t.reduce((t,e)=>t.concat(e instanceof c?e.errors:e),[])}const p=(()=>"function"==typeof Symbol?Symbol("rxSubscriber"):"@@rxSubscriber_"+Math.random())();class f extends h{constructor(t,e,n){switch(super(),this.syncErrorValue=null,this.syncErrorThrown=!1,this.syncErrorThrowable=!1,this.isStopped=!1,arguments.length){case 0:this.destination=a;break;case 1:if(!t){this.destination=a;break}if("object"==typeof t){t instanceof f?(this.syncErrorThrowable=t.syncErrorThrowable,this.destination=t,t.add(this)):(this.syncErrorThrowable=!0,this.destination=new g(this,t));break}default:this.syncErrorThrowable=!0,this.destination=new g(this,t,e,n)}}[p](){return this}static create(t,e,n){const r=new f(t,e,n);return r.syncErrorThrowable=!1,r}next(t){this.isStopped||this._next(t)}error(t){this.isStopped||(this.isStopped=!0,this._error(t))}complete(){this.isStopped||(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe())}_next(t){this.destination.next(t)}_error(t){this.destination.error(t),this.unsubscribe()}_complete(){this.destination.complete(),this.unsubscribe()}_unsubscribeAndRecycle(){const{_parentOrParents:t}=this;return this._parentOrParents=null,this.unsubscribe(),this.closed=!1,this.isStopped=!1,this._parentOrParents=t,this}}class g extends f{constructor(t,e,n,s){let i;super(),this._parentSubscriber=t;let o=this;r(e)?i=e:e&&(i=e.next,n=e.error,s=e.complete,e!==a&&(o=Object.create(e),r(o.unsubscribe)&&this.add(o.unsubscribe.bind(o)),o.unsubscribe=this.unsubscribe.bind(this))),this._context=o,this._next=i,this._error=n,this._complete=s}next(t){if(!this.isStopped&&this._next){const{_parentSubscriber:e}=this;i.useDeprecatedSynchronousErrorHandling&&e.syncErrorThrowable?this.__tryOrSetError(e,this._next,t)&&this.unsubscribe():this.__tryOrUnsub(this._next,t)}}error(t){if(!this.isStopped){const{_parentSubscriber:e}=this,{useDeprecatedSynchronousErrorHandling:n}=i;if(this._error)n&&e.syncErrorThrowable?(this.__tryOrSetError(e,this._error,t),this.unsubscribe()):(this.__tryOrUnsub(this._error,t),this.unsubscribe());else if(e.syncErrorThrowable)n?(e.syncErrorValue=t,e.syncErrorThrown=!0):o(t),this.unsubscribe();else{if(this.unsubscribe(),n)throw t;o(t)}}}complete(){if(!this.isStopped){const{_parentSubscriber:t}=this;if(this._complete){const e=()=>this._complete.call(this._context);i.useDeprecatedSynchronousErrorHandling&&t.syncErrorThrowable?(this.__tryOrSetError(t,e),this.unsubscribe()):(this.__tryOrUnsub(e),this.unsubscribe())}else this.unsubscribe()}}__tryOrUnsub(t,e){try{t.call(this._context,e)}catch(n){if(this.unsubscribe(),i.useDeprecatedSynchronousErrorHandling)throw n;o(n)}}__tryOrSetError(t,e,n){if(!i.useDeprecatedSynchronousErrorHandling)throw new Error("bad call");try{e.call(this._context,n)}catch(r){return i.useDeprecatedSynchronousErrorHandling?(t.syncErrorValue=r,t.syncErrorThrown=!0,!0):(o(r),!0)}return!1}_unsubscribe(){const{_parentSubscriber:t}=this;this._context=null,this._parentSubscriber=null,t.unsubscribe()}}const m=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")();function y(t){return t}let v=(()=>{class t{constructor(t){this._isScalar=!1,t&&(this._subscribe=t)}lift(e){const n=new t;return n.source=this,n.operator=e,n}subscribe(t,e,n){const{operator:r}=this,s=function(t,e,n){if(t){if(t instanceof f)return t;if(t[p])return t[p]()}return t||e||n?new f(t,e,n):new f(a)}(t,e,n);if(s.add(r?r.call(s,this.source):this.source||i.useDeprecatedSynchronousErrorHandling&&!s.syncErrorThrowable?this._subscribe(s):this._trySubscribe(s)),i.useDeprecatedSynchronousErrorHandling&&s.syncErrorThrowable&&(s.syncErrorThrowable=!1,s.syncErrorThrown))throw s.syncErrorValue;return s}_trySubscribe(t){try{return this._subscribe(t)}catch(e){i.useDeprecatedSynchronousErrorHandling&&(t.syncErrorThrown=!0,t.syncErrorValue=e),function(t){for(;t;){const{closed:e,destination:n,isStopped:r}=t;if(e||r)return!1;t=n&&n instanceof f?n:null}return!0}(t)?t.error(e):console.warn(e)}}forEach(t,e){return new(e=_(e))((e,n)=>{let r;r=this.subscribe(e=>{try{t(e)}catch(s){n(s),r&&r.unsubscribe()}},n,e)})}_subscribe(t){const{source:e}=this;return e&&e.subscribe(t)}[m](){return this}pipe(...t){return 0===t.length?this:(0===(e=t).length?y:1===e.length?e[0]:function(t){return e.reduce((t,e)=>e(t),t)})(this);var e}toPromise(t){return new(t=_(t))((t,e)=>{let n;this.subscribe(t=>n=t,t=>e(t),()=>t(n))})}}return t.create=e=>new t(e),t})();function _(t){if(t||(t=i.Promise||Promise),!t)throw new Error("no Promise impl found");return t}const w=(()=>{function t(){return Error.call(this),this.message="object unsubscribed",this.name="ObjectUnsubscribedError",this}return t.prototype=Object.create(Error.prototype),t})();class b extends h{constructor(t,e){super(),this.subject=t,this.subscriber=e,this.closed=!1}unsubscribe(){if(this.closed)return;this.closed=!0;const t=this.subject,e=t.observers;if(this.subject=null,!e||0===e.length||t.isStopped||t.closed)return;const n=e.indexOf(this.subscriber);-1!==n&&e.splice(n,1)}}class S extends f{constructor(t){super(t),this.destination=t}}let C=(()=>{class t extends v{constructor(){super(),this.observers=[],this.closed=!1,this.isStopped=!1,this.hasError=!1,this.thrownError=null}[p](){return new S(this)}lift(t){const e=new x(this,this);return e.operator=t,e}next(t){if(this.closed)throw new w;if(!this.isStopped){const{observers:e}=this,n=e.length,r=e.slice();for(let s=0;s<n;s++)r[s].next(t)}}error(t){if(this.closed)throw new w;this.hasError=!0,this.thrownError=t,this.isStopped=!0;const{observers:e}=this,n=e.length,r=e.slice();for(let s=0;s<n;s++)r[s].error(t);this.observers.length=0}complete(){if(this.closed)throw new w;this.isStopped=!0;const{observers:t}=this,e=t.length,n=t.slice();for(let r=0;r<e;r++)n[r].complete();this.observers.length=0}unsubscribe(){this.isStopped=!0,this.closed=!0,this.observers=null}_trySubscribe(t){if(this.closed)throw new w;return super._trySubscribe(t)}_subscribe(t){if(this.closed)throw new w;return this.hasError?(t.error(this.thrownError),h.EMPTY):this.isStopped?(t.complete(),h.EMPTY):(this.observers.push(t),new b(this,t))}asObservable(){const t=new v;return t.source=this,t}}return t.create=(t,e)=>new x(t,e),t})();class x extends C{constructor(t,e){super(),this.destination=t,this.source=e}next(t){const{destination:e}=this;e&&e.next&&e.next(t)}error(t){const{destination:e}=this;e&&e.error&&this.destination.error(t)}complete(){const{destination:t}=this;t&&t.complete&&this.destination.complete()}_subscribe(t){const{source:e}=this;return e?this.source.subscribe(t):h.EMPTY}}function E(t){return t&&"function"==typeof t.schedule}function T(t,e){return function(n){if("function"!=typeof t)throw new TypeError("argument is not a function. Are you looking for `mapTo()`?");return n.lift(new k(t,e))}}class k{constructor(t,e){this.project=t,this.thisArg=e}call(t,e){return e.subscribe(new R(t,this.project,this.thisArg))}}class R extends f{constructor(t,e,n){super(t),this.project=e,this.count=0,this.thisArg=n||this}_next(t){let e;try{e=this.project.call(this.thisArg,t,this.count++)}catch(n){return void this.destination.error(n)}this.destination.next(e)}}const A=t=>e=>{for(let n=0,r=t.length;n<r&&!e.closed;n++)e.next(t[n]);e.complete()};function O(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}const I=O(),P=t=>t&&"number"==typeof t.length&&"function"!=typeof t;function j(t){return!!t&&"function"!=typeof t.subscribe&&"function"==typeof t.then}const N=t=>{if(t&&"function"==typeof t[m])return r=t,t=>{const e=r[m]();if("function"!=typeof e.subscribe)throw new TypeError("Provided object does not correctly implement Symbol.observable");return e.subscribe(t)};if(P(t))return A(t);if(j(t))return n=t,t=>(n.then(e=>{t.closed||(t.next(e),t.complete())},e=>t.error(e)).then(null,o),t);if(t&&"function"==typeof t[I])return e=t,t=>{const n=e[I]();for(;;){let e;try{e=n.next()}catch(r){return t.error(r),t}if(e.done){t.complete();break}if(t.next(e.value),t.closed)break}return"function"==typeof n.return&&t.add(()=>{n.return&&n.return()}),t};{const e=u(t)?"an invalid object":`'${t}'`;throw new TypeError(`You provided ${e} where a stream was expected. You can provide an Observable, Promise, Array, or Iterable.`)}var e,n,r};function D(t,e){return new v(n=>{const r=new h;let s=0;return r.add(e.schedule(function(){s!==t.length?(n.next(t[s++]),n.closed||r.add(this.schedule())):n.complete()})),r})}function U(t,e){return e?function(t,e){if(null!=t){if(function(t){return t&&"function"==typeof t[m]}(t))return function(t,e){return new v(n=>{const r=new h;return r.add(e.schedule(()=>{const s=t[m]();r.add(s.subscribe({next(t){r.add(e.schedule(()=>n.next(t)))},error(t){r.add(e.schedule(()=>n.error(t)))},complete(){r.add(e.schedule(()=>n.complete()))}}))})),r})}(t,e);if(j(t))return function(t,e){return new v(n=>{const r=new h;return r.add(e.schedule(()=>t.then(t=>{r.add(e.schedule(()=>{n.next(t),r.add(e.schedule(()=>n.complete()))}))},t=>{r.add(e.schedule(()=>n.error(t)))}))),r})}(t,e);if(P(t))return D(t,e);if(function(t){return t&&"function"==typeof t[I]}(t)||"string"==typeof t)return function(t,e){if(!t)throw new Error("Iterable cannot be null");return new v(n=>{const r=new h;let s;return r.add(()=>{s&&"function"==typeof s.return&&s.return()}),r.add(e.schedule(()=>{s=t[I](),r.add(e.schedule(function(){if(n.closed)return;let t,e;try{const n=s.next();t=n.value,e=n.done}catch(r){return void n.error(r)}e?n.complete():(n.next(t),this.schedule())}))})),r})}(t,e)}throw new TypeError((null!==t&&typeof t||t)+" is not observable")}(t,e):t instanceof v?t:new v(N(t))}class H extends f{constructor(t){super(),this.parent=t}_next(t){this.parent.notifyNext(t)}_error(t){this.parent.notifyError(t),this.unsubscribe()}_complete(){this.parent.notifyComplete(),this.unsubscribe()}}class L extends f{notifyNext(t){this.destination.next(t)}notifyError(t){this.destination.error(t)}notifyComplete(){this.destination.complete()}}function F(t,e){if(e.closed)return;if(t instanceof v)return t.subscribe(e);let n;try{n=N(t)(e)}catch(r){e.error(r)}return n}function M(t,e,n=Number.POSITIVE_INFINITY){return"function"==typeof e?r=>r.pipe(M((n,r)=>U(t(n,r)).pipe(T((t,s)=>e(n,t,r,s))),n)):("number"==typeof e&&(n=e),e=>e.lift(new V(t,n)))}class V{constructor(t,e=Number.POSITIVE_INFINITY){this.project=t,this.concurrent=e}call(t,e){return e.subscribe(new $(t,this.project,this.concurrent))}}class $ extends L{constructor(t,e,n=Number.POSITIVE_INFINITY){super(t),this.project=e,this.concurrent=n,this.hasCompleted=!1,this.buffer=[],this.active=0,this.index=0}_next(t){this.active<this.concurrent?this._tryNext(t):this.buffer.push(t)}_tryNext(t){let e;const n=this.index++;try{e=this.project(t,n)}catch(r){return void this.destination.error(r)}this.active++,this._innerSub(e)}_innerSub(t){const e=new H(this),n=this.destination;n.add(e);const r=F(t,e);r!==e&&n.add(r)}_complete(){this.hasCompleted=!0,0===this.active&&0===this.buffer.length&&this.destination.complete(),this.unsubscribe()}notifyNext(t){this.destination.next(t)}notifyComplete(){const t=this.buffer;this.active--,t.length>0?this._next(t.shift()):0===this.active&&this.hasCompleted&&this.destination.complete()}}function z(t=Number.POSITIVE_INFINITY){return M(y,t)}function B(t,e){return e?D(t,e):new v(A(t))}function q(){return function(t){return t.lift(new G(t))}}class G{constructor(t){this.connectable=t}call(t,e){const{connectable:n}=this;n._refCount++;const r=new W(t,n),s=e.subscribe(r);return r.closed||(r.connection=n.connect()),s}}class W extends f{constructor(t,e){super(t),this.connectable=e}_unsubscribe(){const{connectable:t}=this;if(!t)return void(this.connection=null);this.connectable=null;const e=t._refCount;if(e<=0)return void(this.connection=null);if(t._refCount=e-1,e>1)return void(this.connection=null);const{connection:n}=this,r=t._connection;this.connection=null,!r||n&&r!==n||r.unsubscribe()}}class Z extends v{constructor(t,e){super(),this.source=t,this.subjectFactory=e,this._refCount=0,this._isComplete=!1}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){const t=this._subject;return t&&!t.isStopped||(this._subject=this.subjectFactory()),this._subject}connect(){let t=this._connection;return t||(this._isComplete=!1,t=this._connection=new h,t.add(this.source.subscribe(new K(this.getSubject(),this))),t.closed&&(this._connection=null,t=h.EMPTY)),t}refCount(){return q()(this)}}const Q=(()=>{const t=Z.prototype;return{operator:{value:null},_refCount:{value:0,writable:!0},_subject:{value:null,writable:!0},_connection:{value:null,writable:!0},_subscribe:{value:t._subscribe},_isComplete:{value:t._isComplete,writable:!0},getSubject:{value:t.getSubject},connect:{value:t.connect},refCount:{value:t.refCount}}})();class K extends S{constructor(t,e){super(t),this.connectable=e}_error(t){this._unsubscribe(),super._error(t)}_complete(){this.connectable._isComplete=!0,this._unsubscribe(),super._complete()}_unsubscribe(){const t=this.connectable;if(t){this.connectable=null;const e=t._connection;t._refCount=0,t._subject=null,t._connection=null,e&&e.unsubscribe()}}}function J(){return new C}function X(t){for(let e in t)if(t[e]===X)return e;throw Error("Could not find renamed property on target object.")}function Y(t){if("string"==typeof t)return t;if(Array.isArray(t))return"["+t.map(Y).join(", ")+"]";if(null==t)return""+t;if(t.overriddenName)return`${t.overriddenName}`;if(t.name)return`${t.name}`;const e=t.toString();if(null==e)return""+e;const n=e.indexOf("\n");return-1===n?e:e.substring(0,n)}function tt(t,e){return null==t||""===t?null===e?"":e:null==e||""===e?t:t+" "+e}const et=X({__forward_ref__:X});function nt(t){return t.__forward_ref__=nt,t.toString=function(){return Y(this())},t}function rt(t){return"function"==typeof(e=t)&&e.hasOwnProperty(et)&&e.__forward_ref__===nt?t():t;var e}class st extends Error{constructor(t,e){super(function(t,e){return`${t?`NG0${t}: `:""}${e}`}(t,e)),this.code=t}}function it(t){return"string"==typeof t?t:null==t?"":String(t)}function ot(t){return"function"==typeof t?t.name||t.toString():"object"==typeof t&&null!=t&&"function"==typeof t.type?t.type.name||t.type.toString():it(t)}function at(t,e){const n=e?` in ${e}`:"";throw new st("201",`No provider for ${ot(t)} found${n}`)}function lt(t){return{token:t.token,providedIn:t.providedIn||null,factory:t.factory,value:void 0}}function ut(t){return{providers:t.providers||[],imports:t.imports||[]}}function ct(t){return ht(t,pt)||ht(t,gt)}function ht(t,e){return t.hasOwnProperty(e)?t[e]:null}function dt(t){return t&&(t.hasOwnProperty(ft)||t.hasOwnProperty(mt))?t[ft]:null}const pt=X({"\u0275prov":X}),ft=X({"\u0275inj":X}),gt=X({ngInjectableDef:X}),mt=X({ngInjectorDef:X});var yt=function(t){return t[t.Default=0]="Default",t[t.Host=1]="Host",t[t.Self=2]="Self",t[t.SkipSelf=4]="SkipSelf",t[t.Optional=8]="Optional",t}({});let vt;function _t(t){const e=vt;return vt=t,e}function wt(t,e,n){const r=ct(t);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&yt.Optional?null:void 0!==e?e:void at(Y(t),"Injector")}function bt(t){return{toString:t}.toString()}var St=function(t){return t[t.OnPush=0]="OnPush",t[t.Default=1]="Default",t}({}),Ct=function(t){return t[t.Emulated=0]="Emulated",t[t.None=2]="None",t[t.ShadowDom=3]="ShadowDom",t}({});const xt="undefined"!=typeof globalThis&&globalThis,Et="undefined"!=typeof window&&window,Tt="undefined"!=typeof self&&"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&self,kt="undefined"!=typeof global&&global,Rt=xt||kt||Et||Tt,At={},Ot=[],It=X({"\u0275cmp":X}),Pt=X({"\u0275dir":X}),jt=X({"\u0275pipe":X}),Nt=X({"\u0275mod":X}),Dt=X({"\u0275loc":X}),Ut=X({"\u0275fac":X}),Ht=X({__NG_ELEMENT_ID__:X});let Lt=0;function Ft(t){return bt(()=>{const e={},n={type:t.type,providersResolver:null,decls:t.decls,vars:t.vars,factory:null,template:t.template||null,consts:t.consts||null,ngContentSelectors:t.ngContentSelectors,hostBindings:t.hostBindings||null,hostVars:t.hostVars||0,hostAttrs:t.hostAttrs||null,contentQueries:t.contentQueries||null,declaredInputs:e,inputs:null,outputs:null,exportAs:t.exportAs||null,onPush:t.changeDetection===St.OnPush,directiveDefs:null,pipeDefs:null,selectors:t.selectors||Ot,viewQuery:t.viewQuery||null,features:t.features||null,data:t.data||{},encapsulation:t.encapsulation||Ct.Emulated,id:"c",styles:t.styles||Ot,_:null,setInput:null,schemas:t.schemas||null,tView:null},r=t.directives,s=t.features,i=t.pipes;return n.id+=Lt++,n.inputs=Bt(t.inputs,e),n.outputs=Bt(t.outputs),s&&s.forEach(t=>t(n)),n.directiveDefs=r?()=>("function"==typeof r?r():r).map(Mt):null,n.pipeDefs=i?()=>("function"==typeof i?i():i).map(Vt):null,n})}function Mt(t){return Gt(t)||function(t){return t[Pt]||null}(t)}function Vt(t){return function(t){return t[jt]||null}(t)}const $t={};function zt(t){const e={type:t.type,bootstrap:t.bootstrap||Ot,declarations:t.declarations||Ot,imports:t.imports||Ot,exports:t.exports||Ot,transitiveCompileScopes:null,schemas:t.schemas||null,id:t.id||null};return null!=t.id&&bt(()=>{$t[t.id]=t.type}),e}function Bt(t,e){if(null==t)return At;const n={};for(const r in t)if(t.hasOwnProperty(r)){let s=t[r],i=s;Array.isArray(s)&&(i=s[1],s=s[0]),n[s]=r,e&&(e[s]=i)}return n}const qt=Ft;function Gt(t){return t[It]||null}function Wt(t,e){const n=t[Nt]||null;if(!n&&!0===e)throw new Error(`Type ${Y(t)} does not have '\u0275mod' property.`);return n}const Zt=20,Qt=10;function Kt(t){return Array.isArray(t)&&"object"==typeof t[1]}function Jt(t){return Array.isArray(t)&&!0===t[1]}function Xt(t){return 0!=(8&t.flags)}function Yt(t){return 2==(2&t.flags)}function te(t){return 1==(1&t.flags)}function ee(t){return null!==t.template}function ne(t,e){return t.hasOwnProperty(Ut)?t[Ut]:null}class re{constructor(t,e,n){this.previousValue=t,this.currentValue=e,this.firstChange=n}isFirstChange(){return this.firstChange}}function se(){const t=oe(this),e=null==t?void 0:t.current;if(e){const n=t.previous;if(n===At)t.previous=e;else for(let t in e)n[t]=e[t];t.current=null,this.ngOnChanges(e)}}function ie(t,e,n,r){const s=oe(t)||function(t,e){return t.__ngSimpleChanges__=e}(t,{previous:At,current:null}),i=s.current||(s.current={}),o=s.previous,a=this.declaredInputs[n],l=o[a];i[a]=new re(l&&l.currentValue,e,o===At),t[r]=e}function oe(t){return t.__ngSimpleChanges__||null}const ae="http://www.w3.org/2000/svg";let le;function ue(t){return!!t.listen}const ce={createRenderer:(t,e)=>void 0!==le?le:"undefined"!=typeof document?document:void 0};function he(t){for(;Array.isArray(t);)t=t[0];return t}function de(t,e){return he(e[t.index])}function pe(t,e){return t.data[e]}function fe(t,e){const n=e[t];return Kt(n)?n:n[0]}function ge(t){const e=function(t){return t.__ngContext__||null}(t);return e?Array.isArray(e)?e:e.lView:null}function me(t){return 128==(128&t[2])}function ye(t,e){return null==e?null:t[e]}function ve(t){t[18]=0}function _e(t,e){t[5]+=e;let n=t,r=t[3];for(;null!==r&&(1===e&&1===n[5]||-1===e&&0===n[5]);)r[5]+=e,n=r,r=r[3]}const we={lFrame:Le(null),bindingsEnabled:!0,isInCheckNoChangesMode:!1};function be(){return we.bindingsEnabled}function Se(){return we.lFrame.lView}function Ce(){return we.lFrame.tView}function xe(){let t=Ee();for(;null!==t&&64===t.type;)t=t.parent;return t}function Ee(){return we.lFrame.currentTNode}function Te(t,e){const n=we.lFrame;n.currentTNode=t,n.isParent=e}function ke(){return we.lFrame.isParent}function Re(){return we.isInCheckNoChangesMode}function Ae(t){we.isInCheckNoChangesMode=t}function Oe(){return we.lFrame.bindingIndex++}function Ie(t,e){const n=we.lFrame;n.bindingIndex=n.bindingRootIndex=t,Pe(e)}function Pe(t){we.lFrame.currentDirectiveIndex=t}function je(t){we.lFrame.currentQueryIndex=t}function Ne(t){const e=t[1];return 2===e.type?e.declTNode:1===e.type?t[6]:null}function De(t,e,n){if(n&yt.SkipSelf){let r=e,s=t;for(;r=r.parent,!(null!==r||n&yt.Host||(r=Ne(s),null===r)||(s=s[15],10&r.type)););if(null===r)return!1;e=r,t=s}const r=we.lFrame=He();return r.currentTNode=e,r.lView=t,!0}function Ue(t){const e=He(),n=t[1];we.lFrame=e,e.currentTNode=n.firstChild,e.lView=t,e.tView=n,e.contextLView=t,e.bindingIndex=n.bindingStartIndex,e.inI18n=!1}function He(){const t=we.lFrame,e=null===t?null:t.child;return null===e?Le(t):e}function Le(t){const e={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:t,child:null,inI18n:!1};return null!==t&&(t.child=e),e}function Fe(){const t=we.lFrame;return we.lFrame=t.parent,t.currentTNode=null,t.lView=null,t}const Me=Fe;function Ve(){const t=Fe();t.isParent=!0,t.tView=null,t.selectedIndex=-1,t.contextLView=null,t.elementDepthCount=0,t.currentDirectiveIndex=-1,t.currentNamespace=null,t.bindingRootIndex=-1,t.bindingIndex=-1,t.currentQueryIndex=0}function $e(){return we.lFrame.selectedIndex}function ze(t){we.lFrame.selectedIndex=t}function Be(t,e){for(let n=e.directiveStart,r=e.directiveEnd;n<r;n++){const e=t.data[n].type.prototype,{ngAfterContentInit:r,ngAfterContentChecked:s,ngAfterViewInit:i,ngAfterViewChecked:o,ngOnDestroy:a}=e;r&&(t.contentHooks||(t.contentHooks=[])).push(-n,r),s&&((t.contentHooks||(t.contentHooks=[])).push(n,s),(t.contentCheckHooks||(t.contentCheckHooks=[])).push(n,s)),i&&(t.viewHooks||(t.viewHooks=[])).push(-n,i),o&&((t.viewHooks||(t.viewHooks=[])).push(n,o),(t.viewCheckHooks||(t.viewCheckHooks=[])).push(n,o)),null!=a&&(t.destroyHooks||(t.destroyHooks=[])).push(n,a)}}function qe(t,e,n){Ze(t,e,3,n)}function Ge(t,e,n,r){(3&t[2])===n&&Ze(t,e,n,r)}function We(t,e){let n=t[2];(3&n)===e&&(n&=2047,n+=1,t[2]=n)}function Ze(t,e,n,r){const s=null!=r?r:-1,i=e.length-1;let o=0;for(let a=void 0!==r?65535&t[18]:0;a<i;a++)if("number"==typeof e[a+1]){if(o=e[a],null!=r&&o>=r)break}else e[a]<0&&(t[18]+=65536),(o<s||-1==s)&&(Qe(t,n,e,a),t[18]=(**********&t[18])+a+2),a++}function Qe(t,e,n,r){const s=n[r]<0,i=n[r+1],o=t[s?-n[r]:n[r]];if(s){if(t[2]>>11<t[18]>>16&&(3&t[2])===e){t[2]+=2048;try{i.call(o)}finally{}}}else try{i.call(o)}finally{}}const Ke=-1;class Je{constructor(t,e,n){this.factory=t,this.resolving=!1,this.canSeeViewProviders=e,this.injectImpl=n}}function Xe(t,e,n){const r=ue(t);let s=0;for(;s<n.length;){const i=n[s];if("number"==typeof i){if(0!==i)break;s++;const o=n[s++],a=n[s++],l=n[s++];r?t.setAttribute(e,a,l,o):e.setAttributeNS(o,a,l)}else{const o=i,a=n[++s];tn(o)?r&&t.setProperty(e,o,a):r?t.setAttribute(e,o,a):e.setAttribute(o,a),s++}}return s}function Ye(t){return 3===t||4===t||6===t}function tn(t){return 64===t.charCodeAt(0)}function en(t,e){if(null===e||0===e.length);else if(null===t||0===t.length)t=e.slice();else{let n=-1;for(let r=0;r<e.length;r++){const s=e[r];"number"==typeof s?n=s:0===n||nn(t,n,s,null,-1===n||2===n?e[++r]:null)}}return t}function nn(t,e,n,r,s){let i=0,o=t.length;if(-1===e)o=-1;else for(;i<t.length;){const n=t[i++];if("number"==typeof n){if(n===e){o=-1;break}if(n>e){o=i-1;break}}}for(;i<t.length;){const e=t[i];if("number"==typeof e)break;if(e===n){if(null===r)return void(null!==s&&(t[i+1]=s));if(r===t[i+1])return void(t[i+2]=s)}i++,null!==r&&i++,null!==s&&i++}-1!==o&&(t.splice(o,0,e),i=o+1),t.splice(i++,0,n),null!==r&&t.splice(i++,0,r),null!==s&&t.splice(i++,0,s)}function rn(t){return t!==Ke}function sn(t){return 32767&t}function on(t,e){let n=t>>16,r=e;for(;n>0;)r=r[15],n--;return r}let an=!0;function ln(t){const e=an;return an=t,e}let un=0;function cn(t,e){const n=dn(t,e);if(-1!==n)return n;const r=e[1];r.firstCreatePass&&(t.injectorIndex=e.length,hn(r.data,t),hn(e,null),hn(r.blueprint,null));const s=pn(t,e),i=t.injectorIndex;if(rn(s)){const t=sn(s),n=on(s,e),r=n[1].data;for(let s=0;s<8;s++)e[i+s]=n[t+s]|r[t+s]}return e[i+8]=s,i}function hn(t,e){t.push(0,0,0,0,0,0,0,0,e)}function dn(t,e){return-1===t.injectorIndex||t.parent&&t.parent.injectorIndex===t.injectorIndex||null===e[t.injectorIndex+8]?-1:t.injectorIndex}function pn(t,e){if(t.parent&&-1!==t.parent.injectorIndex)return t.parent.injectorIndex;let n=0,r=null,s=e;for(;null!==s;){const t=s[1],e=t.type;if(r=2===e?t.declTNode:1===e?s[6]:null,null===r)return Ke;if(n++,s=s[15],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return Ke}function fn(t,e,n){!function(t,e,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(Ht)&&(r=n[Ht]),null==r&&(r=n[Ht]=un++);const s=255&r;e.data[t+(s>>5)]|=1<<s}(t,e,n)}function gn(t,e,n){if(n&yt.Optional)return t;at(e,"NodeInjector")}function mn(t,e,n,r){if(n&yt.Optional&&void 0===r&&(r=null),0==(n&(yt.Self|yt.Host))){const s=t[9],i=_t(void 0);try{return s?s.get(e,r,n&yt.Optional):wt(e,r,n&yt.Optional)}finally{_t(i)}}return gn(r,e,n)}function yn(t,e,n,r=yt.Default,s){if(null!==t){const i=function(t){if("string"==typeof t)return t.charCodeAt(0)||0;const e=t.hasOwnProperty(Ht)?t[Ht]:void 0;return"number"==typeof e?e>=0?255&e:_n:e}(n);if("function"==typeof i){if(!De(e,t,r))return r&yt.Host?gn(s,n,r):mn(e,n,r,s);try{const t=i();if(null!=t||r&yt.Optional)return t;at(n)}finally{Me()}}else if("number"==typeof i){let s=null,o=dn(t,e),a=Ke,l=r&yt.Host?e[16][6]:null;for((-1===o||r&yt.SkipSelf)&&(a=-1===o?pn(t,e):e[o+8],a!==Ke&&Cn(r,!1)?(s=e[1],o=sn(a),e=on(a,e)):o=-1);-1!==o;){const t=e[1];if(Sn(i,o,t.data)){const t=wn(o,e,n,s,r,l);if(t!==vn)return t}a=e[o+8],a!==Ke&&Cn(r,e[1].data[o+8]===l)&&Sn(i,o,e)?(s=t,o=sn(a),e=on(a,e)):o=-1}}}return mn(e,n,r,s)}const vn={};function _n(){return new xn(xe(),Se())}function wn(t,e,n,r,s,i){const o=e[1],a=o.data[t+8],l=function(t,e,n,r,s){const i=t.providerIndexes,o=e.data,a=1048575&i,l=t.directiveStart,u=i>>20,c=s?a+u:t.directiveEnd;for(let h=r?a:a+u;h<c;h++){const t=o[h];if(h<l&&n===t||h>=l&&t.type===n)return h}if(s){const t=o[l];if(t&&ee(t)&&t.type===n)return l}return null}(a,o,n,null==r?Yt(a)&&an:r!=o&&0!=(3&a.type),s&yt.Host&&i===a);return null!==l?bn(e,o,l,a):vn}function bn(t,e,n,r){let s=t[n];const i=e.data;if(s instanceof Je){const o=s;o.resolving&&function(t,e){throw new st("200",`Circular dependency in DI detected for ${t}`)}(ot(i[n]));const a=ln(o.canSeeViewProviders);o.resolving=!0;const l=o.injectImpl?_t(o.injectImpl):null;De(t,r,yt.Default);try{s=t[n]=o.factory(void 0,i,t,r),e.firstCreatePass&&n>=r.directiveStart&&function(t,e,n){const{ngOnChanges:r,ngOnInit:s,ngDoCheck:i}=e.type.prototype;if(r){const r=((o=e).type.prototype.ngOnChanges&&(o.setInput=ie),se);(n.preOrderHooks||(n.preOrderHooks=[])).push(t,r),(n.preOrderCheckHooks||(n.preOrderCheckHooks=[])).push(t,r)}var o;s&&(n.preOrderHooks||(n.preOrderHooks=[])).push(0-t,s),i&&((n.preOrderHooks||(n.preOrderHooks=[])).push(t,i),(n.preOrderCheckHooks||(n.preOrderCheckHooks=[])).push(t,i))}(n,i[n],e)}finally{null!==l&&_t(l),ln(a),o.resolving=!1,Me()}}return s}function Sn(t,e,n){return!!(n[e+(t>>5)]&1<<t)}function Cn(t,e){return!(t&yt.Self||t&yt.Host&&e)}class xn{constructor(t,e){this._tNode=t,this._lView=e}get(t,e){return yn(this._tNode,this._lView,t,void 0,e)}}const En="__parameters__";function Tn(t,e,n){return bt(()=>{const r=function(t){return function(...e){if(t){const n=t(...e);for(const t in n)this[t]=n[t]}}}(e);function s(...t){if(this instanceof s)return r.apply(this,t),this;const e=new s(...t);return n.annotation=e,n;function n(t,n,r){const s=t.hasOwnProperty(En)?t[En]:Object.defineProperty(t,En,{value:[]})[En];for(;s.length<=r;)s.push(null);return(s[r]=s[r]||[]).push(e),t}}return n&&(s.prototype=Object.create(n.prototype)),s.prototype.ngMetadataName=t,s.annotationCls=s,s})}class kn{constructor(t,e){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof e?this.__NG_ELEMENT_ID__=e:void 0!==e&&(this.\u0275prov=lt({token:this,providedIn:e.providedIn||"root",factory:e.factory}))}toString(){return`InjectionToken ${this._desc}`}}const Rn=new kn("AnalyzeForEntryComponents"),An=Function;function On(t,e){t.forEach(t=>Array.isArray(t)?On(t,e):e(t))}function In(t,e,n){e>=t.length?t.push(n):t.splice(e,0,n)}function Pn(t,e){return e>=t.length-1?t.pop():t.splice(e,1)[0]}const jn={},Nn=/\n/gm,Dn="__source",Un=X({provide:String,useValue:X});let Hn;function Ln(t){const e=Hn;return Hn=t,e}function Fn(t,e=yt.Default){if(void 0===Hn)throw new Error("inject() must be called from an injection context");return null===Hn?wt(t,void 0,e):Hn.get(t,e&yt.Optional?null:void 0,e)}function Mn(t,e=yt.Default){return(vt||Fn)(rt(t),e)}function Vn(t){const e=[];for(let n=0;n<t.length;n++){const r=rt(t[n]);if(Array.isArray(r)){if(0===r.length)throw new Error("Arguments array must have arguments.");let t,n=yt.Default;for(let e=0;e<r.length;e++){const s=r[e],i=s.__NG_DI_FLAG__;"number"==typeof i?-1===i?t=s.token:n|=i:t=s}e.push(Mn(t,n))}else e.push(Mn(r))}return e}function $n(t,e){return t.__NG_DI_FLAG__=e,t.prototype.__NG_DI_FLAG__=e,t}const zn=$n(Tn("Inject",t=>({token:t})),-1),Bn=$n(Tn("Optional"),8),qn=$n(Tn("SkipSelf"),4);class Gn{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see https://g.co/ng/security#xss)`}}const Wn=/^(?:(?:https?|mailto|ftp|tel|file|sms):|[^&:/?#]*(?:[/?#]|$))/gi,Zn=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[a-z0-9+\/]+=*$/i;var Qn=function(t){return t[t.NONE=0]="NONE",t[t.HTML=1]="HTML",t[t.STYLE=2]="STYLE",t[t.SCRIPT=3]="SCRIPT",t[t.URL=4]="URL",t[t.RESOURCE_URL=5]="RESOURCE_URL",t}({});function Kn(t){const e=function(){const t=Se();return t&&t[12]}();return e?e.sanitize(Qn.URL,t)||"":function(t,e){const n=function(t){return t instanceof Gn&&t.getTypeName()||null}(t);if(null!=n&&n!==e){if("ResourceURL"===n&&"URL"===e)return!0;throw new Error(`Required a safe ${e}, got a ${n} (see https://g.co/ng/security#xss)`)}return n===e}(t,"URL")?(r=t)instanceof Gn?r.changingThisBreaksApplicationSecurity:r:(n=it(t),(n=String(n)).match(Wn)||n.match(Zn)?n:"unsafe:"+n);var n,r}function Jn(t){return t.ngDebugContext}function Xn(t){return t.ngOriginalError}function Yn(t,...e){t.error(...e)}class tr{constructor(){this._console=console}handleError(t){const e=this._findOriginalError(t),n=this._findContext(t),r=function(t){return t.ngErrorLogger||Yn}(t);r(this._console,"ERROR",t),e&&r(this._console,"ORIGINAL ERROR",e),n&&r(this._console,"ERROR CONTEXT",n)}_findContext(t){return t?Jn(t)?Jn(t):this._findContext(Xn(t)):null}_findOriginalError(t){let e=Xn(t);for(;e&&Xn(e);)e=Xn(e);return e}}function er(t,e){t.__ngContext__=e}const nr=(()=>("undefined"!=typeof requestAnimationFrame&&requestAnimationFrame||setTimeout).bind(Rt))();function rr(t){return t instanceof Function?t():t}var sr=function(t){return t[t.Important=1]="Important",t[t.DashCase=2]="DashCase",t}({});function ir(t,e){return(void 0)(t,e)}function or(t){const e=t[3];return Jt(e)?e[3]:e}function ar(t){return ur(t[13])}function lr(t){return ur(t[4])}function ur(t){for(;null!==t&&!Jt(t);)t=t[4];return t}function cr(t,e,n,r,s){if(null!=r){let i,o=!1;Jt(r)?i=r:Kt(r)&&(o=!0,r=r[0]);const a=he(r);0===t&&null!==n?null==s?yr(e,n,a):mr(e,n,a,s||null,!0):1===t&&null!==n?mr(e,n,a,s||null,!0):2===t?function(t,e,n){const r=_r(t,e);r&&function(t,e,n,r){ue(t)?t.removeChild(e,n,r):e.removeChild(n)}(t,r,e,n)}(e,a,o):3===t&&e.destroyNode(a),null!=i&&function(t,e,n,r,s){const i=n[7];i!==he(n)&&cr(e,t,r,i,s);for(let o=Qt;o<n.length;o++){const s=n[o];Er(s[1],s,t,e,r,i)}}(e,t,i,n,s)}}function hr(t,e,n){return ue(t)?t.createElement(e,n):null===n?t.createElement(e):t.createElementNS(n,e)}function dr(t,e){const n=t[9],r=n.indexOf(e),s=e[3];1024&e[2]&&(e[2]&=-1025,_e(s,-1)),n.splice(r,1)}function pr(t,e){if(t.length<=Qt)return;const n=Qt+e,r=t[n];if(r){const i=r[17];null!==i&&i!==t&&dr(i,r),e>0&&(t[n-1][4]=r[4]);const o=Pn(t,Qt+e);Er(r[1],s=r,s[11],2,null,null),s[0]=null,s[6]=null;const a=o[19];null!==a&&a.detachView(o[1]),r[3]=null,r[4]=null,r[2]&=-129}var s;return r}function fr(t,e){if(!(256&e[2])){const n=e[11];ue(n)&&n.destroyNode&&Er(t,e,n,3,null,null),function(t){let e=t[13];if(!e)return gr(t[1],t);for(;e;){let n=null;if(Kt(e))n=e[13];else{const t=e[10];t&&(n=t)}if(!n){for(;e&&!e[4]&&e!==t;)Kt(e)&&gr(e[1],e),e=e[3];null===e&&(e=t),Kt(e)&&gr(e[1],e),n=e&&e[4]}e=n}}(e)}}function gr(t,e){if(!(256&e[2])){e[2]&=-129,e[2]|=256,function(t,e){let n;if(null!=t&&null!=(n=t.destroyHooks))for(let r=0;r<n.length;r+=2){const t=e[n[r]];if(!(t instanceof Je)){const e=n[r+1];if(Array.isArray(e))for(let n=0;n<e.length;n+=2){const r=t[e[n]],s=e[n+1];try{s.call(r)}finally{}}else try{e.call(t)}finally{}}}}(t,e),function(t,e){const n=t.cleanup,r=e[7];let s=-1;if(null!==n)for(let i=0;i<n.length-1;i+=2)if("string"==typeof n[i]){const t=n[i+1],o="function"==typeof t?t(e):he(e[t]),a=r[s=n[i+2]],l=n[i+3];"boolean"==typeof l?o.removeEventListener(n[i],a,l):l>=0?r[s=l]():r[s=-l].unsubscribe(),i+=2}else{const t=r[s=n[i+1]];n[i].call(t)}if(null!==r){for(let t=s+1;t<r.length;t++)(0,r[t])();e[7]=null}}(t,e),1===e[1].type&&ue(e[11])&&e[11].destroy();const n=e[17];if(null!==n&&Jt(e[3])){n!==e[3]&&dr(n,e);const r=e[19];null!==r&&r.detachView(t)}}}function mr(t,e,n,r,s){ue(t)?t.insertBefore(e,n,r,s):e.insertBefore(n,r,s)}function yr(t,e,n){ue(t)?t.appendChild(e,n):e.appendChild(n)}function vr(t,e,n,r,s){null!==r?mr(t,e,n,r,s):yr(t,e,n)}function _r(t,e){return ue(t)?t.parentNode(e):e.parentNode}function wr(t,e,n,r){const s=function(t,e,n){return function(t,e,n){let r=e;for(;null!==r&&40&r.type;)r=(e=r).parent;if(null===r)return n[0];if(2&r.flags){const e=t.data[r.directiveStart].encapsulation;if(e===Ct.None||e===Ct.Emulated)return null}return de(r,n)}(t,e.parent,n)}(t,r,e),i=e[11],o=function(t,e,n){return function(t,e,n){return 40&t.type?de(t,n):null}(t,0,n)}(r.parent||e[6],0,e);if(null!=s)if(Array.isArray(n))for(let a=0;a<n.length;a++)vr(i,s,n[a],o,!1);else vr(i,s,n,o,!1)}function br(t,e){if(null!==e){const n=e.type;if(3&n)return de(e,t);if(4&n)return Cr(-1,t[e.index]);if(8&n){const n=e.child;if(null!==n)return br(t,n);{const n=t[e.index];return Jt(n)?Cr(-1,n):he(n)}}if(32&n)return ir(e,t)()||he(t[e.index]);{const n=Sr(t,e);return null!==n?Array.isArray(n)?n[0]:br(or(t[16]),n):br(t,e.next)}}return null}function Sr(t,e){return null!==e?t[16][6].projection[e.projection]:null}function Cr(t,e){const n=Qt+t+1;if(n<e.length){const t=e[n],r=t[1].firstChild;if(null!==r)return br(t,r)}return e[7]}function xr(t,e,n,r,s,i,o){for(;null!=n;){const a=r[n.index],l=n.type;if(o&&0===e&&(a&&er(he(a),r),n.flags|=4),64!=(64&n.flags))if(8&l)xr(t,e,n.child,r,s,i,!1),cr(e,t,s,a,i);else if(32&l){const o=ir(n,r);let l;for(;l=o();)cr(e,t,s,l,i);cr(e,t,s,a,i)}else 16&l?Tr(t,e,r,n,s,i):cr(e,t,s,a,i);n=o?n.projectionNext:n.next}}function Er(t,e,n,r,s,i){xr(n,r,t.firstChild,e,s,i,!1)}function Tr(t,e,n,r,s,i){const o=n[16],a=o[6].projection[r.projection];if(Array.isArray(a))for(let l=0;l<a.length;l++)cr(e,t,s,a[l],i);else xr(t,e,a,o[3],s,i,!0)}function kr(t,e,n){ue(t)?t.setAttribute(e,"style",n):e.style.cssText=n}function Rr(t,e,n){ue(t)?""===n?t.removeAttribute(e,"class"):t.setAttribute(e,"class",n):e.className=n}function Ar(t,e,n){let r=t.length;for(;;){const s=t.indexOf(e,n);if(-1===s)return s;if(0===s||t.charCodeAt(s-1)<=32){const n=e.length;if(s+n===r||t.charCodeAt(s+n)<=32)return s}n=s+1}}const Or="ng-template";function Ir(t,e,n){let r=0;for(;r<t.length;){let s=t[r++];if(n&&"class"===s){if(s=t[r],-1!==Ar(s.toLowerCase(),e,0))return!0}else if(1===s){for(;r<t.length&&"string"==typeof(s=t[r++]);)if(s.toLowerCase()===e)return!0;return!1}}return!1}function Pr(t){return 4===t.type&&t.value!==Or}function jr(t,e,n){return e===(4!==t.type||n?t.value:Or)}function Nr(t,e,n){let r=4;const s=t.attrs||[],i=function(t){for(let e=0;e<t.length;e++)if(Ye(t[e]))return e;return t.length}(s);let o=!1;for(let a=0;a<e.length;a++){const l=e[a];if("number"!=typeof l){if(!o)if(4&r){if(r=2|1&r,""!==l&&!jr(t,l,n)||""===l&&1===e.length){if(Dr(r))return!1;o=!0}}else{const u=8&r?l:e[++a];if(8&r&&null!==t.attrs){if(!Ir(t.attrs,u,n)){if(Dr(r))return!1;o=!0}continue}const c=Ur(8&r?"class":l,s,Pr(t),n);if(-1===c){if(Dr(r))return!1;o=!0;continue}if(""!==u){let t;t=c>i?"":s[c+1].toLowerCase();const e=8&r?t:null;if(e&&-1!==Ar(e,u,0)||2&r&&u!==t){if(Dr(r))return!1;o=!0}}}}else{if(!o&&!Dr(r)&&!Dr(l))return!1;if(o&&Dr(l))continue;o=!1,r=l|1&r}}return Dr(r)||o}function Dr(t){return 0==(1&t)}function Ur(t,e,n,r){if(null===e)return-1;let s=0;if(r||!n){let n=!1;for(;s<e.length;){const r=e[s];if(r===t)return s;if(3===r||6===r)n=!0;else{if(1===r||2===r){let t=e[++s];for(;"string"==typeof t;)t=e[++s];continue}if(4===r)break;if(0===r){s+=4;continue}}s+=n?1:2}return-1}return function(t,e){let n=t.indexOf(4);if(n>-1)for(n++;n<t.length;){const r=t[n];if("number"==typeof r)return-1;if(r===e)return n;n++}return-1}(e,t)}function Hr(t,e,n=!1){for(let r=0;r<e.length;r++)if(Nr(t,e[r],n))return!0;return!1}function Lr(t,e){return t?":not("+e.trim()+")":e}function Fr(t){let e=t[0],n=1,r=2,s="",i=!1;for(;n<t.length;){let o=t[n];if("string"==typeof o)if(2&r){const e=t[++n];s+="["+o+(e.length>0?'="'+e+'"':"")+"]"}else 8&r?s+="."+o:4&r&&(s+=" "+o);else""===s||Dr(o)||(e+=Lr(i,s),s=""),r=o,i=i||!Dr(r);n++}return""!==s&&(e+=Lr(i,s)),e}const Mr={};function Vr(t){$r(Ce(),Se(),$e()+t,Re())}function $r(t,e,n,r){if(!r)if(3==(3&e[2])){const r=t.preOrderCheckHooks;null!==r&&qe(e,r,n)}else{const r=t.preOrderHooks;null!==r&&Ge(e,r,0,n)}ze(n)}function zr(t,e){const n=t.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const s=n[r],i=n[r+1];if(-1!==i){const n=t.data[i];je(s),n.contentQueries(2,e[i],i)}}}function Br(t,e,n,r,s,i,o,a,l,u){const c=e.blueprint.slice();return c[0]=s,c[2]=140|r,ve(c),c[3]=c[15]=t,c[8]=n,c[10]=o||t&&t[10],c[11]=a||t&&t[11],c[12]=l||t&&t[12]||null,c[9]=u||t&&t[9]||null,c[6]=i,c[16]=2==e.type?t[16]:c,c}function qr(t,e,n,r,s){let i=t.data[e];if(null===i)i=function(t,e,n,r,s){const i=Ee(),o=ke(),a=t.data[e]=function(t,e,n,r,s,i){return{type:n,index:r,insertBeforeIndex:null,injectorIndex:e?e.injectorIndex:-1,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,propertyBindings:null,flags:0,providerIndexes:0,value:s,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tViews:null,next:null,projectionNext:null,child:null,parent:e,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,o?i:i&&i.parent,n,e,r,s);return null===t.firstChild&&(t.firstChild=a),null!==i&&(o?null==i.child&&null!==a.parent&&(i.child=a):null===i.next&&(i.next=a)),a}(t,e,n,r,s),we.lFrame.inI18n&&(i.flags|=64);else if(64&i.type){i.type=n,i.value=r,i.attrs=s;const t=function(){const t=we.lFrame,e=t.currentTNode;return t.isParent?e:e.parent}();i.injectorIndex=null===t?-1:t.injectorIndex}return Te(i,!0),i}function Gr(t,e,n,r){if(0===n)return-1;const s=e.length;for(let i=0;i<n;i++)e.push(r),t.blueprint.push(r),t.data.push(null);return s}function Wr(t,e,n){Ue(e);try{const r=t.viewQuery;null!==r&&ws(1,r,n);const s=t.template;null!==s&&Kr(t,e,s,1,n),t.firstCreatePass&&(t.firstCreatePass=!1),t.staticContentQueries&&zr(t,e),t.staticViewQueries&&ws(2,t.viewQuery,n);const i=t.components;null!==i&&function(t,e){for(let n=0;n<e.length;n++)gs(t,e[n])}(e,i)}catch(r){throw t.firstCreatePass&&(t.incompleteFirstPass=!0),r}finally{e[2]&=-5,Ve()}}function Zr(t,e,n,r){const s=e[2];if(256==(256&s))return;Ue(e);const i=Re();try{ve(e),we.lFrame.bindingIndex=t.bindingStartIndex,null!==n&&Kr(t,e,n,2,r);const o=3==(3&s);if(!i)if(o){const n=t.preOrderCheckHooks;null!==n&&qe(e,n,null)}else{const n=t.preOrderHooks;null!==n&&Ge(e,n,0,null),We(e,0)}if(function(t){for(let e=ar(t);null!==e;e=lr(e)){if(!e[2])continue;const t=e[9];for(let e=0;e<t.length;e++){const n=t[e],r=n[3];0==(1024&n[2])&&_e(r,1),n[2]|=1024}}}(e),function(t){for(let e=ar(t);null!==e;e=lr(e))for(let t=Qt;t<e.length;t++){const n=e[t],r=n[1];me(n)&&Zr(r,n,r.template,n[8])}}(e),null!==t.contentQueries&&zr(t,e),!i)if(o){const n=t.contentCheckHooks;null!==n&&qe(e,n)}else{const n=t.contentHooks;null!==n&&Ge(e,n,1),We(e,1)}!function(t,e){const n=t.hostBindingOpCodes;if(null!==n)try{for(let t=0;t<n.length;t++){const r=n[t];if(r<0)ze(~r);else{const s=r,i=n[++t],o=n[++t];Ie(i,s),o(2,e[s])}}}finally{ze(-1)}}(t,e);const a=t.components;null!==a&&function(t,e){for(let n=0;n<e.length;n++)ps(t,e[n])}(e,a);const l=t.viewQuery;if(null!==l&&ws(2,l,r),!i)if(o){const n=t.viewCheckHooks;null!==n&&qe(e,n)}else{const n=t.viewHooks;null!==n&&Ge(e,n,2),We(e,2)}!0===t.firstUpdatePass&&(t.firstUpdatePass=!1),i||(e[2]&=-73),1024&e[2]&&(e[2]&=-1025,_e(e[3],-1))}finally{Ve()}}function Qr(t,e,n,r){const s=e[10],i=!Re(),o=4==(4&e[2]);try{i&&!o&&s.begin&&s.begin(),o&&Wr(t,e,r),Zr(t,e,n,r)}finally{i&&!o&&s.end&&s.end()}}function Kr(t,e,n,r,s){const i=$e(),o=2&r;try{ze(-1),o&&e.length>Zt&&$r(t,e,Zt,Re()),n(r,s)}finally{ze(i)}}function Jr(t,e,n){be()&&(function(t,e,n,r){const s=n.directiveStart,i=n.directiveEnd;t.firstCreatePass||cn(n,e),er(r,e);const o=n.initialInputs;for(let a=s;a<i;a++){const r=t.data[a],i=ee(r);i&&us(e,n,r);const l=bn(e,t,a,n);er(l,e),null!==o&&cs(0,a-s,l,r,0,o),i&&(fe(n.index,e)[8]=l)}}(t,e,n,de(n,e)),128==(128&n.flags)&&function(t,e,n){const r=n.directiveStart,s=n.directiveEnd,i=n.index,o=we.lFrame.currentDirectiveIndex;try{ze(i);for(let n=r;n<s;n++){const r=t.data[n],s=e[n];Pe(n),null===r.hostBindings&&0===r.hostVars&&null===r.hostAttrs||ss(r,s)}}finally{ze(-1),Pe(o)}}(t,e,n))}function Xr(t,e,n=de){const r=e.localNames;if(null!==r){let s=e.index+1;for(let i=0;i<r.length;i+=2){const o=r[i+1],a=-1===o?n(e,t):t[o];t[s++]=a}}}function Yr(t){const e=t.tView;return null===e||e.incompleteFirstPass?t.tView=ts(1,null,t.template,t.decls,t.vars,t.directiveDefs,t.pipeDefs,t.viewQuery,t.schemas,t.consts):e}function ts(t,e,n,r,s,i,o,a,l,u){const c=Zt+r,h=c+s,d=function(t,e){const n=[];for(let r=0;r<e;r++)n.push(r<t?null:Mr);return n}(c,h),p="function"==typeof u?u():u;return d[1]={type:t,blueprint:d,template:n,queries:null,viewQuery:a,declTNode:e,data:d.slice().fill(null,c),bindingStartIndex:c,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof i?i():i,pipeRegistry:"function"==typeof o?o():o,firstChild:null,schemas:l,consts:p,incompleteFirstPass:!1}}function es(t,e,n){for(let r in t)if(t.hasOwnProperty(r)){const s=t[r];(n=null===n?{}:n).hasOwnProperty(r)?n[r].push(e,s):n[r]=[e,s]}return n}function ns(t,e,n,r){let s=!1;if(be()){const i=function(t,e,n){const r=t.directiveRegistry;let s=null;if(r)for(let i=0;i<r.length;i++){const o=r[i];Hr(n,o.selectors,!1)&&(s||(s=[]),fn(cn(n,e),t,o.type),ee(o)?(is(t,n),s.unshift(o)):s.push(o))}return s}(t,e,n),o=null===r?null:{"":-1};if(null!==i){s=!0,as(n,t.data.length,i.length);for(let t=0;t<i.length;t++){const e=i[t];e.providersResolver&&e.providersResolver(e)}let r=!1,a=!1,l=Gr(t,e,i.length,null);for(let s=0;s<i.length;s++){const u=i[s];n.mergedAttrs=en(n.mergedAttrs,u.hostAttrs),ls(t,n,e,l,u),os(l,u,o),null!==u.contentQueries&&(n.flags|=8),null===u.hostBindings&&null===u.hostAttrs&&0===u.hostVars||(n.flags|=128);const c=u.type.prototype;!r&&(c.ngOnChanges||c.ngOnInit||c.ngDoCheck)&&((t.preOrderHooks||(t.preOrderHooks=[])).push(n.index),r=!0),a||!c.ngOnChanges&&!c.ngDoCheck||((t.preOrderCheckHooks||(t.preOrderCheckHooks=[])).push(n.index),a=!0),l++}!function(t,e){const n=e.directiveEnd,r=t.data,s=e.attrs,i=[];let o=null,a=null;for(let l=e.directiveStart;l<n;l++){const t=r[l],n=t.inputs,u=null===s||Pr(e)?null:hs(n,s);i.push(u),o=es(n,l,o),a=es(t.outputs,l,a)}null!==o&&(o.hasOwnProperty("class")&&(e.flags|=16),o.hasOwnProperty("style")&&(e.flags|=32)),e.initialInputs=i,e.inputs=o,e.outputs=a}(t,n)}o&&function(t,e,n){if(e){const r=t.localNames=[];for(let t=0;t<e.length;t+=2){const s=n[e[t+1]];if(null==s)throw new st("301",`Export of name '${e[t+1]}' not found!`);r.push(e[t],s)}}}(n,r,o)}return n.mergedAttrs=en(n.mergedAttrs,n.attrs),s}function rs(t,e,n,r,s,i){const o=i.hostBindings;if(o){let n=t.hostBindingOpCodes;null===n&&(n=t.hostBindingOpCodes=[]);const i=~e.index;(function(t){let e=t.length;for(;e>0;){const n=t[--e];if("number"==typeof n&&n<0)return n}return 0})(n)!=i&&n.push(i),n.push(r,s,o)}}function ss(t,e){null!==t.hostBindings&&t.hostBindings(1,e)}function is(t,e){e.flags|=2,(t.components||(t.components=[])).push(e.index)}function os(t,e,n){if(n){if(e.exportAs)for(let r=0;r<e.exportAs.length;r++)n[e.exportAs[r]]=t;ee(e)&&(n[""]=t)}}function as(t,e,n){t.flags|=1,t.directiveStart=e,t.directiveEnd=e+n,t.providerIndexes=e}function ls(t,e,n,r,s){t.data[r]=s;const i=s.factory||(s.factory=ne(s.type)),o=new Je(i,ee(s),null);t.blueprint[r]=o,n[r]=o,rs(t,e,0,r,Gr(t,n,s.hostVars,Mr),s)}function us(t,e,n){const r=de(e,t),s=Yr(n),i=t[10],o=ms(t,Br(t,s,null,n.onPush?64:16,r,e,i,i.createRenderer(r,n),null,null));t[e.index]=o}function cs(t,e,n,r,s,i){const o=i[e];if(null!==o){const t=r.setInput;for(let e=0;e<o.length;){const s=o[e++],i=o[e++],a=o[e++];null!==t?r.setInput(n,a,s,i):n[i]=a}}}function hs(t,e){let n=null,r=0;for(;r<e.length;){const s=e[r];if(0!==s)if(5!==s){if("number"==typeof s)break;t.hasOwnProperty(s)&&(null===n&&(n=[]),n.push(s,t[s],e[r+1])),r+=2}else r+=2;else r+=4}return n}function ds(t,e,n,r){return new Array(t,!0,!1,e,null,0,r,n,null,null)}function ps(t,e){const n=fe(e,t);if(me(n)){const t=n[1];80&n[2]?Zr(t,n,t.template,n[8]):n[5]>0&&fs(n)}}function fs(t){for(let n=ar(t);null!==n;n=lr(n))for(let t=Qt;t<n.length;t++){const e=n[t];if(1024&e[2]){const t=e[1];Zr(t,e,t.template,e[8])}else e[5]>0&&fs(e)}const e=t[1].components;if(null!==e)for(let n=0;n<e.length;n++){const r=fe(e[n],t);me(r)&&r[5]>0&&fs(r)}}function gs(t,e){const n=fe(e,t),r=n[1];!function(t,e){for(let n=e.length;n<t.blueprint.length;n++)e.push(t.blueprint[n])}(r,n),Wr(r,n,n[8])}function ms(t,e){return t[13]?t[14][4]=e:t[13]=e,t[14]=e,e}function ys(t){for(;t;){t[2]|=64;const e=or(t);if(0!=(512&t[2])&&!e)return t;t=e}return null}function vs(t,e,n){const r=e[10];r.begin&&r.begin();try{Zr(t,e,t.template,n)}catch(s){throw xs(e,s),s}finally{r.end&&r.end()}}function _s(t){!function(t){for(let e=0;e<t.components.length;e++){const n=t.components[e],r=ge(n),s=r[1];Qr(s,r,s.template,n)}}(t[8])}function ws(t,e,n){je(0),e(t,n)}const bs=(()=>Promise.resolve(null))();function Ss(t){return t[7]||(t[7]=[])}function Cs(t){return t.cleanup||(t.cleanup=[])}function xs(t,e){const n=t[9],r=n?n.get(tr,null):null;r&&r.handleError(e)}function Es(t,e,n,r,s){for(let i=0;i<n.length;){const o=n[i++],a=n[i++],l=e[o],u=t.data[o];null!==u.setInput?u.setInput(l,s,r,a):l[a]=s}}function Ts(t,e,n){let r=n?t.styles:null,s=n?t.classes:null,i=0;if(null!==e)for(let o=0;o<e.length;o++){const t=e[o];"number"==typeof t?i=t:1==i?s=tt(s,t):2==i&&(r=tt(r,t+": "+e[++o]+";"))}n?t.styles=r:t.stylesWithoutHost=r,n?t.classes=s:t.classesWithoutHost=s}const ks=new kn("INJECTOR",-1);class Rs{get(t,e=jn){if(e===jn){const e=new Error(`NullInjectorError: No provider for ${Y(t)}!`);throw e.name="NullInjectorError",e}return e}}const As=new kn("Set Injector scope."),Os={},Is={},Ps=[];let js;function Ns(){return void 0===js&&(js=new Rs),js}function Ds(t,e=null,n=null,r){return new Us(t,n,e||Ns(),r)}class Us{constructor(t,e,n,r=null){this.parent=n,this.records=new Map,this.injectorDefTypes=new Set,this.onDestroy=new Set,this._destroyed=!1;const s=[];e&&On(e,n=>this.processProvider(n,t,e)),On([t],t=>this.processInjectorType(t,[],s)),this.records.set(ks,Ls(void 0,this));const i=this.records.get(As);this.scope=null!=i?i.value:null,this.source=r||("object"==typeof t?null:Y(t))}get destroyed(){return this._destroyed}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{this.onDestroy.forEach(t=>t.ngOnDestroy())}finally{this.records.clear(),this.onDestroy.clear(),this.injectorDefTypes.clear()}}get(t,e=jn,n=yt.Default){this.assertNotDestroyed();const r=Ln(this);try{if(!(n&yt.SkipSelf)){let e=this.records.get(t);if(void 0===e){const n=("function"==typeof(s=t)||"object"==typeof s&&s instanceof kn)&&ct(t);e=n&&this.injectableDefInScope(n)?Ls(Hs(t),Os):null,this.records.set(t,e)}if(null!=e)return this.hydrate(t,e)}return(n&yt.Self?Ns():this.parent).get(t,e=n&yt.Optional&&e===jn?null:e)}catch(i){if("NullInjectorError"===i.name){if((i.ngTempTokenPath=i.ngTempTokenPath||[]).unshift(Y(t)),r)throw i;return function(t,e,n,r){const s=t.ngTempTokenPath;throw e[Dn]&&s.unshift(e[Dn]),t.message=function(t,e,n,r=null){t=t&&"\n"===t.charAt(0)&&"\u0275"==t.charAt(1)?t.substr(2):t;let s=Y(e);if(Array.isArray(e))s=e.map(Y).join(" -> ");else if("object"==typeof e){let t=[];for(let n in e)if(e.hasOwnProperty(n)){let r=e[n];t.push(n+":"+("string"==typeof r?JSON.stringify(r):Y(r)))}s=`{${t.join(", ")}}`}return`${n}${r?"("+r+")":""}[${s}]: ${t.replace(Nn,"\n  ")}`}("\n"+t.message,s,n,r),t.ngTokenPath=s,t.ngTempTokenPath=null,t}(i,t,"R3InjectorError",this.source)}throw i}finally{Ln(r)}var s}_resolveInjectorDefTypes(){this.injectorDefTypes.forEach(t=>this.get(t))}toString(){const t=[];return this.records.forEach((e,n)=>t.push(Y(n))),`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new Error("Injector has already been destroyed.")}processInjectorType(t,e,n){if(!(t=rt(t)))return!1;let r=dt(t);const s=null==r&&t.ngModule||void 0,i=void 0===s?t:s,o=-1!==n.indexOf(i);if(void 0!==s&&(r=dt(s)),null==r)return!1;if(null!=r.imports&&!o){let t;n.push(i);try{On(r.imports,r=>{this.processInjectorType(r,e,n)&&(void 0===t&&(t=[]),t.push(r))})}finally{}if(void 0!==t)for(let e=0;e<t.length;e++){const{ngModule:n,providers:r}=t[e];On(r,t=>this.processProvider(t,n,r||Ps))}}this.injectorDefTypes.add(i);const a=ne(i)||(()=>new i);this.records.set(i,Ls(a,Os));const l=r.providers;if(null!=l&&!o){const e=t;On(l,t=>this.processProvider(t,e,l))}return void 0!==s&&void 0!==t.providers}processProvider(t,e,n){let r=Ms(t=rt(t))?t:rt(t&&t.provide);const s=function(t,e,n){return Fs(t)?Ls(void 0,t.useValue):Ls(function(t,e,n){let r;if(Ms(t)){const e=rt(t);return ne(e)||Hs(e)}if(Fs(t))r=()=>rt(t.useValue);else if((s=t)&&s.useFactory)r=()=>t.useFactory(...Vn(t.deps||[]));else if(function(t){return!(!t||!t.useExisting)}(t))r=()=>Mn(rt(t.useExisting));else{const e=rt(t&&(t.useClass||t.provide));if(!function(t){return!!t.deps}(t))return ne(e)||Hs(e);r=()=>new e(...Vn(t.deps))}var s;return r}(t),Os)}(t);if(Ms(t)||!0!==t.multi)this.records.get(r);else{let e=this.records.get(r);e||(e=Ls(void 0,Os,!0),e.factory=()=>Vn(e.multi),this.records.set(r,e)),r=t,e.multi.push(t)}this.records.set(r,s)}hydrate(t,e){var n;return e.value===Os&&(e.value=Is,e.value=e.factory()),"object"==typeof e.value&&e.value&&null!==(n=e.value)&&"object"==typeof n&&"function"==typeof n.ngOnDestroy&&this.onDestroy.add(e.value),e.value}injectableDefInScope(t){return!!t.providedIn&&("string"==typeof t.providedIn?"any"===t.providedIn||t.providedIn===this.scope:this.injectorDefTypes.has(t.providedIn))}}function Hs(t){const e=ct(t),n=null!==e?e.factory:ne(t);if(null!==n)return n;if(t instanceof kn)throw new Error(`Token ${Y(t)} is missing a \u0275prov definition.`);if(t instanceof Function)return function(t){const e=t.length;if(e>0){const n=function(t,e){const n=[];for(let r=0;r<t;r++)n.push("?");return n}(e);throw new Error(`Can't resolve all parameters for ${Y(t)}: (${n.join(", ")}).`)}const n=function(t){const e=t&&(t[pt]||t[gt]);if(e){const n=function(t){if(t.hasOwnProperty("name"))return t.name;const e=(""+t).match(/^function\s*([^\s(]+)/);return null===e?"":e[1]}(t);return console.warn(`DEPRECATED: DI is instantiating a token "${n}" that inherits its @Injectable decorator but does not provide one itself.\nThis will become an error in a future version of Angular. Please add @Injectable() to the "${n}" class.`),e}return null}(t);return null!==n?()=>n.factory(t):()=>new t}(t);throw new Error("unreachable")}function Ls(t,e,n=!1){return{factory:t,value:e,multi:n?[]:void 0}}function Fs(t){return null!==t&&"object"==typeof t&&Un in t}function Ms(t){return"function"==typeof t}const Vs=function(t,e,n){return function(t,e=null,n=null,r){const s=Ds(t,e,n,r);return s._resolveInjectorDefTypes(),s}({name:n},e,t,n)};let $s=(()=>{class t{static create(t,e){return Array.isArray(t)?Vs(t,e,""):Vs(t.providers,t.parent,t.name||"")}}return t.THROW_IF_NOT_FOUND=jn,t.NULL=new Rs,t.\u0275prov=lt({token:t,providedIn:"any",factory:()=>Mn(ks)}),t.__NG_ELEMENT_ID__=-1,t})();function zs(t,e){Be(ge(t)[1],xe())}let Bs=null;function qs(){if(!Bs){const t=Rt.Symbol;if(t&&t.iterator)Bs=t.iterator;else{const t=Object.getOwnPropertyNames(Map.prototype);for(let e=0;e<t.length;++e){const n=t[e];"entries"!==n&&"size"!==n&&Map.prototype[n]===Map.prototype.entries&&(Bs=n)}}}return Bs}function Gs(t){return!!Ws(t)&&(Array.isArray(t)||!(t instanceof Map)&&qs()in t)}function Ws(t){return null!==t&&("function"==typeof t||"object"==typeof t)}function Zs(t,e,n){return!Object.is(t[e],n)&&(t[e]=n,!0)}function Qs(t,e,n,r,s,i,o,a){const l=Se(),u=Ce(),c=t+Zt,h=u.firstCreatePass?function(t,e,n,r,s,i,o,a,l){const u=e.consts,c=qr(e,t,4,o||null,ye(u,a));ns(e,n,c,ye(u,l)),Be(e,c);const h=c.tViews=ts(2,c,r,s,i,e.directiveRegistry,e.pipeRegistry,null,e.schemas,u);return null!==e.queries&&(e.queries.template(e,c),h.queries=e.queries.embeddedTView(c)),c}(c,u,l,e,n,r,s,i,o):u.data[c];Te(h,!1);const d=l[11].createComment("");wr(u,l,d,h),er(d,l),ms(l,l[c]=ds(d,l,d,h)),te(h)&&Jr(u,l,h),null!=o&&Xr(l,h,a)}function Ks(t,e=yt.Default){const n=Se();return null===n?Mn(t,e):yn(xe(),n,rt(t),e)}function Js(t,e,n){const r=Se();return Zs(r,Oe(),e)&&function(t,e,n,r,s,i,o,a){const l=de(e,n);let u,c=e.inputs;var h;null!=c&&(u=c[r])?(Es(t,n,u,r,s),Yt(e)&&function(t,e){const n=fe(e,t);16&n[2]||(n[2]|=64)}(n,e.index)):3&e.type&&(r="class"===(h=r)?"className":"for"===h?"htmlFor":"formaction"===h?"formAction":"innerHtml"===h?"innerHTML":"readonly"===h?"readOnly":"tabindex"===h?"tabIndex":h,s=null!=o?o(s,e.value||"",r):s,ue(i)?i.setProperty(l,r,s):tn(r)||(l.setProperty?l.setProperty(r,s):l[r]=s))}(Ce(),function(){const t=we.lFrame;return pe(t.tView,t.selectedIndex)}(),r,t,e,r[11],n),Js}function Xs(t,e,n,r,s){const i=s?"class":"style";Es(t,n,e.inputs[i],i,r)}function Ys(t,e,n,r){const s=Se(),i=Ce(),o=Zt+t,a=s[11],l=s[o]=hr(a,e,we.lFrame.currentNamespace),u=i.firstCreatePass?function(t,e,n,r,s,i,o){const a=e.consts,l=qr(e,t,2,s,ye(a,i));return ns(e,n,l,ye(a,o)),null!==l.attrs&&Ts(l,l.attrs,!1),null!==l.mergedAttrs&&Ts(l,l.mergedAttrs,!0),null!==e.queries&&e.queries.elementStart(e,l),l}(o,i,s,0,e,n,r):i.data[o];Te(u,!0);const c=u.mergedAttrs;null!==c&&Xe(a,l,c);const h=u.classes;null!==h&&Rr(a,l,h);const d=u.styles;null!==d&&kr(a,l,d),64!=(64&u.flags)&&wr(i,s,l,u),0===we.lFrame.elementDepthCount&&er(l,s),we.lFrame.elementDepthCount++,te(u)&&(Jr(i,s,u),function(t,e,n){if(Xt(e)){const r=e.directiveEnd;for(let s=e.directiveStart;s<r;s++){const e=t.data[s];e.contentQueries&&e.contentQueries(1,n[s],s)}}}(i,u,s)),null!==r&&Xr(s,u)}function ti(){let t=xe();ke()?we.lFrame.isParent=!1:(t=t.parent,Te(t,!1));const e=t;we.lFrame.elementDepthCount--;const n=Ce();n.firstCreatePass&&(Be(n,t),Xt(t)&&n.queries.elementEnd(t)),null!=e.classesWithoutHost&&function(t){return 0!=(16&t.flags)}(e)&&Xs(n,e,Se(),e.classesWithoutHost,!0),null!=e.stylesWithoutHost&&function(t){return 0!=(32&t.flags)}(e)&&Xs(n,e,Se(),e.stylesWithoutHost,!1)}function ei(t,e,n,r){Ys(t,e,n,r),ti()}function ni(t){return!!t&&"function"==typeof t.then}function ri(t,e,n=!1,r){const s=Se(),i=Ce(),o=xe();return function(t,e,n,r,s,i,o=!1,a){const l=te(r),u=t.firstCreatePass&&Cs(t),c=Ss(e);let h=!0;if(3&r.type){const d=de(r,e),p=a?a(d):At,f=p.target||d,g=c.length,m=a?t=>a(he(t[r.index])).target:r.index;if(ue(n)){let o=null;if(!a&&l&&(o=function(t,e,n,r){const s=t.cleanup;if(null!=s)for(let i=0;i<s.length-1;i+=2){const t=s[i];if(t===n&&s[i+1]===r){const t=e[7],n=s[i+2];return t.length>n?t[n]:null}"string"==typeof t&&(i+=2)}return null}(t,e,s,r.index)),null!==o)(o.__ngLastListenerFn__||o).__ngNextListenerFn__=i,o.__ngLastListenerFn__=i,h=!1;else{i=ii(r,e,0,i,!1);const t=n.listen(p.name||f,s,i);c.push(i,t),u&&u.push(s,m,g,g+1)}}else i=ii(r,e,0,i,!0),f.addEventListener(s,i,o),c.push(i),u&&u.push(s,m,g,o)}else i=ii(r,e,0,i,!1);const d=r.outputs;let p;if(h&&null!==d&&(p=d[s])){const t=p.length;if(t)for(let n=0;n<t;n+=2){const t=e[p[n]][p[n+1]].subscribe(i),o=c.length;c.push(i,t),u&&u.push(s,r.index,o,-(o+1))}}}(i,s,s[11],o,t,e,n,r),ri}function si(t,e,n,r){try{return!1!==n(r)}catch(s){return xs(t,s),!1}}function ii(t,e,n,r,s){return function n(i){if(i===Function)return r;const o=2&t.flags?fe(t.index,e):e;0==(32&e[2])&&ys(o);let a=si(e,0,r,i),l=n.__ngNextListenerFn__;for(;l;)a=si(e,0,l,i)&&a,l=l.__ngNextListenerFn__;return s&&!1===a&&(i.preventDefault(),i.returnValue=!1),a}}function oi(t=1){return function(t){return(we.lFrame.contextLView=function(t,e){for(;t>0;)e=e[15],t--;return e}(t,we.lFrame.contextLView))[8]}(t)}function ai(t,e=""){const n=Se(),r=Ce(),s=t+Zt,i=r.firstCreatePass?qr(r,s,1,e,null):r.data[s],o=n[s]=function(t,e){return ue(t)?t.createText(e):t.createTextNode(e)}(n[11],e);wr(r,n,o,i),Te(i,!1)}function li(t){return ui("",t,""),li}function ui(t,e,n){const r=Se(),s=function(t,e,n,r){return Zs(t,Oe(),n)?e+it(n)+r:Mr}(r,t,e,n);return s!==Mr&&function(t,e,n){const r=function(t,e){return he(e[t])}(e,t);!function(t,e,n){ue(t)?t.setValue(e,n):e.textContent=n}(t[11],r,n)}(r,$e(),s),ui}const ci=void 0;var hi=["en",[["a","p"],["AM","PM"],ci],[["AM","PM"],ci,ci],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],ci,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],ci,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",ci,"{1} 'at' {0}",ci],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function(t){let e=Math.floor(Math.abs(t)),n=t.toString().replace(/^[^.]*\.?/,"").length;return 1===e&&0===n?1:5}];let di={};function pi(t){return t in di||(di[t]=Rt.ng&&Rt.ng.common&&Rt.ng.common.locales&&Rt.ng.common.locales[t]),di[t]}var fi=function(t){return t[t.LocaleId=0]="LocaleId",t[t.DayPeriodsFormat=1]="DayPeriodsFormat",t[t.DayPeriodsStandalone=2]="DayPeriodsStandalone",t[t.DaysFormat=3]="DaysFormat",t[t.DaysStandalone=4]="DaysStandalone",t[t.MonthsFormat=5]="MonthsFormat",t[t.MonthsStandalone=6]="MonthsStandalone",t[t.Eras=7]="Eras",t[t.FirstDayOfWeek=8]="FirstDayOfWeek",t[t.WeekendRange=9]="WeekendRange",t[t.DateFormat=10]="DateFormat",t[t.TimeFormat=11]="TimeFormat",t[t.DateTimeFormat=12]="DateTimeFormat",t[t.NumberSymbols=13]="NumberSymbols",t[t.NumberFormats=14]="NumberFormats",t[t.CurrencyCode=15]="CurrencyCode",t[t.CurrencySymbol=16]="CurrencySymbol",t[t.CurrencyName=17]="CurrencyName",t[t.Currencies=18]="Currencies",t[t.Directionality=19]="Directionality",t[t.PluralCase=20]="PluralCase",t[t.ExtraData=21]="ExtraData",t}({});const gi="en-US";let mi=gi;function yi(t){var e,n;n="Expected localeId to be defined",null==(e=t)&&function(t,e,n,r){throw new Error(`ASSERTION ERROR: ${t} [Expected=> null != ${e} <=Actual]`)}(n,e),"string"==typeof t&&(mi=t.toLowerCase().replace(/_/g,"-"))}class vi{}class _i{resolveComponentFactory(t){throw function(t){const e=Error(`No component factory found for ${Y(t)}. Did you add it to @NgModule.entryComponents?`);return e.ngComponent=t,e}(t)}}let wi=(()=>{class t{}return t.NULL=new _i,t})();function bi(...t){}function Si(t,e){return new xi(de(t,e))}const Ci=function(){return Si(xe(),Se())};let xi=(()=>{class t{constructor(t){this.nativeElement=t}}return t.__NG_ELEMENT_ID__=Ci,t})();class Ei{}let Ti=(()=>{class t{}return t.__NG_ELEMENT_ID__=()=>ki(),t})();const ki=function(){const t=Se(),e=fe(xe().index,t);return function(t){return t[11]}(Kt(e)?e:t)};let Ri=(()=>{class t{}return t.\u0275prov=lt({token:t,providedIn:"root",factory:()=>null}),t})();class Ai{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const Oi=new Ai("11.2.14");class Ii{constructor(){}supports(t){return Gs(t)}create(t){return new ji(t)}}const Pi=(t,e)=>e;class ji{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||Pi}forEachItem(t){let e;for(e=this._itHead;null!==e;e=e._next)t(e)}forEachOperation(t){let e=this._itHead,n=this._removalsHead,r=0,s=null;for(;e||n;){const i=!n||e&&e.currentIndex<Hi(n,r,s)?e:n,o=Hi(i,r,s),a=i.currentIndex;if(i===n)r--,n=n._nextRemoved;else if(e=e._next,null==i.previousIndex)r++;else{s||(s=[]);const t=o-r,e=a-r;if(t!=e){for(let n=0;n<t;n++){const r=n<s.length?s[n]:s[n]=0,i=r+n;e<=i&&i<t&&(s[n]=r+1)}s[i.previousIndex]=e-t}}o!==a&&t(i,o,a)}}forEachPreviousItem(t){let e;for(e=this._previousItHead;null!==e;e=e._nextPrevious)t(e)}forEachAddedItem(t){let e;for(e=this._additionsHead;null!==e;e=e._nextAdded)t(e)}forEachMovedItem(t){let e;for(e=this._movesHead;null!==e;e=e._nextMoved)t(e)}forEachRemovedItem(t){let e;for(e=this._removalsHead;null!==e;e=e._nextRemoved)t(e)}forEachIdentityChange(t){let e;for(e=this._identityChangesHead;null!==e;e=e._nextIdentityChange)t(e)}diff(t){if(null==t&&(t=[]),!Gs(t))throw new Error(`Error trying to diff '${Y(t)}'. Only arrays and iterables are allowed`);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let e,n,r,s=this._itHead,i=!1;if(Array.isArray(t)){this.length=t.length;for(let e=0;e<this.length;e++)n=t[e],r=this._trackByFn(e,n),null!==s&&Object.is(s.trackById,r)?(i&&(s=this._verifyReinsertion(s,n,r,e)),Object.is(s.item,n)||this._addIdentityChange(s,n)):(s=this._mismatch(s,n,r,e),i=!0),s=s._next}else e=0,function(t,e){if(Array.isArray(t))for(let n=0;n<t.length;n++)e(t[n]);else{const n=t[qs()]();let r;for(;!(r=n.next()).done;)e(r.value)}}(t,t=>{r=this._trackByFn(e,t),null!==s&&Object.is(s.trackById,r)?(i&&(s=this._verifyReinsertion(s,t,r,e)),Object.is(s.item,t)||this._addIdentityChange(s,t)):(s=this._mismatch(s,t,r,e),i=!0),s=s._next,e++}),this.length=e;return this._truncate(s),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,e,n,r){let s;return null===t?s=this._itTail:(s=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(n,null))?(Object.is(t.item,e)||this._addIdentityChange(t,e),this._reinsertAfter(t,s,r)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(n,r))?(Object.is(t.item,e)||this._addIdentityChange(t,e),this._moveAfter(t,s,r)):t=this._addAfter(new Ni(e,n),s,r),t}_verifyReinsertion(t,e,n,r){let s=null===this._unlinkedRecords?null:this._unlinkedRecords.get(n,null);return null!==s?t=this._reinsertAfter(s,t._prev,r):t.currentIndex!=r&&(t.currentIndex=r,this._addToMoves(t,r)),t}_truncate(t){for(;null!==t;){const e=t._next;this._addToRemovals(this._unlink(t)),t=e}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,e,n){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const r=t._prevRemoved,s=t._nextRemoved;return null===r?this._removalsHead=s:r._nextRemoved=s,null===s?this._removalsTail=r:s._prevRemoved=r,this._insertAfter(t,e,n),this._addToMoves(t,n),t}_moveAfter(t,e,n){return this._unlink(t),this._insertAfter(t,e,n),this._addToMoves(t,n),t}_addAfter(t,e,n){return this._insertAfter(t,e,n),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,e,n){const r=null===e?this._itHead:e._next;return t._next=r,t._prev=e,null===r?this._itTail=t:r._prev=t,null===e?this._itHead=t:e._next=t,null===this._linkedRecords&&(this._linkedRecords=new Ui),this._linkedRecords.put(t),t.currentIndex=n,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const e=t._prev,n=t._next;return null===e?this._itHead=n:e._next=n,null===n?this._itTail=e:n._prev=e,t}_addToMoves(t,e){return t.previousIndex===e||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new Ui),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,e){return t.item=e,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class Ni{constructor(t,e){this.item=t,this.trackById=e,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class Di{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,e){let n;for(n=this._head;null!==n;n=n._nextDup)if((null===e||e<=n.currentIndex)&&Object.is(n.trackById,t))return n;return null}remove(t){const e=t._prevDup,n=t._nextDup;return null===e?this._head=n:e._nextDup=n,null===n?this._tail=e:n._prevDup=e,null===this._head}}class Ui{constructor(){this.map=new Map}put(t){const e=t.trackById;let n=this.map.get(e);n||(n=new Di,this.map.set(e,n)),n.add(t)}get(t,e){const n=this.map.get(t);return n?n.get(t,e):null}remove(t){const e=t.trackById;return this.map.get(e).remove(t)&&this.map.delete(e),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function Hi(t,e,n){const r=t.previousIndex;if(null===r)return r;let s=0;return n&&r<n.length&&(s=n[r]),r+e+s}class Li{constructor(){}supports(t){return t instanceof Map||Ws(t)}create(){return new Fi}}class Fi{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let e;for(e=this._mapHead;null!==e;e=e._next)t(e)}forEachPreviousItem(t){let e;for(e=this._previousMapHead;null!==e;e=e._nextPrevious)t(e)}forEachChangedItem(t){let e;for(e=this._changesHead;null!==e;e=e._nextChanged)t(e)}forEachAddedItem(t){let e;for(e=this._additionsHead;null!==e;e=e._nextAdded)t(e)}forEachRemovedItem(t){let e;for(e=this._removalsHead;null!==e;e=e._nextRemoved)t(e)}diff(t){if(t){if(!(t instanceof Map||Ws(t)))throw new Error(`Error trying to diff '${Y(t)}'. Only maps and objects are allowed`)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let e=this._mapHead;if(this._appendAfter=null,this._forEach(t,(t,n)=>{if(e&&e.key===n)this._maybeAddToChanges(e,t),this._appendAfter=e,e=e._next;else{const r=this._getOrCreateRecordForKey(n,t);e=this._insertBeforeOrAppend(e,r)}}),e){e._prev&&(e._prev._next=null),this._removalsHead=e;for(let t=e;null!==t;t=t._nextRemoved)t===this._mapHead&&(this._mapHead=null),this._records.delete(t.key),t._nextRemoved=t._next,t.previousValue=t.currentValue,t.currentValue=null,t._prev=null,t._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,e){if(t){const n=t._prev;return e._next=t,e._prev=n,t._prev=e,n&&(n._next=e),t===this._mapHead&&(this._mapHead=e),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=e,e._prev=this._appendAfter):this._mapHead=e,this._appendAfter=e,null}_getOrCreateRecordForKey(t,e){if(this._records.has(t)){const n=this._records.get(t);this._maybeAddToChanges(n,e);const r=n._prev,s=n._next;return r&&(r._next=s),s&&(s._prev=r),n._next=null,n._prev=null,n}const n=new Mi(t);return this._records.set(t,n),n.currentValue=e,this._addToAdditions(n),n}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,e){Object.is(e,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=e,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,e){t instanceof Map?t.forEach(e):Object.keys(t).forEach(n=>e(t[n],n))}}class Mi{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function Vi(){return new $i([new Ii])}let $i=(()=>{class t{constructor(t){this.factories=t}static create(e,n){if(null!=n){const t=n.factories.slice();e=e.concat(t)}return new t(e)}static extend(e){return{provide:t,useFactory:n=>t.create(e,n||Vi()),deps:[[t,new qn,new Bn]]}}find(t){const e=this.factories.find(e=>e.supports(t));if(null!=e)return e;throw new Error(`Cannot find a differ supporting object '${t}' of type '${n=t,n.name||typeof n}'`);var n}}return t.\u0275prov=lt({token:t,providedIn:"root",factory:Vi}),t})();function zi(){return new Bi([new Li])}let Bi=(()=>{class t{constructor(t){this.factories=t}static create(e,n){if(n){const t=n.factories.slice();e=e.concat(t)}return new t(e)}static extend(e){return{provide:t,useFactory:n=>t.create(e,n||zi()),deps:[[t,new qn,new Bn]]}}find(t){const e=this.factories.find(e=>e.supports(t));if(e)return e;throw new Error(`Cannot find a differ supporting object '${t}'`)}}return t.\u0275prov=lt({token:t,providedIn:"root",factory:zi}),t})();function qi(t,e,n,r,s=!1){for(;null!==n;){const i=e[n.index];if(null!==i&&r.push(he(i)),Jt(i))for(let t=Qt;t<i.length;t++){const e=i[t],n=e[1].firstChild;null!==n&&qi(e[1],e,n,r)}const o=n.type;if(8&o)qi(t,e,n.child,r);else if(32&o){const t=ir(n,e);let s;for(;s=t();)r.push(s)}else if(16&o){const t=Sr(e,n);if(Array.isArray(t))r.push(...t);else{const n=or(e[16]);qi(n[1],n,t,r,!0)}}n=s?n.projectionNext:n.next}return r}class Gi{constructor(t,e){this._lView=t,this._cdRefInjectingView=e,this._appRef=null,this._attachedToViewContainer=!1}get rootNodes(){const t=this._lView,e=t[1];return qi(e,t,e.firstChild,[])}get context(){return this._lView[8]}get destroyed(){return 256==(256&this._lView[2])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[3];if(Jt(t)){const e=t[8],n=e?e.indexOf(this):-1;n>-1&&(pr(t,n),Pn(e,n))}this._attachedToViewContainer=!1}fr(this._lView[1],this._lView)}onDestroy(t){!function(t,e,n,r){const s=Ss(e);s.push(r)}(0,this._lView,0,t)}markForCheck(){ys(this._cdRefInjectingView||this._lView)}detach(){this._lView[2]&=-129}reattach(){this._lView[2]|=128}detectChanges(){vs(this._lView[1],this._lView,this.context)}checkNoChanges(){!function(t,e,n){Ae(!0);try{vs(t,e,n)}finally{Ae(!1)}}(this._lView[1],this._lView,this.context)}attachToViewContainerRef(){if(this._appRef)throw new Error("This view is already attached directly to the ApplicationRef!");this._attachedToViewContainer=!0}detachFromAppRef(){var t;this._appRef=null,Er(this._lView[1],t=this._lView,t[11],2,null,null)}attachToAppRef(t){if(this._attachedToViewContainer)throw new Error("This view is already attached to a ViewContainer!");this._appRef=t}}class Wi extends Gi{constructor(t){super(t),this._view=t}detectChanges(){_s(this._view)}checkNoChanges(){!function(t){Ae(!0);try{_s(t)}finally{Ae(!1)}}(this._view)}get context(){return null}}const Zi=function(t=!1){return function(t,e,n){if(!n&&Yt(t)){const n=fe(t.index,e);return new Gi(n,n)}return 47&t.type?new Gi(e[16],e):null}(xe(),Se(),t)};let Qi=(()=>{class t{}return t.__NG_ELEMENT_ID__=Zi,t.__ChangeDetectorRef__=!0,t})();const Ki=[new Li],Ji=new $i([new Ii]),Xi=new Bi(Ki),Yi=function(){return t=xe(),e=Se(),4&t.type?new no(e,t,Si(t,e)):null;var t,e};let to=(()=>{class t{}return t.__NG_ELEMENT_ID__=Yi,t})();const eo=to,no=class extends eo{constructor(t,e,n){super(),this._declarationLView=t,this._declarationTContainer=e,this.elementRef=n}createEmbeddedView(t){const e=this._declarationTContainer.tViews,n=Br(this._declarationLView,e,t,16,null,e.declTNode,null,null,null,null);n[17]=this._declarationLView[this._declarationTContainer.index];const r=this._declarationLView[19];return null!==r&&(n[19]=r.createEmbeddedView(e)),Wr(e,n,t),new Gi(n)}};class ro{}class so{}const io=function(){return function(t,e){let n;const r=e[t.index];if(Jt(r))n=r;else{let s;if(8&t.type)s=he(r);else{const n=e[11];s=n.createComment("");const r=de(t,e);mr(n,_r(n,r),s,function(t,e){return ue(t)?t.nextSibling(e):e.nextSibling}(n,r),!1)}e[t.index]=n=ds(r,e,s,t),ms(e,n)}return new lo(n,t,e)}(xe(),Se())};let oo=(()=>{class t{}return t.__NG_ELEMENT_ID__=io,t})();const ao=oo,lo=class extends ao{constructor(t,e,n){super(),this._lContainer=t,this._hostTNode=e,this._hostLView=n}get element(){return Si(this._hostTNode,this._hostLView)}get injector(){return new xn(this._hostTNode,this._hostLView)}get parentInjector(){const t=pn(this._hostTNode,this._hostLView);if(rn(t)){const e=on(t,this._hostLView),n=sn(t);return new xn(e[1].data[n+8],e)}return new xn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const e=uo(this._lContainer);return null!==e&&e[t]||null}get length(){return this._lContainer.length-Qt}createEmbeddedView(t,e,n){const r=t.createEmbeddedView(e||{});return this.insert(r,n),r}createComponent(t,e,n,r,s){const i=n||this.parentInjector;if(!s&&null==t.ngModule&&i){const t=i.get(ro,null);t&&(s=t)}const o=t.create(i,r,void 0,s);return this.insert(o.hostView,e),o}insert(t,e){const n=t._lView,r=n[1];if(Jt(n[3])){const e=this.indexOf(t);if(-1!==e)this.detach(e);else{const e=n[3],r=new lo(e,e[6],e[3]);r.detach(r.indexOf(t))}}const s=this._adjustIndex(e),i=this._lContainer;!function(t,e,n,r){const s=Qt+r,i=n.length;r>0&&(n[s-1][4]=e),r<i-Qt?(e[4]=n[s],In(n,Qt+r,e)):(n.push(e),e[4]=null),e[3]=n;const o=e[17];null!==o&&n!==o&&function(t,e){const n=t[9];e[16]!==e[3][3][16]&&(t[2]=!0),null===n?t[9]=[e]:n.push(e)}(o,e);const a=e[19];null!==a&&a.insertView(t),e[2]|=128}(r,n,i,s);const o=Cr(s,i),a=n[11],l=_r(a,i[7]);return null!==l&&function(t,e,n,r,s,i){r[0]=s,r[6]=e,Er(t,r,n,1,s,i)}(r,i[6],a,n,l,o),t.attachToViewContainerRef(),In(co(i),s,t),t}move(t,e){return this.insert(t,e)}indexOf(t){const e=uo(this._lContainer);return null!==e?e.indexOf(t):-1}remove(t){const e=this._adjustIndex(t,-1),n=pr(this._lContainer,e);n&&(Pn(co(this._lContainer),e),fr(n[1],n))}detach(t){const e=this._adjustIndex(t,-1),n=pr(this._lContainer,e);return n&&null!=Pn(co(this._lContainer),e)?new Gi(n):null}_adjustIndex(t,e=0){return null==t?this.length+e:t}};function uo(t){return t[8]}function co(t){return t[8]||(t[8]=[])}const ho={};class po extends wi{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const e=Gt(t);return new mo(e,this.ngModule)}}function fo(t){const e=[];for(let n in t)t.hasOwnProperty(n)&&e.push({propName:t[n],templateName:n});return e}const go=new kn("SCHEDULER_TOKEN",{providedIn:"root",factory:()=>nr});class mo extends vi{constructor(t,e){super(),this.componentDef=t,this.ngModule=e,this.componentType=t.type,this.selector=t.selectors.map(Fr).join(","),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!e}get inputs(){return fo(this.componentDef.inputs)}get outputs(){return fo(this.componentDef.outputs)}create(t,e,n,r){const s=(r=r||this.ngModule)?function(t,e){return{get:(n,r,s)=>{const i=t.get(n,ho,s);return i!==ho||r===ho?i:e.get(n,r,s)}}}(t,r.injector):t,i=s.get(Ei,ce),o=s.get(Ri,null),a=i.createRenderer(null,this.componentDef),l=this.componentDef.selectors[0][0]||"div",u=n?function(t,e,n){if(ue(t))return t.selectRootElement(e,n===Ct.ShadowDom);let r="string"==typeof e?t.querySelector(e):e;return r.textContent="",r}(a,n,this.componentDef.encapsulation):hr(i.createRenderer(null,this.componentDef),l,function(t){const e=t.toLowerCase();return"svg"===e?ae:"math"===e?"http://www.w3.org/1998/MathML/":null}(l)),c=this.componentDef.onPush?576:528,h={components:[],scheduler:nr,clean:bs,playerHandler:null,flags:0},d=ts(0,null,null,1,0,null,null,null,null,null),p=Br(null,d,h,c,null,null,i,a,o,s);let f,g;Ue(p);try{const t=function(t,e,n,r,s,i){const o=n[1];n[20]=t;const a=qr(o,20,2,"#host",null),l=a.mergedAttrs=e.hostAttrs;null!==l&&(Ts(a,l,!0),null!==t&&(Xe(s,t,l),null!==a.classes&&Rr(s,t,a.classes),null!==a.styles&&kr(s,t,a.styles)));const u=r.createRenderer(t,e),c=Br(n,Yr(e),null,e.onPush?64:16,n[20],a,r,u,null,null);return o.firstCreatePass&&(fn(cn(a,n),o,e.type),is(o,a),as(a,n.length,1)),ms(n,c),n[20]=c}(u,this.componentDef,p,i,a);if(u)if(n)Xe(a,u,["ng-version",Oi.full]);else{const{attrs:t,classes:e}=function(t){const e=[],n=[];let r=1,s=2;for(;r<t.length;){let i=t[r];if("string"==typeof i)2===s?""!==i&&e.push(i,t[++r]):8===s&&n.push(i);else{if(!Dr(s))break;s=i}r++}return{attrs:e,classes:n}}(this.componentDef.selectors[0]);t&&Xe(a,u,t),e&&e.length>0&&Rr(a,u,e.join(" "))}if(g=pe(d,Zt),void 0!==e){const t=g.projection=[];for(let n=0;n<this.ngContentSelectors.length;n++){const r=e[n];t.push(null!=r?Array.from(r):null)}}f=function(t,e,n,r,s){const i=n[1],o=function(t,e,n){const r=xe();t.firstCreatePass&&(n.providersResolver&&n.providersResolver(n),ls(t,r,e,Gr(t,e,1,null),n));const s=bn(e,t,r.directiveStart,r);er(s,e);const i=de(r,e);return i&&er(i,e),s}(i,n,e);if(r.components.push(o),t[8]=o,s&&s.forEach(t=>t(o,e)),e.contentQueries){const t=xe();e.contentQueries(1,o,t.directiveStart)}const a=xe();return!i.firstCreatePass||null===e.hostBindings&&null===e.hostAttrs||(ze(a.index),rs(n[1],a,0,a.directiveStart,a.directiveEnd,e),ss(e,o)),o}(t,this.componentDef,p,h,[zs]),Wr(d,p,null)}finally{Ve()}return new yo(this.componentType,f,Si(g,p),p,g)}}class yo extends class{}{constructor(t,e,n,r,s){super(),this.location=n,this._rootLView=r,this._tNode=s,this.instance=e,this.hostView=this.changeDetectorRef=new Wi(r),this.componentType=t}get injector(){return new xn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}const vo=new Map;class _o extends ro{constructor(t,e){super(),this._parent=e,this._bootstrapComponents=[],this.injector=this,this.destroyCbs=[],this.componentFactoryResolver=new po(this);const n=Wt(t),r=t[Dt]||null;r&&yi(r),this._bootstrapComponents=rr(n.bootstrap),this._r3Injector=Ds(t,e,[{provide:ro,useValue:this},{provide:wi,useValue:this.componentFactoryResolver}],Y(t)),this._r3Injector._resolveInjectorDefTypes(),this.instance=this.get(t)}get(t,e=$s.THROW_IF_NOT_FOUND,n=yt.Default){return t===$s||t===ro||t===ks?this:this._r3Injector.get(t,e,n)}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class wo extends so{constructor(t){super(),this.moduleType=t,null!==Wt(t)&&function(t){const e=new Set;!function t(n){const r=Wt(n,!0),s=r.id;null!==s&&(function(t,e,n){if(e&&e!==n)throw new Error(`Duplicate module registered for ${t} - ${Y(e)} vs ${Y(e.name)}`)}(s,vo.get(s),n),vo.set(s,n));const i=rr(r.imports);for(const o of i)e.has(o)||(e.add(o),t(o))}(t)}(t)}create(t){return new _o(this.moduleType,t)}}function bo(t){return e=>{setTimeout(t,void 0,e)}}const So=class extends C{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,e,n){var r,s,i;let o=t,a=e||(()=>null),l=n;if(t&&"object"==typeof t){const e=t;o=null===(r=e.next)||void 0===r?void 0:r.bind(e),a=null===(s=e.error)||void 0===s?void 0:s.bind(e),l=null===(i=e.complete)||void 0===i?void 0:i.bind(e)}this.__isAsync&&(a=bo(a),o&&(o=bo(o)),l&&(l=bo(l)));const u=super.subscribe({next:o,error:a,complete:l});return t instanceof h&&t.add(u),u}},Co=new kn("Application Initializer");let xo=(()=>{class t{constructor(t){this.appInits=t,this.resolve=bi,this.reject=bi,this.initialized=!1,this.done=!1,this.donePromise=new Promise((t,e)=>{this.resolve=t,this.reject=e})}runInitializers(){if(this.initialized)return;const t=[],e=()=>{this.done=!0,this.resolve()};if(this.appInits)for(let n=0;n<this.appInits.length;n++){const e=this.appInits[n]();ni(e)&&t.push(e)}Promise.all(t).then(()=>{e()}).catch(t=>{this.reject(t)}),0===t.length&&e(),this.initialized=!0}}return t.\u0275fac=function(e){return new(e||t)(Mn(Co,8))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();const Eo=new kn("AppId"),To={provide:Eo,useFactory:function(){return`${ko()}${ko()}${ko()}`},deps:[]};function ko(){return String.fromCharCode(97+Math.floor(25*Math.random()))}const Ro=new kn("Platform Initializer"),Ao=new kn("Platform ID"),Oo=new kn("appBootstrapListener");let Io=(()=>{class t{log(t){console.log(t)}warn(t){console.warn(t)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();const Po=new kn("LocaleId"),jo=new kn("DefaultCurrencyCode");class No{constructor(t,e){this.ngModuleFactory=t,this.componentFactories=e}}const Do=function(t){return new wo(t)},Uo=Do,Ho=function(t){return Promise.resolve(Do(t))},Lo=function(t){const e=Do(t),n=rr(Wt(t).declarations).reduce((t,e)=>{const n=Gt(e);return n&&t.push(new mo(n)),t},[]);return new No(e,n)},Fo=Lo,Mo=function(t){return Promise.resolve(Lo(t))};let Vo=(()=>{class t{constructor(){this.compileModuleSync=Uo,this.compileModuleAsync=Ho,this.compileModuleAndAllComponentsSync=Fo,this.compileModuleAndAllComponentsAsync=Mo}clearCache(){}clearCacheFor(t){}getModuleId(t){}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();const $o=(()=>Promise.resolve(0))();function zo(t){"undefined"==typeof Zone?$o.then(()=>{t&&t.apply(null,null)}):Zone.current.scheduleMicroTask("scheduleMicrotask",t)}class Bo{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:e=!1,shouldCoalesceRunChangeDetection:n=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new So(!1),this.onMicrotaskEmpty=new So(!1),this.onStable=new So(!1),this.onError=new So(!1),"undefined"==typeof Zone)throw new Error("In this configuration Angular requires Zone.js");Zone.assertZonePatched();const r=this;r._nesting=0,r._outer=r._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(r._inner=r._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(r._inner=r._inner.fork(Zone.longStackTraceZoneSpec)),r.shouldCoalesceEventChangeDetection=!n&&e,r.shouldCoalesceRunChangeDetection=n,r.lastRequestAnimationFrameId=-1,r.nativeRequestAnimationFrame=function(){let t=Rt.requestAnimationFrame,e=Rt.cancelAnimationFrame;if("undefined"!=typeof Zone&&t&&e){const n=t[Zone.__symbol__("OriginalDelegate")];n&&(t=n);const r=e[Zone.__symbol__("OriginalDelegate")];r&&(e=r)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:e}}().nativeRequestAnimationFrame,function(t){const e=()=>{!function(t){-1===t.lastRequestAnimationFrameId&&(t.lastRequestAnimationFrameId=t.nativeRequestAnimationFrame.call(Rt,()=>{t.fakeTopEventTask||(t.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{t.lastRequestAnimationFrameId=-1,Wo(t),Go(t)},void 0,()=>{},()=>{})),t.fakeTopEventTask.invoke()}),Wo(t))}(t)};t._inner=t._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,s,i,o,a)=>{try{return Zo(t),n.invokeTask(s,i,o,a)}finally{(t.shouldCoalesceEventChangeDetection&&"eventTask"===i.type||t.shouldCoalesceRunChangeDetection)&&e(),Qo(t)}},onInvoke:(n,r,s,i,o,a,l)=>{try{return Zo(t),n.invoke(s,i,o,a,l)}finally{t.shouldCoalesceRunChangeDetection&&e(),Qo(t)}},onHasTask:(e,n,r,s)=>{e.hasTask(r,s),n===r&&("microTask"==s.change?(t._hasPendingMicrotasks=s.microTask,Wo(t),Go(t)):"macroTask"==s.change&&(t.hasPendingMacrotasks=s.macroTask))},onHandleError:(e,n,r,s)=>(e.handleError(r,s),t.runOutsideAngular(()=>t.onError.emit(s)),!1)})}(r)}static isInAngularZone(){return!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!Bo.isInAngularZone())throw new Error("Expected to be in Angular Zone, but it is not!")}static assertNotInAngularZone(){if(Bo.isInAngularZone())throw new Error("Expected to not be in Angular Zone, but it is!")}run(t,e,n){return this._inner.run(t,e,n)}runTask(t,e,n,r){const s=this._inner,i=s.scheduleEventTask("NgZoneEvent: "+r,t,qo,bi,bi);try{return s.runTask(i,e,n)}finally{s.cancelTask(i)}}runGuarded(t,e,n){return this._inner.runGuarded(t,e,n)}runOutsideAngular(t){return this._outer.run(t)}}const qo={};function Go(t){if(0==t._nesting&&!t.hasPendingMicrotasks&&!t.isStable)try{t._nesting++,t.onMicrotaskEmpty.emit(null)}finally{if(t._nesting--,!t.hasPendingMicrotasks)try{t.runOutsideAngular(()=>t.onStable.emit(null))}finally{t.isStable=!0}}}function Wo(t){t.hasPendingMicrotasks=!!(t._hasPendingMicrotasks||(t.shouldCoalesceEventChangeDetection||t.shouldCoalesceRunChangeDetection)&&-1!==t.lastRequestAnimationFrameId)}function Zo(t){t._nesting++,t.isStable&&(t.isStable=!1,t.onUnstable.emit(null))}function Qo(t){t._nesting--,Go(t)}class Ko{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new So,this.onMicrotaskEmpty=new So,this.onStable=new So,this.onError=new So}run(t,e,n){return t.apply(e,n)}runGuarded(t,e,n){return t.apply(e,n)}runOutsideAngular(t){return t()}runTask(t,e,n,r){return t.apply(e,n)}}let Jo=(()=>{class t{constructor(t){this._ngZone=t,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,this._watchAngularEvents(),t.run(()=>{this.taskTrackingZone="undefined"==typeof Zone?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{Bo.assertNotInAngularZone(),zo(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())zo(()=>{for(;0!==this._callbacks.length;){let t=this._callbacks.pop();clearTimeout(t.timeoutId),t.doneCb(this._didWork)}this._didWork=!1});else{let t=this.getPendingTasks();this._callbacks=this._callbacks.filter(e=>!e.updateCb||!e.updateCb(t)||(clearTimeout(e.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(t=>({source:t.source,creationLocation:t.creationLocation,data:t.data})):[]}addCallback(t,e,n){let r=-1;e&&e>0&&(r=setTimeout(()=>{this._callbacks=this._callbacks.filter(t=>t.timeoutId!==r),t(this._didWork,this.getPendingTasks())},e)),this._callbacks.push({doneCb:t,timeoutId:r,updateCb:n})}whenStable(t,e,n){if(n&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/dist/task-tracking.js" loaded?');this.addCallback(t,e,n),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}findProviders(t,e,n){return[]}}return t.\u0275fac=function(e){return new(e||t)(Mn(Bo))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),Xo=(()=>{class t{constructor(){this._applications=new Map,ea.addToWindow(this)}registerApplication(t,e){this._applications.set(t,e)}unregisterApplication(t){this._applications.delete(t)}unregisterAllApplications(){this._applications.clear()}getTestability(t){return this._applications.get(t)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(t,e=!0){return ea.findTestabilityInTree(this,t,e)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();class Yo{addToWindow(t){}findTestabilityInTree(t,e,n){return null}}let ta,ea=new Yo,na=!0,ra=!1;const sa=new kn("AllowMultipleToken");class ia{constructor(t,e){this.name=t,this.token=e}}function oa(t,e,n=[]){const r=`Platform: ${e}`,s=new kn(r);return(e=[])=>{let i=aa();if(!i||i.injector.get(sa,!1))if(t)t(n.concat(e).concat({provide:s,useValue:!0}));else{const t=n.concat(e).concat({provide:s,useValue:!0},{provide:As,useValue:"platform"});!function(t){if(ta&&!ta.destroyed&&!ta.injector.get(sa,!1))throw new Error("There can be only one platform. Destroy the previous one to create a new one.");ta=t.get(la);const e=t.get(Ro,null);e&&e.forEach(t=>t())}($s.create({providers:t,name:r}))}return function(t){const e=aa();if(!e)throw new Error("No platform exists!");if(!e.injector.get(t,null))throw new Error("A platform with a different configuration has been created. Please destroy it first.");return e}(s)}}function aa(){return ta&&!ta.destroyed?ta:null}let la=(()=>{class t{constructor(t){this._injector=t,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(t,e){const n=function(t,e){let n;return n="noop"===t?new Ko:("zone.js"===t?void 0:t)||new Bo({enableLongStackTrace:(ra=!0,na),shouldCoalesceEventChangeDetection:!!(null==e?void 0:e.ngZoneEventCoalescing),shouldCoalesceRunChangeDetection:!!(null==e?void 0:e.ngZoneRunCoalescing)}),n}(e?e.ngZone:void 0,{ngZoneEventCoalescing:e&&e.ngZoneEventCoalescing||!1,ngZoneRunCoalescing:e&&e.ngZoneRunCoalescing||!1}),r=[{provide:Bo,useValue:n}];return n.run(()=>{const e=$s.create({providers:r,parent:this.injector,name:t.moduleType.name}),s=t.create(e),i=s.injector.get(tr,null);if(!i)throw new Error("No ErrorHandler. Is platform module (BrowserModule) included?");return n.runOutsideAngular(()=>{const t=n.onError.subscribe({next:t=>{i.handleError(t)}});s.onDestroy(()=>{ha(this._modules,s),t.unsubscribe()})}),function(t,e,n){try{const r=n();return ni(r)?r.catch(n=>{throw e.runOutsideAngular(()=>t.handleError(n)),n}):r}catch(r){throw e.runOutsideAngular(()=>t.handleError(r)),r}}(i,n,()=>{const t=s.injector.get(xo);return t.runInitializers(),t.donePromise.then(()=>(yi(s.injector.get(Po,gi)||gi),this._moduleDoBootstrap(s),s))})})}bootstrapModule(t,e=[]){const n=ua({},e);return function(t,e,n){const r=new wo(n);return Promise.resolve(r)}(0,0,t).then(t=>this.bootstrapModuleFactory(t,n))}_moduleDoBootstrap(t){const e=t.injector.get(ca);if(t._bootstrapComponents.length>0)t._bootstrapComponents.forEach(t=>e.bootstrap(t));else{if(!t.instance.ngDoBootstrap)throw new Error(`The module ${Y(t.instance.constructor)} was bootstrapped, but it does not declare "@NgModule.bootstrap" components nor a "ngDoBootstrap" method. Please define one of these.`);t.instance.ngDoBootstrap(e)}this._modules.push(t)}onDestroy(t){this._destroyListeners.push(t)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new Error("The platform has already been destroyed!");this._modules.slice().forEach(t=>t.destroy()),this._destroyListeners.forEach(t=>t()),this._destroyed=!0}get destroyed(){return this._destroyed}}return t.\u0275fac=function(e){return new(e||t)(Mn($s))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();function ua(t,e){return Array.isArray(e)?e.reduce(ua,t):Object.assign(Object.assign({},t),e)}let ca=(()=>{class t{constructor(t,e,n,r,s){this._zone=t,this._injector=e,this._exceptionHandler=n,this._componentFactoryResolver=r,this._initStatus=s,this._bootstrapListeners=[],this._views=[],this._runningTick=!1,this._stable=!0,this.componentTypes=[],this.components=[],this._onMicrotaskEmptySubscription=this._zone.onMicrotaskEmpty.subscribe({next:()=>{this._zone.run(()=>{this.tick()})}});const i=new v(t=>{this._stable=this._zone.isStable&&!this._zone.hasPendingMacrotasks&&!this._zone.hasPendingMicrotasks,this._zone.runOutsideAngular(()=>{t.next(this._stable),t.complete()})}),o=new v(t=>{let e;this._zone.runOutsideAngular(()=>{e=this._zone.onStable.subscribe(()=>{Bo.assertNotInAngularZone(),zo(()=>{this._stable||this._zone.hasPendingMacrotasks||this._zone.hasPendingMicrotasks||(this._stable=!0,t.next(!0))})})});const n=this._zone.onUnstable.subscribe(()=>{Bo.assertInAngularZone(),this._stable&&(this._stable=!1,this._zone.runOutsideAngular(()=>{t.next(!1)}))});return()=>{e.unsubscribe(),n.unsubscribe()}});this.isStable=function(...t){let e=Number.POSITIVE_INFINITY,n=null,r=t[t.length-1];return E(r)?(n=t.pop(),t.length>1&&"number"==typeof t[t.length-1]&&(e=t.pop())):"number"==typeof r&&(e=t.pop()),null===n&&1===t.length&&t[0]instanceof v?t[0]:z(e)(B(t,n))}(i,o.pipe(t=>{return q()((e=J,function(t){let n;n="function"==typeof e?e:function(){return e};const r=Object.create(t,Q);return r.source=t,r.subjectFactory=n,r})(t));var e}))}bootstrap(t,e){if(!this._initStatus.done)throw new Error("Cannot bootstrap as there are still asynchronous initializers running. Bootstrap components in the `ngDoBootstrap` method of the root module.");let n;n=t instanceof vi?t:this._componentFactoryResolver.resolveComponentFactory(t),this.componentTypes.push(n.componentType);const r=n.isBoundToModule?void 0:this._injector.get(ro),s=n.create($s.NULL,[],e||n.selector,r),i=s.location.nativeElement,o=s.injector.get(Jo,null),a=o&&s.injector.get(Xo);return o&&a&&a.registerApplication(i,o),s.onDestroy(()=>{this.detachView(s.hostView),ha(this.components,s),a&&a.unregisterApplication(i)}),this._loadComponent(s),s}tick(){if(this._runningTick)throw new Error("ApplicationRef.tick is called recursively");try{this._runningTick=!0;for(let t of this._views)t.detectChanges()}catch(t){this._zone.runOutsideAngular(()=>this._exceptionHandler.handleError(t))}finally{this._runningTick=!1}}attachView(t){const e=t;this._views.push(e),e.attachToAppRef(this)}detachView(t){const e=t;ha(this._views,e),e.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView),this.tick(),this.components.push(t),this._injector.get(Oo,[]).concat(this._bootstrapListeners).forEach(e=>e(t))}ngOnDestroy(){this._views.slice().forEach(t=>t.destroy()),this._onMicrotaskEmptySubscription.unsubscribe()}get viewCount(){return this._views.length}}return t.\u0275fac=function(e){return new(e||t)(Mn(Bo),Mn($s),Mn(tr),Mn(wi),Mn(xo))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();function ha(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class da{}class pa{}const fa={factoryPathPrefix:"",factoryPathSuffix:".ngfactory"};let ga=(()=>{class t{constructor(t,e){this._compiler=t,this._config=e||fa}load(t){return this.loadAndCompile(t)}loadAndCompile(t){let[e,r]=t.split("#");return void 0===r&&(r="default"),n("zn8P")(e).then(t=>t[r]).then(t=>ma(t,e,r)).then(t=>this._compiler.compileModuleAsync(t))}loadFactory(t){let[e,r]=t.split("#"),s="NgFactory";return void 0===r&&(r="default",s=""),n("zn8P")(this._config.factoryPathPrefix+e+this._config.factoryPathSuffix).then(t=>t[r+s]).then(t=>ma(t,e,r))}}return t.\u0275fac=function(e){return new(e||t)(Mn(Vo),Mn(pa,8))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();function ma(t,e,n){if(!t)throw new Error(`Cannot find '${n}' in '${e}'`);return t}const ya=oa(null,"core",[{provide:Ao,useValue:"unknown"},{provide:la,deps:[$s]},{provide:Xo,deps:[]},{provide:Io,deps:[]}]),va=[{provide:ca,useClass:ca,deps:[Bo,$s,tr,wi,xo]},{provide:go,deps:[Bo],useFactory:function(t){let e=[];return t.onStable.subscribe(()=>{for(;e.length;)e.pop()()}),function(t){e.push(t)}}},{provide:xo,useClass:xo,deps:[[new Bn,Co]]},{provide:Vo,useClass:Vo,deps:[]},To,{provide:$i,useFactory:function(){return Ji},deps:[]},{provide:Bi,useFactory:function(){return Xi},deps:[]},{provide:Po,useFactory:function(t){return yi(t=t||"undefined"!=typeof $localize&&$localize.locale||gi),t},deps:[[new zn(Po),new Bn,new qn]]},{provide:jo,useValue:"USD"}];let _a=(()=>{class t{constructor(t){}}return t.\u0275fac=function(e){return new(e||t)(Mn(ca))},t.\u0275mod=zt({type:t}),t.\u0275inj=ut({providers:va}),t})(),wa=null;function ba(){return wa}const Sa=new kn("DocumentToken");let Ca=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=lt({factory:xa,token:t,providedIn:"platform"}),t})();function xa(){return Mn(Ta)}const Ea=new kn("Location Initialized");let Ta=(()=>{class t extends Ca{constructor(t){super(),this._doc=t,this._init()}_init(){this.location=ba().getLocation(),this._history=ba().getHistory()}getBaseHrefFromDOM(){return ba().getBaseHref(this._doc)}onPopState(t){ba().getGlobalEventTarget(this._doc,"window").addEventListener("popstate",t,!1)}onHashChange(t){ba().getGlobalEventTarget(this._doc,"window").addEventListener("hashchange",t,!1)}get href(){return this.location.href}get protocol(){return this.location.protocol}get hostname(){return this.location.hostname}get port(){return this.location.port}get pathname(){return this.location.pathname}get search(){return this.location.search}get hash(){return this.location.hash}set pathname(t){this.location.pathname=t}pushState(t,e,n){ka()?this._history.pushState(t,e,n):this.location.hash=n}replaceState(t,e,n){ka()?this._history.replaceState(t,e,n):this.location.hash=n}forward(){this._history.forward()}back(){this._history.back()}getState(){return this._history.state}}return t.\u0275fac=function(e){return new(e||t)(Mn(Sa))},t.\u0275prov=lt({factory:Ra,token:t,providedIn:"platform"}),t})();function ka(){return!!window.history.pushState}function Ra(){return new Ta(Mn(Sa))}function Aa(t,e){if(0==t.length)return e;if(0==e.length)return t;let n=0;return t.endsWith("/")&&n++,e.startsWith("/")&&n++,2==n?t+e.substring(1):1==n?t+e:t+"/"+e}function Oa(t){const e=t.match(/#|\?|$/),n=e&&e.index||t.length;return t.slice(0,n-("/"===t[n-1]?1:0))+t.slice(n)}function Ia(t){return t&&"?"!==t[0]?"?"+t:t}let Pa=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=lt({factory:ja,token:t,providedIn:"root"}),t})();function ja(t){const e=Mn(Sa).location;return new Da(Mn(Ca),e&&e.origin||"")}const Na=new kn("appBaseHref");let Da=(()=>{class t extends Pa{constructor(t,e){if(super(),this._platformLocation=t,null==e&&(e=this._platformLocation.getBaseHrefFromDOM()),null==e)throw new Error("No base href set. Please provide a value for the APP_BASE_HREF token or add a base element to the document.");this._baseHref=e}onPopState(t){this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t)}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return Aa(this._baseHref,t)}path(t=!1){const e=this._platformLocation.pathname+Ia(this._platformLocation.search),n=this._platformLocation.hash;return n&&t?`${e}${n}`:e}pushState(t,e,n,r){const s=this.prepareExternalUrl(n+Ia(r));this._platformLocation.pushState(t,e,s)}replaceState(t,e,n,r){const s=this.prepareExternalUrl(n+Ia(r));this._platformLocation.replaceState(t,e,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}}return t.\u0275fac=function(e){return new(e||t)(Mn(Ca),Mn(Na,8))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),Ua=(()=>{class t extends Pa{constructor(t,e){super(),this._platformLocation=t,this._baseHref="",null!=e&&(this._baseHref=e)}onPopState(t){this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t)}getBaseHref(){return this._baseHref}path(t=!1){let e=this._platformLocation.hash;return null==e&&(e="#"),e.length>0?e.substring(1):e}prepareExternalUrl(t){const e=Aa(this._baseHref,t);return e.length>0?"#"+e:e}pushState(t,e,n,r){let s=this.prepareExternalUrl(n+Ia(r));0==s.length&&(s=this._platformLocation.pathname),this._platformLocation.pushState(t,e,s)}replaceState(t,e,n,r){let s=this.prepareExternalUrl(n+Ia(r));0==s.length&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(t,e,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}}return t.\u0275fac=function(e){return new(e||t)(Mn(Ca),Mn(Na,8))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),Ha=(()=>{class t{constructor(t,e){this._subject=new So,this._urlChangeListeners=[],this._platformStrategy=t;const n=this._platformStrategy.getBaseHref();this._platformLocation=e,this._baseHref=Oa(Fa(n)),this._platformStrategy.onPopState(t=>{this._subject.emit({url:this.path(!0),pop:!0,state:t.state,type:t.type})})}path(t=!1){return this.normalize(this._platformStrategy.path(t))}getState(){return this._platformLocation.getState()}isCurrentPathEqualTo(t,e=""){return this.path()==this.normalize(t+Ia(e))}normalize(e){return t.stripTrailingSlash(function(t,e){return t&&e.startsWith(t)?e.substring(t.length):e}(this._baseHref,Fa(e)))}prepareExternalUrl(t){return t&&"/"!==t[0]&&(t="/"+t),this._platformStrategy.prepareExternalUrl(t)}go(t,e="",n=null){this._platformStrategy.pushState(n,"",t,e),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Ia(e)),n)}replaceState(t,e="",n=null){this._platformStrategy.replaceState(n,"",t,e),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+Ia(e)),n)}forward(){this._platformStrategy.forward()}back(){this._platformStrategy.back()}onUrlChange(t){this._urlChangeListeners.push(t),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(t=>{this._notifyUrlChangeListeners(t.url,t.state)}))}_notifyUrlChangeListeners(t="",e){this._urlChangeListeners.forEach(n=>n(t,e))}subscribe(t,e,n){return this._subject.subscribe({next:t,error:e,complete:n})}}return t.\u0275fac=function(e){return new(e||t)(Mn(Pa),Mn(Ca))},t.normalizeQueryParams=Ia,t.joinWithSlash=Aa,t.stripTrailingSlash=Oa,t.\u0275prov=lt({factory:La,token:t,providedIn:"root"}),t})();function La(){return new Ha(Mn(Pa),Mn(Ca))}function Fa(t){return t.replace(/\/index.html$/,"")}var Ma=function(t){return t[t.Zero=0]="Zero",t[t.One=1]="One",t[t.Two=2]="Two",t[t.Few=3]="Few",t[t.Many=4]="Many",t[t.Other=5]="Other",t}({});class Va{}let $a=(()=>{class t extends Va{constructor(t){super(),this.locale=t}getPluralCategory(t,e){switch(function(t){return function(t){const e=function(t){return t.toLowerCase().replace(/_/g,"-")}(t);let n=pi(e);if(n)return n;const r=e.split("-")[0];if(n=pi(r),n)return n;if("en"===r)return hi;throw new Error(`Missing locale data for the locale "${t}".`)}(t)[fi.PluralCase]}(e||this.locale)(t)){case Ma.Zero:return"zero";case Ma.One:return"one";case Ma.Two:return"two";case Ma.Few:return"few";case Ma.Many:return"many";default:return"other"}}}return t.\u0275fac=function(e){return new(e||t)(Mn(Po))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();function za(t,e){e=encodeURIComponent(e);for(const n of t.split(";")){const t=n.indexOf("="),[r,s]=-1==t?[n,""]:[n.slice(0,t),n.slice(t+1)];if(r.trim()===e)return decodeURIComponent(s)}return null}class Ba{constructor(t,e,n,r){this.$implicit=t,this.ngForOf=e,this.index=n,this.count=r}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let qa=(()=>{class t{constructor(t,e,n){this._viewContainer=t,this._template=e,this._differs=n,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const n=this._ngForOf;if(!this._differ&&n)try{this._differ=this._differs.find(n).create(this.ngForTrackBy)}catch(e){throw new Error(`Cannot find a differ supporting object '${n}' of type '${t=n,t.name||typeof t}'. NgFor only supports binding to Iterables such as Arrays.`)}}var t;if(this._differ){const t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){const e=[];t.forEachOperation((t,n,r)=>{if(null==t.previousIndex){const n=this._viewContainer.createEmbeddedView(this._template,new Ba(null,this._ngForOf,-1,-1),null===r?void 0:r),s=new Ga(t,n);e.push(s)}else if(null==r)this._viewContainer.remove(null===n?void 0:n);else if(null!==n){const s=this._viewContainer.get(n);this._viewContainer.move(s,r);const i=new Ga(t,s);e.push(i)}});for(let n=0;n<e.length;n++)this._perViewChange(e[n].view,e[n].record);for(let n=0,r=this._viewContainer.length;n<r;n++){const t=this._viewContainer.get(n);t.context.index=n,t.context.count=r,t.context.ngForOf=this._ngForOf}t.forEachIdentityChange(t=>{this._viewContainer.get(t.currentIndex).context.$implicit=t.item})}_perViewChange(t,e){t.context.$implicit=e.item}static ngTemplateContextGuard(t,e){return!0}}return t.\u0275fac=function(e){return new(e||t)(Ks(oo),Ks(to),Ks($i))},t.\u0275dir=qt({type:t,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}}),t})();class Ga{constructor(t,e){this.record=t,this.view=e}}let Wa=(()=>{class t{constructor(t,e){this._viewContainer=t,this._context=new Za,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=e}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Qa("ngIfThen",t),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Qa("ngIfElse",t),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(t,e){return!0}}return t.\u0275fac=function(e){return new(e||t)(Ks(oo),Ks(to))},t.\u0275dir=qt({type:t,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}}),t})();class Za{constructor(){this.$implicit=null,this.ngIf=null}}function Qa(t,e){if(e&&!e.createEmbeddedView)throw new Error(`${t} must be a TemplateRef, but received '${Y(e)}'.`)}let Ka=(()=>{class t{constructor(t,e,n){this._ngEl=t,this._differs=e,this._renderer=n,this._ngStyle=null,this._differ=null}set ngStyle(t){this._ngStyle=t,!this._differ&&t&&(this._differ=this._differs.find(t).create())}ngDoCheck(){if(this._differ){const t=this._differ.diff(this._ngStyle);t&&this._applyChanges(t)}}_setStyle(t,e){const[n,r]=t.split(".");null!=(e=null!=e&&r?`${e}${r}`:e)?this._renderer.setStyle(this._ngEl.nativeElement,n,e):this._renderer.removeStyle(this._ngEl.nativeElement,n)}_applyChanges(t){t.forEachRemovedItem(t=>this._setStyle(t.key,null)),t.forEachAddedItem(t=>this._setStyle(t.key,t.currentValue)),t.forEachChangedItem(t=>this._setStyle(t.key,t.currentValue))}}return t.\u0275fac=function(e){return new(e||t)(Ks(xi),Ks(Bi),Ks(Ti))},t.\u0275dir=qt({type:t,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"}}),t})(),Ja=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=zt({type:t}),t.\u0275inj=ut({providers:[{provide:Va,useClass:$a}]}),t})(),Xa=(()=>{class t{}return t.\u0275prov=lt({token:t,providedIn:"root",factory:()=>new Ya(Mn(Sa),window)}),t})();class Ya{constructor(t,e){this.document=t,this.window=e,this.offset=()=>[0,0]}setOffset(t){this.offset=Array.isArray(t)?()=>t:t}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(t){this.supportsScrolling()&&this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){if(!this.supportsScrolling())return;const e=function(t,e){const n=t.getElementById(e)||t.getElementsByName(e)[0];if(n)return n;if("function"==typeof t.createTreeWalker&&t.body&&(t.body.createShadowRoot||t.body.attachShadow)){const n=t.createTreeWalker(t.body,NodeFilter.SHOW_ELEMENT);let r=n.currentNode;for(;r;){const t=r.shadowRoot;if(t){const n=t.getElementById(e)||t.querySelector(`[name="${e}"]`);if(n)return n}r=n.nextNode()}}return null}(this.document,t);e&&(this.scrollToElement(e),this.attemptFocus(e))}setHistoryScrollRestoration(t){if(this.supportScrollRestoration()){const e=this.window.history;e&&e.scrollRestoration&&(e.scrollRestoration=t)}}scrollToElement(t){const e=t.getBoundingClientRect(),n=e.left+this.window.pageXOffset,r=e.top+this.window.pageYOffset,s=this.offset();this.window.scrollTo(n-s[0],r-s[1])}attemptFocus(t){return t.focus(),this.document.activeElement===t}supportScrollRestoration(){try{if(!this.supportsScrolling())return!1;const t=tl(this.window.history)||tl(Object.getPrototypeOf(this.window.history));return!(!t||!t.writable&&!t.set)}catch(t){return!1}}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch(t){return!1}}}function tl(t){return Object.getOwnPropertyDescriptor(t,"scrollRestoration")}class el extends class extends class{}{constructor(){super()}supportsDOMEvents(){return!0}}{static makeCurrent(){var t;t=new el,wa||(wa=t)}getProperty(t,e){return t[e]}log(t){window.console&&window.console.log&&window.console.log(t)}logGroup(t){window.console&&window.console.group&&window.console.group(t)}logGroupEnd(){window.console&&window.console.groupEnd&&window.console.groupEnd()}onAndCancel(t,e,n){return t.addEventListener(e,n,!1),()=>{t.removeEventListener(e,n,!1)}}dispatchEvent(t,e){t.dispatchEvent(e)}remove(t){return t.parentNode&&t.parentNode.removeChild(t),t}getValue(t){return t.value}createElement(t,e){return(e=e||this.getDefaultDocument()).createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,e){return"window"===e?window:"document"===e?t:"body"===e?t.body:null}getHistory(){return window.history}getLocation(){return window.location}getBaseHref(t){const e=rl||(rl=document.querySelector("base"),rl)?rl.getAttribute("href"):null;return null==e?null:(n=e,nl||(nl=document.createElement("a")),nl.setAttribute("href",n),"/"===nl.pathname.charAt(0)?nl.pathname:"/"+nl.pathname);var n}resetBaseElement(){rl=null}getUserAgent(){return window.navigator.userAgent}performanceNow(){return window.performance&&window.performance.now?window.performance.now():(new Date).getTime()}supportsCookies(){return!0}getCookie(t){return za(document.cookie,t)}}let nl,rl=null;const sl=new kn("TRANSITION_ID"),il=[{provide:Co,useFactory:function(t,e,n){return()=>{n.get(xo).donePromise.then(()=>{const n=ba();Array.prototype.slice.apply(e.querySelectorAll("style[ng-transition]")).filter(e=>e.getAttribute("ng-transition")===t).forEach(t=>n.remove(t))})}},deps:[sl,Sa,$s],multi:!0}];class ol{static init(){var t;t=new ol,ea=t}addToWindow(t){Rt.getAngularTestability=(e,n=!0)=>{const r=t.findTestabilityInTree(e,n);if(null==r)throw new Error("Could not find testability for element.");return r},Rt.getAllAngularTestabilities=()=>t.getAllTestabilities(),Rt.getAllAngularRootElements=()=>t.getAllRootElements(),Rt.frameworkStabilizers||(Rt.frameworkStabilizers=[]),Rt.frameworkStabilizers.push(t=>{const e=Rt.getAllAngularTestabilities();let n=e.length,r=!1;const s=function(e){r=r||e,n--,0==n&&t(r)};e.forEach(function(t){t.whenStable(s)})})}findTestabilityInTree(t,e,n){if(null==e)return null;const r=t.getTestability(e);return null!=r?r:n?ba().isShadowRoot(e)?this.findTestabilityInTree(t,e.host,!0):this.findTestabilityInTree(t,e.parentElement,!0):null}}const al=new kn("EventManagerPlugins");let ll=(()=>{class t{constructor(t,e){this._zone=e,this._eventNameToPlugin=new Map,t.forEach(t=>t.manager=this),this._plugins=t.slice().reverse()}addEventListener(t,e,n){return this._findPluginFor(e).addEventListener(t,e,n)}addGlobalEventListener(t,e,n){return this._findPluginFor(e).addGlobalEventListener(t,e,n)}getZone(){return this._zone}_findPluginFor(t){const e=this._eventNameToPlugin.get(t);if(e)return e;const n=this._plugins;for(let r=0;r<n.length;r++){const e=n[r];if(e.supports(t))return this._eventNameToPlugin.set(t,e),e}throw new Error(`No event manager plugin found for event ${t}`)}}return t.\u0275fac=function(e){return new(e||t)(Mn(al),Mn(Bo))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();class ul{constructor(t){this._doc=t}addGlobalEventListener(t,e,n){const r=ba().getGlobalEventTarget(this._doc,t);if(!r)throw new Error(`Unsupported event target ${r} for event ${e}`);return this.addEventListener(r,e,n)}}let cl=(()=>{class t{constructor(){this._stylesSet=new Set}addStyles(t){const e=new Set;t.forEach(t=>{this._stylesSet.has(t)||(this._stylesSet.add(t),e.add(t))}),this.onStylesAdded(e)}onStylesAdded(t){}getAllStyles(){return Array.from(this._stylesSet)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),hl=(()=>{class t extends cl{constructor(t){super(),this._doc=t,this._hostNodes=new Map,this._hostNodes.set(t.head,[])}_addStylesToHost(t,e,n){t.forEach(t=>{const r=this._doc.createElement("style");r.textContent=t,n.push(e.appendChild(r))})}addHost(t){const e=[];this._addStylesToHost(this._stylesSet,t,e),this._hostNodes.set(t,e)}removeHost(t){const e=this._hostNodes.get(t);e&&e.forEach(dl),this._hostNodes.delete(t)}onStylesAdded(t){this._hostNodes.forEach((e,n)=>{this._addStylesToHost(t,n,e)})}ngOnDestroy(){this._hostNodes.forEach(t=>t.forEach(dl))}}return t.\u0275fac=function(e){return new(e||t)(Mn(Sa))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();function dl(t){ba().remove(t)}const pl={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},fl=/%COMP%/g;function gl(t,e,n){for(let r=0;r<e.length;r++){let s=e[r];Array.isArray(s)?gl(t,s,n):(s=s.replace(fl,t),n.push(s))}return n}function ml(t){return e=>{if("__ngUnwrap__"===e)return t;!1===t(e)&&(e.preventDefault(),e.returnValue=!1)}}let yl=(()=>{class t{constructor(t,e,n){this.eventManager=t,this.sharedStylesHost=e,this.appId=n,this.rendererByCompId=new Map,this.defaultRenderer=new vl(t)}createRenderer(t,e){if(!t||!e)return this.defaultRenderer;switch(e.encapsulation){case Ct.Emulated:{let n=this.rendererByCompId.get(e.id);return n||(n=new _l(this.eventManager,this.sharedStylesHost,e,this.appId),this.rendererByCompId.set(e.id,n)),n.applyToHost(t),n}case 1:case Ct.ShadowDom:return new wl(this.eventManager,this.sharedStylesHost,t,e);default:if(!this.rendererByCompId.has(e.id)){const t=gl(e.id,e.styles,[]);this.sharedStylesHost.addStyles(t),this.rendererByCompId.set(e.id,this.defaultRenderer)}return this.defaultRenderer}}begin(){}end(){}}return t.\u0275fac=function(e){return new(e||t)(Mn(ll),Mn(hl),Mn(Eo))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();class vl{constructor(t){this.eventManager=t,this.data=Object.create(null)}destroy(){}createElement(t,e){return e?document.createElementNS(pl[e]||e,t):document.createElement(t)}createComment(t){return document.createComment(t)}createText(t){return document.createTextNode(t)}appendChild(t,e){t.appendChild(e)}insertBefore(t,e,n){t&&t.insertBefore(e,n)}removeChild(t,e){t&&t.removeChild(e)}selectRootElement(t,e){let n="string"==typeof t?document.querySelector(t):t;if(!n)throw new Error(`The selector "${t}" did not match any elements`);return e||(n.textContent=""),n}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,e,n,r){if(r){e=r+":"+e;const s=pl[r];s?t.setAttributeNS(s,e,n):t.setAttribute(e,n)}else t.setAttribute(e,n)}removeAttribute(t,e,n){if(n){const r=pl[n];r?t.removeAttributeNS(r,e):t.removeAttribute(`${n}:${e}`)}else t.removeAttribute(e)}addClass(t,e){t.classList.add(e)}removeClass(t,e){t.classList.remove(e)}setStyle(t,e,n,r){r&(sr.DashCase|sr.Important)?t.style.setProperty(e,n,r&sr.Important?"important":""):t.style[e]=n}removeStyle(t,e,n){n&sr.DashCase?t.style.removeProperty(e):t.style[e]=""}setProperty(t,e,n){t[e]=n}setValue(t,e){t.nodeValue=e}listen(t,e,n){return"string"==typeof t?this.eventManager.addGlobalEventListener(t,e,ml(n)):this.eventManager.addEventListener(t,e,ml(n))}}class _l extends vl{constructor(t,e,n,r){super(t),this.component=n;const s=gl(r+"-"+n.id,n.styles,[]);e.addStyles(s),this.contentAttr="_ngcontent-%COMP%".replace(fl,r+"-"+n.id),this.hostAttr="_nghost-%COMP%".replace(fl,r+"-"+n.id)}applyToHost(t){super.setAttribute(t,this.hostAttr,"")}createElement(t,e){const n=super.createElement(t,e);return super.setAttribute(n,this.contentAttr,""),n}}class wl extends vl{constructor(t,e,n,r){super(t),this.sharedStylesHost=e,this.hostEl=n,this.shadowRoot=n.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const s=gl(r.id,r.styles,[]);for(let i=0;i<s.length;i++){const t=document.createElement("style");t.textContent=s[i],this.shadowRoot.appendChild(t)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}appendChild(t,e){return super.appendChild(this.nodeOrShadowRoot(t),e)}insertBefore(t,e,n){return super.insertBefore(this.nodeOrShadowRoot(t),e,n)}removeChild(t,e){return super.removeChild(this.nodeOrShadowRoot(t),e)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}}let bl=(()=>{class t extends ul{constructor(t){super(t)}supports(t){return!0}addEventListener(t,e,n){return t.addEventListener(e,n,!1),()=>this.removeEventListener(t,e,n)}removeEventListener(t,e,n){return t.removeEventListener(e,n)}}return t.\u0275fac=function(e){return new(e||t)(Mn(Sa))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();const Sl=["alt","control","meta","shift"],Cl={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},xl={A:"1",B:"2",C:"3",D:"4",E:"5",F:"6",G:"7",H:"8",I:"9",J:"*",K:"+",M:"-",N:".",O:"/","`":"0","\x90":"NumLock"},El={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey};let Tl=(()=>{class t extends ul{constructor(t){super(t)}supports(e){return null!=t.parseEventName(e)}addEventListener(e,n,r){const s=t.parseEventName(n),i=t.eventCallback(s.fullKey,r,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>ba().onAndCancel(e,s.domEventName,i))}static parseEventName(e){const n=e.toLowerCase().split("."),r=n.shift();if(0===n.length||"keydown"!==r&&"keyup"!==r)return null;const s=t._normalizeKey(n.pop());let i="";if(Sl.forEach(t=>{const e=n.indexOf(t);e>-1&&(n.splice(e,1),i+=t+".")}),i+=s,0!=n.length||0===s.length)return null;const o={};return o.domEventName=r,o.fullKey=i,o}static getEventFullKey(t){let e="",n=function(t){let e=t.key;if(null==e){if(e=t.keyIdentifier,null==e)return"Unidentified";e.startsWith("U+")&&(e=String.fromCharCode(parseInt(e.substring(2),16)),3===t.location&&xl.hasOwnProperty(e)&&(e=xl[e]))}return Cl[e]||e}(t);return n=n.toLowerCase()," "===n?n="space":"."===n&&(n="dot"),Sl.forEach(r=>{r!=n&&(0,El[r])(t)&&(e+=r+".")}),e+=n,e}static eventCallback(e,n,r){return s=>{t.getEventFullKey(s)===e&&r.runGuarded(()=>n(s))}}static _normalizeKey(t){switch(t){case"esc":return"escape";default:return t}}}return t.\u0275fac=function(e){return new(e||t)(Mn(Sa))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();const kl=oa(ya,"browser",[{provide:Ao,useValue:"browser"},{provide:Ro,useValue:function(){el.makeCurrent(),ol.init()},multi:!0},{provide:Sa,useFactory:function(){return function(t){le=t}(document),document},deps:[]}]),Rl=[[],{provide:As,useValue:"root"},{provide:tr,useFactory:function(){return new tr},deps:[]},{provide:al,useClass:bl,multi:!0,deps:[Sa,Bo,Ao]},{provide:al,useClass:Tl,multi:!0,deps:[Sa]},[],{provide:yl,useClass:yl,deps:[ll,hl,Eo]},{provide:Ei,useExisting:yl},{provide:cl,useExisting:hl},{provide:hl,useClass:hl,deps:[Sa]},{provide:Jo,useClass:Jo,deps:[Bo]},{provide:ll,useClass:ll,deps:[al,Bo]},[]];let Al=(()=>{class t{constructor(t){if(t)throw new Error("BrowserModule has already been loaded. If you need access to common directives such as NgIf and NgFor from a lazy loaded module, import CommonModule instead.")}static withServerTransition(e){return{ngModule:t,providers:[{provide:Eo,useValue:e.appId},{provide:sl,useExisting:Eo},il]}}}return t.\u0275fac=function(e){return new(e||t)(Mn(t,12))},t.\u0275mod=zt({type:t}),t.\u0275inj=ut({providers:Rl,imports:[Ja,_a]}),t})();function Ol(...t){let e=t[t.length-1];return E(e)?(t.pop(),D(t,e)):B(t)}function Il(t,e){return M(t,e,1)}function Pl(t,e){return function(n){return n.lift(new jl(t,e))}}"undefined"!=typeof window&&window;class jl{constructor(t,e){this.predicate=t,this.thisArg=e}call(t,e){return e.subscribe(new Nl(t,this.predicate,this.thisArg))}}class Nl extends f{constructor(t,e,n){super(t),this.predicate=e,this.thisArg=n,this.count=0}_next(t){let e;try{e=this.predicate.call(this.thisArg,t,this.count++)}catch(n){return void this.destination.error(n)}e&&this.destination.next(t)}}class Dl{}class Ul{}class Hl{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?this.lazyInit="string"==typeof t?()=>{this.headers=new Map,t.split("\n").forEach(t=>{const e=t.indexOf(":");if(e>0){const n=t.slice(0,e),r=n.toLowerCase(),s=t.slice(e+1).trim();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(s):this.headers.set(r,[s])}})}:()=>{this.headers=new Map,Object.keys(t).forEach(e=>{let n=t[e];const r=e.toLowerCase();"string"==typeof n&&(n=[n]),n.length>0&&(this.headers.set(r,n),this.maybeSetNormalizedName(e,r))})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();const e=this.headers.get(t.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,e){return this.clone({name:t,value:e,op:"a"})}set(t,e){return this.clone({name:t,value:e,op:"s"})}delete(t,e){return this.clone({name:t,value:e,op:"d"})}maybeSetNormalizedName(t,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,t)}init(){this.lazyInit&&(this.lazyInit instanceof Hl?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(e=>{this.headers.set(e,t.headers.get(e)),this.normalizedNames.set(e,t.normalizedNames.get(e))})}clone(t){const e=new Hl;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof Hl?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([t]),e}applyUpdate(t){const e=t.name.toLowerCase();switch(t.op){case"a":case"s":let n=t.value;if("string"==typeof n&&(n=[n]),0===n.length)return;this.maybeSetNormalizedName(t.name,e);const r=("a"===t.op?this.headers.get(e):void 0)||[];r.push(...n),this.headers.set(e,r);break;case"d":const s=t.value;if(s){let t=this.headers.get(e);if(!t)return;t=t.filter(t=>-1===s.indexOf(t)),0===t.length?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,t)}else this.headers.delete(e),this.normalizedNames.delete(e)}}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>t(this.normalizedNames.get(e),this.headers.get(e)))}}class Ll{encodeKey(t){return Fl(t)}encodeValue(t){return Fl(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}}function Fl(t){return encodeURIComponent(t).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/gi,"$").replace(/%2C/gi,",").replace(/%3B/gi,";").replace(/%2B/gi,"+").replace(/%3D/gi,"=").replace(/%3F/gi,"?").replace(/%2F/gi,"/")}class Ml{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new Ll,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function(t,e){const n=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(t=>{const r=t.indexOf("="),[s,i]=-1==r?[e.decodeKey(t),""]:[e.decodeKey(t.slice(0,r)),e.decodeValue(t.slice(r+1))],o=n.get(s)||[];o.push(i),n.set(s,o)}),n}(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(e=>{const n=t.fromObject[e];this.map.set(e,Array.isArray(n)?n:[n])})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();const e=this.map.get(t);return e?e[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,e){return this.clone({param:t,value:e,op:"a"})}appendAll(t){const e=[];return Object.keys(t).forEach(n=>{const r=t[n];Array.isArray(r)?r.forEach(t=>{e.push({param:n,value:t,op:"a"})}):e.push({param:n,value:r,op:"a"})}),this.clone(e)}set(t,e){return this.clone({param:t,value:e,op:"s"})}delete(t,e){return this.clone({param:t,value:e,op:"d"})}toString(){return this.init(),this.keys().map(t=>{const e=this.encoder.encodeKey(t);return this.map.get(t).map(t=>e+"="+this.encoder.encodeValue(t)).join("&")}).filter(t=>""!==t).join("&")}clone(t){const e=new Ml({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(t),e}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":const e=("a"===t.op?this.map.get(t.param):void 0)||[];e.push(t.value),this.map.set(t.param,e);break;case"d":if(void 0===t.value){this.map.delete(t.param);break}{let e=this.map.get(t.param)||[];const n=e.indexOf(t.value);-1!==n&&e.splice(n,1),e.length>0?this.map.set(t.param,e):this.map.delete(t.param)}}}),this.cloneFrom=this.updates=null)}}function Vl(t){return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer}function $l(t){return"undefined"!=typeof Blob&&t instanceof Blob}function zl(t){return"undefined"!=typeof FormData&&t instanceof FormData}class Bl{constructor(t,e,n,r){let s;if(this.url=e,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase(),function(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||r?(this.body=void 0!==n?n:null,s=r):s=n,s&&(this.reportProgress=!!s.reportProgress,this.withCredentials=!!s.withCredentials,s.responseType&&(this.responseType=s.responseType),s.headers&&(this.headers=s.headers),s.params&&(this.params=s.params)),this.headers||(this.headers=new Hl),this.params){const t=this.params.toString();if(0===t.length)this.urlWithParams=e;else{const n=e.indexOf("?");this.urlWithParams=e+(-1===n?"?":n<e.length-1?"&":"")+t}}else this.params=new Ml,this.urlWithParams=e}serializeBody(){return null===this.body?null:Vl(this.body)||$l(this.body)||zl(this.body)||"string"==typeof this.body?this.body:this.body instanceof Ml?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||zl(this.body)?null:$l(this.body)?this.body.type||null:Vl(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof Ml?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||"boolean"==typeof this.body?"application/json":null}clone(t={}){const e=t.method||this.method,n=t.url||this.url,r=t.responseType||this.responseType,s=void 0!==t.body?t.body:this.body,i=void 0!==t.withCredentials?t.withCredentials:this.withCredentials,o=void 0!==t.reportProgress?t.reportProgress:this.reportProgress;let a=t.headers||this.headers,l=t.params||this.params;return void 0!==t.setHeaders&&(a=Object.keys(t.setHeaders).reduce((e,n)=>e.set(n,t.setHeaders[n]),a)),t.setParams&&(l=Object.keys(t.setParams).reduce((e,n)=>e.set(n,t.setParams[n]),l)),new Bl(e,n,s,{params:l,headers:a,reportProgress:o,responseType:r,withCredentials:i})}}var ql=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}({});class Gl{constructor(t,e=200,n="OK"){this.headers=t.headers||new Hl,this.status=void 0!==t.status?t.status:e,this.statusText=t.statusText||n,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}}class Wl extends Gl{constructor(t={}){super(t),this.type=ql.ResponseHeader}clone(t={}){return new Wl({headers:t.headers||this.headers,status:void 0!==t.status?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}}class Zl extends Gl{constructor(t={}){super(t),this.type=ql.Response,this.body=void 0!==t.body?t.body:null}clone(t={}){return new Zl({body:void 0!==t.body?t.body:this.body,headers:t.headers||this.headers,status:void 0!==t.status?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}}class Ql extends Gl{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${t.url||"(unknown url)"}`:`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}}function Kl(t,e){return{body:e,headers:t.headers,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials}}let Jl=(()=>{class t{constructor(t){this.handler=t}request(t,e,n={}){let r;if(t instanceof Bl)r=t;else{let s,i;s=n.headers instanceof Hl?n.headers:new Hl(n.headers),n.params&&(i=n.params instanceof Ml?n.params:new Ml({fromObject:n.params})),r=new Bl(t,e,void 0!==n.body?n.body:null,{headers:s,params:i,reportProgress:n.reportProgress,responseType:n.responseType||"json",withCredentials:n.withCredentials})}const s=Ol(r).pipe(Il(t=>this.handler.handle(t)));if(t instanceof Bl||"events"===n.observe)return s;const i=s.pipe(Pl(t=>t instanceof Zl));switch(n.observe||"body"){case"body":switch(r.responseType){case"arraybuffer":return i.pipe(T(t=>{if(null!==t.body&&!(t.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return t.body}));case"blob":return i.pipe(T(t=>{if(null!==t.body&&!(t.body instanceof Blob))throw new Error("Response is not a Blob.");return t.body}));case"text":return i.pipe(T(t=>{if(null!==t.body&&"string"!=typeof t.body)throw new Error("Response is not a string.");return t.body}));case"json":default:return i.pipe(T(t=>t.body))}case"response":return i;default:throw new Error(`Unreachable: unhandled observe type ${n.observe}}`)}}delete(t,e={}){return this.request("DELETE",t,e)}get(t,e={}){return this.request("GET",t,e)}head(t,e={}){return this.request("HEAD",t,e)}jsonp(t,e){return this.request("JSONP",t,{params:(new Ml).append(e,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,e={}){return this.request("OPTIONS",t,e)}patch(t,e,n={}){return this.request("PATCH",t,Kl(n,e))}post(t,e,n={}){return this.request("POST",t,Kl(n,e))}put(t,e,n={}){return this.request("PUT",t,Kl(n,e))}}return t.\u0275fac=function(e){return new(e||t)(Mn(Dl))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();class Xl{constructor(t,e){this.next=t,this.interceptor=e}handle(t){return this.interceptor.intercept(t,this.next)}}const Yl=new kn("HTTP_INTERCEPTORS");let tu=(()=>{class t{intercept(t,e){return e.handle(t)}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();const eu=/^\)\]\}',?\n/;class nu{}let ru=(()=>{class t{constructor(){}build(){return new XMLHttpRequest}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),su=(()=>{class t{constructor(t){this.xhrFactory=t}handle(t){if("JSONP"===t.method)throw new Error("Attempted to construct Jsonp request without HttpClientJsonpModule installed.");return new v(e=>{const n=this.xhrFactory.build();if(n.open(t.method,t.urlWithParams),t.withCredentials&&(n.withCredentials=!0),t.headers.forEach((t,e)=>n.setRequestHeader(t,e.join(","))),t.headers.has("Accept")||n.setRequestHeader("Accept","application/json, text/plain, */*"),!t.headers.has("Content-Type")){const e=t.detectContentTypeHeader();null!==e&&n.setRequestHeader("Content-Type",e)}if(t.responseType){const e=t.responseType.toLowerCase();n.responseType="json"!==e?e:"text"}const r=t.serializeBody();let s=null;const i=()=>{if(null!==s)return s;const e=1223===n.status?204:n.status,r=n.statusText||"OK",i=new Hl(n.getAllResponseHeaders()),o=function(t){return"responseURL"in t&&t.responseURL?t.responseURL:/^X-Request-URL:/m.test(t.getAllResponseHeaders())?t.getResponseHeader("X-Request-URL"):null}(n)||t.url;return s=new Wl({headers:i,status:e,statusText:r,url:o}),s},o=()=>{let{headers:r,status:s,statusText:o,url:a}=i(),l=null;204!==s&&(l=void 0===n.response?n.responseText:n.response),0===s&&(s=l?200:0);let u=s>=200&&s<300;if("json"===t.responseType&&"string"==typeof l){const t=l;l=l.replace(eu,"");try{l=""!==l?JSON.parse(l):null}catch(c){l=t,u&&(u=!1,l={error:c,text:l})}}u?(e.next(new Zl({body:l,headers:r,status:s,statusText:o,url:a||void 0})),e.complete()):e.error(new Ql({error:l,headers:r,status:s,statusText:o,url:a||void 0}))},a=t=>{const{url:r}=i(),s=new Ql({error:t,status:n.status||0,statusText:n.statusText||"Unknown Error",url:r||void 0});e.error(s)};let l=!1;const u=r=>{l||(e.next(i()),l=!0);let s={type:ql.DownloadProgress,loaded:r.loaded};r.lengthComputable&&(s.total=r.total),"text"===t.responseType&&n.responseText&&(s.partialText=n.responseText),e.next(s)},c=t=>{let n={type:ql.UploadProgress,loaded:t.loaded};t.lengthComputable&&(n.total=t.total),e.next(n)};return n.addEventListener("load",o),n.addEventListener("error",a),n.addEventListener("timeout",a),n.addEventListener("abort",a),t.reportProgress&&(n.addEventListener("progress",u),null!==r&&n.upload&&n.upload.addEventListener("progress",c)),n.send(r),e.next({type:ql.Sent}),()=>{n.removeEventListener("error",a),n.removeEventListener("abort",a),n.removeEventListener("load",o),n.removeEventListener("timeout",a),t.reportProgress&&(n.removeEventListener("progress",u),null!==r&&n.upload&&n.upload.removeEventListener("progress",c)),n.readyState!==n.DONE&&n.abort()}})}}return t.\u0275fac=function(e){return new(e||t)(Mn(nu))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();const iu=new kn("XSRF_COOKIE_NAME"),ou=new kn("XSRF_HEADER_NAME");class au{}let lu=(()=>{class t{constructor(t,e,n){this.doc=t,this.platform=e,this.cookieName=n,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=za(t,this.cookieName),this.lastCookieString=t),this.lastToken}}return t.\u0275fac=function(e){return new(e||t)(Mn(Sa),Mn(Ao),Mn(iu))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),uu=(()=>{class t{constructor(t,e){this.tokenService=t,this.headerName=e}intercept(t,e){const n=t.url.toLowerCase();if("GET"===t.method||"HEAD"===t.method||n.startsWith("http://")||n.startsWith("https://"))return e.handle(t);const r=this.tokenService.getToken();return null===r||t.headers.has(this.headerName)||(t=t.clone({headers:t.headers.set(this.headerName,r)})),e.handle(t)}}return t.\u0275fac=function(e){return new(e||t)(Mn(au),Mn(ou))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),cu=(()=>{class t{constructor(t,e){this.backend=t,this.injector=e,this.chain=null}handle(t){if(null===this.chain){const t=this.injector.get(Yl,[]);this.chain=t.reduceRight((t,e)=>new Xl(t,e),this.backend)}return this.chain.handle(t)}}return t.\u0275fac=function(e){return new(e||t)(Mn(Ul),Mn($s))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),hu=(()=>{class t{static disable(){return{ngModule:t,providers:[{provide:uu,useClass:tu}]}}static withOptions(e={}){return{ngModule:t,providers:[e.cookieName?{provide:iu,useValue:e.cookieName}:[],e.headerName?{provide:ou,useValue:e.headerName}:[]]}}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=zt({type:t}),t.\u0275inj=ut({providers:[uu,{provide:Yl,useExisting:uu,multi:!0},{provide:au,useClass:lu},{provide:iu,useValue:"XSRF-TOKEN"},{provide:ou,useValue:"X-XSRF-TOKEN"}]}),t})(),du=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=zt({type:t}),t.\u0275inj=ut({providers:[Jl,{provide:Dl,useClass:cu},su,{provide:Ul,useExisting:su},ru,{provide:nu,useExisting:ru}],imports:[[hu.withOptions({cookieName:"XSRF-TOKEN",headerName:"X-XSRF-TOKEN"})]]}),t})();class pu extends C{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){const e=super._subscribe(t);return e&&!e.closed&&t.next(this._value),e}getValue(){if(this.hasError)throw this.thrownError;if(this.closed)throw new w;return this._value}next(t){super.next(this._value=t)}}class fu extends f{notifyNext(t,e,n,r,s){this.destination.next(e)}notifyError(t,e){this.destination.error(t)}notifyComplete(t){this.destination.complete()}}class gu extends f{constructor(t,e,n){super(),this.parent=t,this.outerValue=e,this.outerIndex=n,this.index=0}_next(t){this.parent.notifyNext(this.outerValue,t,this.outerIndex,this.index++,this)}_error(t){this.parent.notifyError(t,this),this.unsubscribe()}_complete(){this.parent.notifyComplete(this),this.unsubscribe()}}function mu(t,e,n,r,s=new gu(t,n,r)){if(!s.closed)return e instanceof v?e.subscribe(s):N(e)(s)}const yu={};class vu{constructor(t){this.resultSelector=t}call(t,e){return e.subscribe(new _u(t,this.resultSelector))}}class _u extends fu{constructor(t,e){super(t),this.resultSelector=e,this.active=0,this.values=[],this.observables=[]}_next(t){this.values.push(yu),this.observables.push(t)}_complete(){const t=this.observables,e=t.length;if(0===e)this.destination.complete();else{this.active=e,this.toRespond=e;for(let n=0;n<e;n++)this.add(mu(this,t[n],void 0,n))}}notifyComplete(t){0==(this.active-=1)&&this.destination.complete()}notifyNext(t,e,n){const r=this.values,s=this.toRespond?r[n]===yu?--this.toRespond:this.toRespond:0;r[n]=e,0===s&&(this.resultSelector?this._tryResultSelector(r):this.destination.next(r.slice()))}_tryResultSelector(t){let e;try{e=this.resultSelector.apply(this,t)}catch(n){return void this.destination.error(n)}this.destination.next(e)}}const wu=(()=>{function t(){return Error.call(this),this.message="no elements in sequence",this.name="EmptyError",this}return t.prototype=Object.create(Error.prototype),t})();function bu(...t){return z(1)(Ol(...t))}const Su=new v(t=>t.complete());function Cu(t){return t?function(t){return new v(e=>t.schedule(()=>e.complete()))}(t):Su}function xu(t){return new v(e=>{let n;try{n=t()}catch(r){return void e.error(r)}return(n?U(n):Cu()).subscribe(e)})}function Eu(t,e){return"function"==typeof e?n=>n.pipe(Eu((n,r)=>U(t(n,r)).pipe(T((t,s)=>e(n,t,r,s))))):e=>e.lift(new Tu(t))}class Tu{constructor(t){this.project=t}call(t,e){return e.subscribe(new ku(t,this.project))}}class ku extends L{constructor(t,e){super(t),this.project=e,this.index=0}_next(t){let e;const n=this.index++;try{e=this.project(t,n)}catch(r){return void this.destination.error(r)}this._innerSub(e)}_innerSub(t){const e=this.innerSubscription;e&&e.unsubscribe();const n=new H(this),r=this.destination;r.add(n),this.innerSubscription=F(t,n),this.innerSubscription!==n&&r.add(this.innerSubscription)}_complete(){const{innerSubscription:t}=this;t&&!t.closed||super._complete(),this.unsubscribe()}_unsubscribe(){this.innerSubscription=void 0}notifyComplete(){this.innerSubscription=void 0,this.isStopped&&super._complete()}notifyNext(t){this.destination.next(t)}}const Ru=(()=>{function t(){return Error.call(this),this.message="argument out of range",this.name="ArgumentOutOfRangeError",this}return t.prototype=Object.create(Error.prototype),t})();function Au(t){return e=>0===t?Cu():e.lift(new Ou(t))}class Ou{constructor(t){if(this.total=t,this.total<0)throw new Ru}call(t,e){return e.subscribe(new Iu(t,this.total))}}class Iu extends f{constructor(t,e){super(t),this.total=e,this.count=0}_next(t){const e=this.total,n=++this.count;n<=e&&(this.destination.next(t),n===e&&(this.destination.complete(),this.unsubscribe()))}}function Pu(t,e){let n=!1;return arguments.length>=2&&(n=!0),function(r){return r.lift(new ju(t,e,n))}}class ju{constructor(t,e,n=!1){this.accumulator=t,this.seed=e,this.hasSeed=n}call(t,e){return e.subscribe(new Nu(t,this.accumulator,this.seed,this.hasSeed))}}class Nu extends f{constructor(t,e,n,r){super(t),this.accumulator=e,this._seed=n,this.hasSeed=r,this.index=0}get seed(){return this._seed}set seed(t){this.hasSeed=!0,this._seed=t}_next(t){if(this.hasSeed)return this._tryNext(t);this.seed=t,this.destination.next(t)}_tryNext(t){const e=this.index++;let n;try{n=this.accumulator(this.seed,t,e)}catch(r){this.destination.error(r)}this.seed=n,this.destination.next(n)}}function Du(t){return function(e){const n=new Uu(t),r=e.lift(n);return n.caught=r}}class Uu{constructor(t){this.selector=t}call(t,e){return e.subscribe(new Hu(t,this.selector,this.caught))}}class Hu extends L{constructor(t,e,n){super(t),this.selector=e,this.caught=n}error(t){if(!this.isStopped){let n;try{n=this.selector(t,this.caught)}catch(e){return void super.error(e)}this._unsubscribeAndRecycle();const r=new H(this);this.add(r);const s=F(n,r);s!==r&&this.add(s)}}}function Lu(t){return function(e){return 0===t?Cu():e.lift(new Fu(t))}}class Fu{constructor(t){if(this.total=t,this.total<0)throw new Ru}call(t,e){return e.subscribe(new Mu(t,this.total))}}class Mu extends f{constructor(t,e){super(t),this.total=e,this.ring=new Array,this.count=0}_next(t){const e=this.ring,n=this.total,r=this.count++;e.length<n?e.push(t):e[r%n]=t}_complete(){const t=this.destination;let e=this.count;if(e>0){const n=this.count>=this.total?this.total:this.count,r=this.ring;for(let s=0;s<n;s++){const s=e++%n;t.next(r[s])}}t.complete()}}function Vu(t=Bu){return e=>e.lift(new $u(t))}class $u{constructor(t){this.errorFactory=t}call(t,e){return e.subscribe(new zu(t,this.errorFactory))}}class zu extends f{constructor(t,e){super(t),this.errorFactory=e,this.hasValue=!1}_next(t){this.hasValue=!0,this.destination.next(t)}_complete(){if(this.hasValue)return this.destination.complete();{let e;try{e=this.errorFactory()}catch(t){e=t}this.destination.error(e)}}}function Bu(){return new wu}function qu(t=null){return e=>e.lift(new Gu(t))}class Gu{constructor(t){this.defaultValue=t}call(t,e){return e.subscribe(new Wu(t,this.defaultValue))}}class Wu extends f{constructor(t,e){super(t),this.defaultValue=e,this.isEmpty=!0}_next(t){this.isEmpty=!1,this.destination.next(t)}_complete(){this.isEmpty&&this.destination.next(this.defaultValue),this.destination.complete()}}function Zu(t,e){const n=arguments.length>=2;return r=>r.pipe(t?Pl((e,n)=>t(e,n,r)):y,Au(1),n?qu(e):Vu(()=>new wu))}function Qu(){}function Ku(t,e,n){return function(r){return r.lift(new Ju(t,e,n))}}class Ju{constructor(t,e,n){this.nextOrObserver=t,this.error=e,this.complete=n}call(t,e){return e.subscribe(new Xu(t,this.nextOrObserver,this.error,this.complete))}}class Xu extends f{constructor(t,e,n,s){super(t),this._tapNext=Qu,this._tapError=Qu,this._tapComplete=Qu,this._tapError=n||Qu,this._tapComplete=s||Qu,r(e)?(this._context=this,this._tapNext=e):e&&(this._context=e,this._tapNext=e.next||Qu,this._tapError=e.error||Qu,this._tapComplete=e.complete||Qu)}_next(t){try{this._tapNext.call(this._context,t)}catch(e){return void this.destination.error(e)}this.destination.next(t)}_error(t){try{this._tapError.call(this._context,t)}catch(t){return void this.destination.error(t)}this.destination.error(t)}_complete(){try{this._tapComplete.call(this._context)}catch(t){return void this.destination.error(t)}return this.destination.complete()}}class Yu{constructor(t){this.callback=t}call(t,e){return e.subscribe(new tc(t,this.callback))}}class tc extends f{constructor(t,e){super(t),this.add(new h(e))}}class ec{constructor(t,e){this.id=t,this.url=e}}class nc extends ec{constructor(t,e,n="imperative",r=null){super(t,e),this.navigationTrigger=n,this.restoredState=r}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class rc extends ec{constructor(t,e,n){super(t,e),this.urlAfterRedirects=n}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class sc extends ec{constructor(t,e,n){super(t,e),this.reason=n}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class ic extends ec{constructor(t,e,n){super(t,e),this.error=n}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class oc extends ec{constructor(t,e,n,r){super(t,e),this.urlAfterRedirects=n,this.state=r}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class ac extends ec{constructor(t,e,n,r){super(t,e),this.urlAfterRedirects=n,this.state=r}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class lc extends ec{constructor(t,e,n,r,s){super(t,e),this.urlAfterRedirects=n,this.state=r,this.shouldActivate=s}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class uc extends ec{constructor(t,e,n,r){super(t,e),this.urlAfterRedirects=n,this.state=r}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class cc extends ec{constructor(t,e,n,r){super(t,e),this.urlAfterRedirects=n,this.state=r}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class hc{constructor(t){this.route=t}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class dc{constructor(t){this.route=t}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class pc{constructor(t){this.snapshot=t}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class fc{constructor(t){this.snapshot=t}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class gc{constructor(t){this.snapshot=t}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class mc{constructor(t){this.snapshot=t}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class yc{constructor(t,e,n){this.routerEvent=t,this.position=e,this.anchor=n}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}const vc="primary";class _c{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){const e=this.params[t];return Array.isArray(e)?e[0]:e}return null}getAll(t){if(this.has(t)){const e=this.params[t];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}}function wc(t){return new _c(t)}function bc(t){const e=Error("NavigationCancelingError: "+t);return e.ngNavigationCancelingError=!0,e}function Sc(t,e,n){const r=n.path.split("/");if(r.length>t.length)return null;if("full"===n.pathMatch&&(e.hasChildren()||r.length<t.length))return null;const s={};for(let i=0;i<r.length;i++){const e=r[i],n=t[i];if(e.startsWith(":"))s[e.substring(1)]=n;else if(e!==n.path)return null}return{consumed:t.slice(0,r.length),posParams:s}}function Cc(t,e){const n=t?Object.keys(t):void 0,r=e?Object.keys(e):void 0;if(!n||!r||n.length!=r.length)return!1;let s;for(let i=0;i<n.length;i++)if(s=n[i],!xc(t[s],e[s]))return!1;return!0}function xc(t,e){if(Array.isArray(t)&&Array.isArray(e)){if(t.length!==e.length)return!1;const n=[...t].sort(),r=[...e].sort();return n.every((t,e)=>r[e]===t)}return t===e}function Ec(t){return Array.prototype.concat.apply([],t)}function Tc(t){return t.length>0?t[t.length-1]:null}function kc(t,e){for(const n in t)t.hasOwnProperty(n)&&e(t[n],n)}function Rc(t){return(e=t)&&"function"==typeof e.subscribe?t:ni(t)?U(Promise.resolve(t)):Ol(t);var e}function Ac(t,e,n){return n?function(t,e){return Cc(t,e)}(t.queryParams,e.queryParams)&&Oc(t.root,e.root):function(t,e){return Object.keys(e).length<=Object.keys(t).length&&Object.keys(e).every(n=>xc(t[n],e[n]))}(t.queryParams,e.queryParams)&&Ic(t.root,e.root)}function Oc(t,e){if(!Uc(t.segments,e.segments))return!1;if(t.numberOfChildren!==e.numberOfChildren)return!1;for(const n in e.children){if(!t.children[n])return!1;if(!Oc(t.children[n],e.children[n]))return!1}return!0}function Ic(t,e){return Pc(t,e,e.segments)}function Pc(t,e,n){if(t.segments.length>n.length)return!!Uc(t.segments.slice(0,n.length),n)&&!e.hasChildren();if(t.segments.length===n.length){if(!Uc(t.segments,n))return!1;for(const n in e.children){if(!t.children[n])return!1;if(!Ic(t.children[n],e.children[n]))return!1}return!0}{const r=n.slice(0,t.segments.length),s=n.slice(t.segments.length);return!!Uc(t.segments,r)&&!!t.children.primary&&Pc(t.children.primary,e,s)}}class jc{constructor(t,e,n){this.root=t,this.queryParams=e,this.fragment=n}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=wc(this.queryParams)),this._queryParamMap}toString(){return Fc.serialize(this)}}class Nc{constructor(t,e){this.segments=t,this.children=e,this.parent=null,kc(e,(t,e)=>t.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return Mc(this)}}class Dc{constructor(t,e){this.path=t,this.parameters=e}get parameterMap(){return this._parameterMap||(this._parameterMap=wc(this.parameters)),this._parameterMap}toString(){return Wc(this)}}function Uc(t,e){return t.length===e.length&&t.every((t,n)=>t.path===e[n].path)}class Hc{}class Lc{parse(t){const e=new Xc(t);return new jc(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(t){var e;return`/${Vc(t.root,!0)}${function(t){const e=Object.keys(t).map(e=>{const n=t[e];return Array.isArray(n)?n.map(t=>`${zc(e)}=${zc(t)}`).join("&"):`${zc(e)}=${zc(n)}`});return e.length?`?${e.join("&")}`:""}(t.queryParams)}${"string"==typeof t.fragment?`#${e=t.fragment,encodeURI(e)}`:""}`}}const Fc=new Lc;function Mc(t){return t.segments.map(t=>Wc(t)).join("/")}function Vc(t,e){if(!t.hasChildren())return Mc(t);if(e){const e=t.children.primary?Vc(t.children.primary,!1):"",n=[];return kc(t.children,(t,e)=>{e!==vc&&n.push(`${e}:${Vc(t,!1)}`)}),n.length>0?`${e}(${n.join("//")})`:e}{const e=function(t,e){let n=[];return kc(t.children,(t,r)=>{r===vc&&(n=n.concat(e(t,r)))}),kc(t.children,(t,r)=>{r!==vc&&(n=n.concat(e(t,r)))}),n}(t,(e,n)=>n===vc?[Vc(t.children.primary,!1)]:[`${n}:${Vc(e,!1)}`]);return 1===Object.keys(t.children).length&&null!=t.children.primary?`${Mc(t)}/${e[0]}`:`${Mc(t)}/(${e.join("//")})`}}function $c(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function zc(t){return $c(t).replace(/%3B/gi,";")}function Bc(t){return $c(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function qc(t){return decodeURIComponent(t)}function Gc(t){return qc(t.replace(/\+/g,"%20"))}function Wc(t){return`${Bc(t.path)}${e=t.parameters,Object.keys(e).map(t=>`;${Bc(t)}=${Bc(e[t])}`).join("")}`;var e}const Zc=/^[^\/()?;=#]+/;function Qc(t){const e=t.match(Zc);return e?e[0]:""}const Kc=/^[^=?&#]+/,Jc=/^[^?&#]+/;class Xc{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new Nc([],{}):new Nc([],this.parseChildren())}parseQueryParams(){const t={};if(this.consumeOptional("?"))do{this.parseQueryParam(t)}while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(t.length>0||Object.keys(e).length>0)&&(n.primary=new Nc(t,e)),n}parseSegment(){const t=Qc(this.remaining);if(""===t&&this.peekStartsWith(";"))throw new Error(`Empty path url segment cannot have parameters: '${this.remaining}'.`);return this.capture(t),new Dc(qc(t),this.parseMatrixParams())}parseMatrixParams(){const t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){const e=Qc(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){const t=Qc(this.remaining);t&&(n=t,this.capture(n))}t[qc(e)]=qc(n)}parseQueryParam(t){const e=function(t){const e=t.match(Kc);return e?e[0]:""}(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){const t=function(t){const e=t.match(Jc);return e?e[0]:""}(this.remaining);t&&(n=t,this.capture(n))}const r=Gc(e),s=Gc(n);if(t.hasOwnProperty(r)){let e=t[r];Array.isArray(e)||(e=[e],t[r]=e),e.push(s)}else t[r]=s}parseParens(t){const e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const n=Qc(this.remaining),r=this.remaining[n.length];if("/"!==r&&")"!==r&&";"!==r)throw new Error(`Cannot parse url '${this.url}'`);let s;n.indexOf(":")>-1?(s=n.substr(0,n.indexOf(":")),this.capture(s),this.capture(":")):t&&(s=vc);const i=this.parseChildren();e[s]=1===Object.keys(i).length?i.primary:new Nc([],i),this.consumeOptional("//")}return e}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return!!this.peekStartsWith(t)&&(this.remaining=this.remaining.substring(t.length),!0)}capture(t){if(!this.consumeOptional(t))throw new Error(`Expected "${t}".`)}}class Yc{constructor(t){this._root=t}get root(){return this._root.value}parent(t){const e=this.pathFromRoot(t);return e.length>1?e[e.length-2]:null}children(t){const e=th(t,this._root);return e?e.children.map(t=>t.value):[]}firstChild(t){const e=th(t,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(t){const e=eh(t,this._root);return e.length<2?[]:e[e.length-2].children.map(t=>t.value).filter(e=>e!==t)}pathFromRoot(t){return eh(t,this._root).map(t=>t.value)}}function th(t,e){if(t===e.value)return e;for(const n of e.children){const e=th(t,n);if(e)return e}return null}function eh(t,e){if(t===e.value)return[e];for(const n of e.children){const r=eh(t,n);if(r.length)return r.unshift(e),r}return[]}class nh{constructor(t,e){this.value=t,this.children=e}toString(){return`TreeNode(${this.value})`}}function rh(t){const e={};return t&&t.children.forEach(t=>e[t.value.outlet]=t),e}class sh extends Yc{constructor(t,e){super(t),this.snapshot=e,ch(this,t)}toString(){return this.snapshot.toString()}}function ih(t,e){const n=function(t,e){const n=new lh([],{},{},"",{},vc,e,null,t.root,-1,{});return new uh("",new nh(n,[]))}(t,e),r=new pu([new Dc("",{})]),s=new pu({}),i=new pu({}),o=new pu({}),a=new pu(""),l=new oh(r,s,o,a,i,vc,e,n.root);return l.snapshot=n.root,new sh(new nh(l,[]),n)}class oh{constructor(t,e,n,r,s,i,o,a){this.url=t,this.params=e,this.queryParams=n,this.fragment=r,this.data=s,this.outlet=i,this.component=o,this._futureSnapshot=a}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe(T(t=>wc(t)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe(T(t=>wc(t)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function ah(t,e="emptyOnly"){const n=t.pathFromRoot;let r=0;if("always"!==e)for(r=n.length-1;r>=1;){const t=n[r],e=n[r-1];if(t.routeConfig&&""===t.routeConfig.path)r--;else{if(e.component)break;r--}}return function(t){return t.reduce((t,e)=>({params:Object.assign(Object.assign({},t.params),e.params),data:Object.assign(Object.assign({},t.data),e.data),resolve:Object.assign(Object.assign({},t.resolve),e._resolvedData)}),{params:{},data:{},resolve:{}})}(n.slice(r))}class lh{constructor(t,e,n,r,s,i,o,a,l,u,c){this.url=t,this.params=e,this.queryParams=n,this.fragment=r,this.data=s,this.outlet=i,this.component=o,this.routeConfig=a,this._urlSegment=l,this._lastPathIndex=u,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=wc(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=wc(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(t=>t.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class uh extends Yc{constructor(t,e){super(e),this.url=t,ch(this,e)}toString(){return hh(this._root)}}function ch(t,e){e.value._routerState=t,e.children.forEach(e=>ch(t,e))}function hh(t){const e=t.children.length>0?` { ${t.children.map(hh).join(", ")} } `:"";return`${t.value}${e}`}function dh(t){if(t.snapshot){const e=t.snapshot,n=t._futureSnapshot;t.snapshot=n,Cc(e.queryParams,n.queryParams)||t.queryParams.next(n.queryParams),e.fragment!==n.fragment&&t.fragment.next(n.fragment),Cc(e.params,n.params)||t.params.next(n.params),function(t,e){if(t.length!==e.length)return!1;for(let n=0;n<t.length;++n)if(!Cc(t[n],e[n]))return!1;return!0}(e.url,n.url)||t.url.next(n.url),Cc(e.data,n.data)||t.data.next(n.data)}else t.snapshot=t._futureSnapshot,t.data.next(t._futureSnapshot.data)}function ph(t,e){var n,r;return Cc(t.params,e.params)&&Uc(n=t.url,r=e.url)&&n.every((t,e)=>Cc(t.parameters,r[e].parameters))&&!(!t.parent!=!e.parent)&&(!t.parent||ph(t.parent,e.parent))}function fh(t,e,n){if(n&&t.shouldReuseRoute(e.value,n.value.snapshot)){const r=n.value;r._futureSnapshot=e.value;const s=function(t,e,n){return e.children.map(e=>{for(const r of n.children)if(t.shouldReuseRoute(e.value,r.value.snapshot))return fh(t,e,r);return fh(t,e)})}(t,e,n);return new nh(r,s)}{if(t.shouldAttach(e.value)){const n=t.retrieve(e.value);if(null!==n){const t=n.route;return gh(e,t),t}}const n=new oh(new pu((r=e.value).url),new pu(r.params),new pu(r.queryParams),new pu(r.fragment),new pu(r.data),r.outlet,r.component,r),s=e.children.map(e=>fh(t,e));return new nh(n,s)}var r}function gh(t,e){if(t.value.routeConfig!==e.value.routeConfig)throw new Error("Cannot reattach ActivatedRouteSnapshot created from a different route");if(t.children.length!==e.children.length)throw new Error("Cannot reattach ActivatedRouteSnapshot with a different number of children");e.value._futureSnapshot=t.value;for(let n=0;n<t.children.length;++n)gh(t.children[n],e.children[n])}function mh(t){return"object"==typeof t&&null!=t&&!t.outlets&&!t.segmentPath}function yh(t){return"object"==typeof t&&null!=t&&t.outlets}function vh(t,e,n,r,s){let i={};return r&&kc(r,(t,e)=>{i[e]=Array.isArray(t)?t.map(t=>`${t}`):`${t}`}),new jc(n.root===t?e:_h(n.root,t,e),i,s)}function _h(t,e,n){const r={};return kc(t.children,(t,s)=>{r[s]=t===e?n:_h(t,e,n)}),new Nc(t.segments,r)}class wh{constructor(t,e,n){if(this.isAbsolute=t,this.numberOfDoubleDots=e,this.commands=n,t&&n.length>0&&mh(n[0]))throw new Error("Root segment cannot have matrix parameters");const r=n.find(yh);if(r&&r!==Tc(n))throw new Error("{outlets:{}} has to be the last command")}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class bh{constructor(t,e,n){this.segmentGroup=t,this.processChildren=e,this.index=n}}function Sh(t,e,n){if(t||(t=new Nc([],{})),0===t.segments.length&&t.hasChildren())return Ch(t,e,n);const r=function(t,e,n){let r=0,s=e;const i={match:!1,pathIndex:0,commandIndex:0};for(;s<t.segments.length;){if(r>=n.length)return i;const e=t.segments[s],o=n[r];if(yh(o))break;const a=`${o}`,l=r<n.length-1?n[r+1]:null;if(s>0&&void 0===a)break;if(a&&l&&"object"==typeof l&&void 0===l.outlets){if(!kh(a,l,e))return i;r+=2}else{if(!kh(a,{},e))return i;r++}s++}return{match:!0,pathIndex:s,commandIndex:r}}(t,e,n),s=n.slice(r.commandIndex);if(r.match&&r.pathIndex<t.segments.length){const e=new Nc(t.segments.slice(0,r.pathIndex),{});return e.children.primary=new Nc(t.segments.slice(r.pathIndex),t.children),Ch(e,0,s)}return r.match&&0===s.length?new Nc(t.segments,{}):r.match&&!t.hasChildren()?xh(t,e,n):r.match?Ch(t,0,s):xh(t,e,n)}function Ch(t,e,n){if(0===n.length)return new Nc(t.segments,{});{const r=function(t){return yh(t[0])?t[0].outlets:{[vc]:t}}(n),s={};return kc(r,(n,r)=>{"string"==typeof n&&(n=[n]),null!==n&&(s[r]=Sh(t.children[r],e,n))}),kc(t.children,(t,e)=>{void 0===r[e]&&(s[e]=t)}),new Nc(t.segments,s)}}function xh(t,e,n){const r=t.segments.slice(0,e);let s=0;for(;s<n.length;){const i=n[s];if(yh(i)){const t=Eh(i.outlets);return new Nc(r,t)}if(0===s&&mh(n[0])){r.push(new Dc(t.segments[e].path,Th(n[0]))),s++;continue}const o=yh(i)?i.outlets.primary:`${i}`,a=s<n.length-1?n[s+1]:null;o&&a&&mh(a)?(r.push(new Dc(o,Th(a))),s+=2):(r.push(new Dc(o,{})),s++)}return new Nc(r,{})}function Eh(t){const e={};return kc(t,(t,n)=>{"string"==typeof t&&(t=[t]),null!==t&&(e[n]=xh(new Nc([],{}),0,t))}),e}function Th(t){const e={};return kc(t,(t,n)=>e[n]=`${t}`),e}function kh(t,e,n){return t==n.path&&Cc(e,n.parameters)}class Rh{constructor(t,e,n,r){this.routeReuseStrategy=t,this.futureState=e,this.currState=n,this.forwardEvent=r}activate(t){const e=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,n,t),dh(this.futureState.root),this.activateChildRoutes(e,n,t)}deactivateChildRoutes(t,e,n){const r=rh(e);t.children.forEach(t=>{const e=t.value.outlet;this.deactivateRoutes(t,r[e],n),delete r[e]}),kc(r,(t,e)=>{this.deactivateRouteAndItsChildren(t,n)})}deactivateRoutes(t,e,n){const r=t.value,s=e?e.value:null;if(r===s)if(r.component){const s=n.getContext(r.outlet);s&&this.deactivateChildRoutes(t,e,s.children)}else this.deactivateChildRoutes(t,e,n);else s&&this.deactivateRouteAndItsChildren(e,n)}deactivateRouteAndItsChildren(t,e){this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,e):this.deactivateRouteAndOutlet(t,e)}detachAndStoreRouteSubtree(t,e){const n=e.getContext(t.value.outlet);if(n&&n.outlet){const e=n.outlet.detach(),r=n.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:e,route:t,contexts:r})}}deactivateRouteAndOutlet(t,e){const n=e.getContext(t.value.outlet),r=n&&t.value.component?n.children:e,s=rh(t);for(const i of Object.keys(s))this.deactivateRouteAndItsChildren(s[i],r);n&&n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated(),n.attachRef=null,n.resolver=null,n.route=null)}activateChildRoutes(t,e,n){const r=rh(e);t.children.forEach(t=>{this.activateRoutes(t,r[t.value.outlet],n),this.forwardEvent(new mc(t.value.snapshot))}),t.children.length&&this.forwardEvent(new fc(t.value.snapshot))}activateRoutes(t,e,n){const r=t.value,s=e?e.value:null;if(dh(r),r===s)if(r.component){const s=n.getOrCreateContext(r.outlet);this.activateChildRoutes(t,e,s.children)}else this.activateChildRoutes(t,e,n);else if(r.component){const e=n.getOrCreateContext(r.outlet);if(this.routeReuseStrategy.shouldAttach(r.snapshot)){const t=this.routeReuseStrategy.retrieve(r.snapshot);this.routeReuseStrategy.store(r.snapshot,null),e.children.onOutletReAttached(t.contexts),e.attachRef=t.componentRef,e.route=t.route.value,e.outlet&&e.outlet.attach(t.componentRef,t.route.value),Ah(t.route)}else{const n=function(t){for(let e=t.parent;e;e=e.parent){const t=e.routeConfig;if(t&&t._loadedConfig)return t._loadedConfig;if(t&&t.component)return null}return null}(r.snapshot),s=n?n.module.componentFactoryResolver:null;e.attachRef=null,e.route=r,e.resolver=s,e.outlet&&e.outlet.activateWith(r,s),this.activateChildRoutes(t,null,e.children)}}else this.activateChildRoutes(t,null,n)}}function Ah(t){dh(t.value),t.children.forEach(Ah)}class Oh{constructor(t,e){this.routes=t,this.module=e}}function Ih(t){return"function"==typeof t}function Ph(t){return t instanceof jc}const jh=Symbol("INITIAL_VALUE");function Nh(){return Eu(t=>function(...t){let e,n;return E(t[t.length-1])&&(n=t.pop()),"function"==typeof t[t.length-1]&&(e=t.pop()),1===t.length&&l(t[0])&&(t=t[0]),B(t,n).lift(new vu(e))}(t.map(t=>t.pipe(Au(1),function(...t){const e=t[t.length-1];return E(e)?(t.pop(),n=>bu(t,n,e)):e=>bu(t,e)}(jh)))).pipe(Pu((t,e)=>{let n=!1;return e.reduce((t,r,s)=>{if(t!==jh)return t;if(r===jh&&(n=!0),!n){if(!1===r)return r;if(s===e.length-1||Ph(r))return r}return t},t)},jh),Pl(t=>t!==jh),T(t=>Ph(t)?t:!0===t),Au(1)))}let Dh=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=Ft({type:t,selectors:[["ng-component"]],decls:1,vars:0,template:function(t,e){1&t&&ei(0,"router-outlet")},directives:function(){return[kd]},encapsulation:2}),t})();function Uh(t,e=""){for(let n=0;n<t.length;n++){const r=t[n];Hh(r,Lh(e,r))}}function Hh(t,e){t.children&&Uh(t.children,e)}function Lh(t,e){return e?t||e.path?t&&!e.path?`${t}/`:!t&&e.path?e.path:`${t}/${e.path}`:"":t}function Fh(t){const e=t.children&&t.children.map(Fh),n=e?Object.assign(Object.assign({},t),{children:e}):Object.assign({},t);return!n.component&&(e||n.loadChildren)&&n.outlet&&n.outlet!==vc&&(n.component=Dh),n}function Mh(t){return t.outlet||vc}function Vh(t,e){const n=t.filter(t=>Mh(t)===e);return n.push(...t.filter(t=>Mh(t)!==e)),n}const $h={matched:!1,consumedSegments:[],lastChild:0,parameters:{},positionalParamSegments:{}};function zh(t,e,n){var r;if(""===e.path)return"full"===e.pathMatch&&(t.hasChildren()||n.length>0)?Object.assign({},$h):{matched:!0,consumedSegments:[],lastChild:0,parameters:{},positionalParamSegments:{}};const s=(e.matcher||Sc)(n,t,e);if(!s)return Object.assign({},$h);const i={};kc(s.posParams,(t,e)=>{i[e]=t.path});const o=s.consumed.length>0?Object.assign(Object.assign({},i),s.consumed[s.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:s.consumed,lastChild:s.consumed.length,parameters:o,positionalParamSegments:null!==(r=s.posParams)&&void 0!==r?r:{}}}function Bh(t,e,n,r,s="corrected"){if(n.length>0&&function(t,e,n){return n.some(n=>qh(t,e,n)&&Mh(n)!==vc)}(t,n,r)){const s=new Nc(e,function(t,e,n,r){const s={};s.primary=r,r._sourceSegment=t,r._segmentIndexShift=e.length;for(const i of n)if(""===i.path&&Mh(i)!==vc){const n=new Nc([],{});n._sourceSegment=t,n._segmentIndexShift=e.length,s[Mh(i)]=n}return s}(t,e,r,new Nc(n,t.children)));return s._sourceSegment=t,s._segmentIndexShift=e.length,{segmentGroup:s,slicedSegments:[]}}if(0===n.length&&function(t,e,n){return n.some(n=>qh(t,e,n))}(t,n,r)){const i=new Nc(t.segments,function(t,e,n,r,s,i){const o={};for(const a of r)if(qh(t,n,a)&&!s[Mh(a)]){const n=new Nc([],{});n._sourceSegment=t,n._segmentIndexShift="legacy"===i?t.segments.length:e.length,o[Mh(a)]=n}return Object.assign(Object.assign({},s),o)}(t,e,n,r,t.children,s));return i._sourceSegment=t,i._segmentIndexShift=e.length,{segmentGroup:i,slicedSegments:n}}const i=new Nc(t.segments,t.children);return i._sourceSegment=t,i._segmentIndexShift=e.length,{segmentGroup:i,slicedSegments:n}}function qh(t,e,n){return(!(t.hasChildren()||e.length>0)||"full"!==n.pathMatch)&&""===n.path}function Gh(t,e,n,r){return!!(Mh(t)===r||r!==vc&&qh(e,n,t))&&("**"===t.path||zh(e,t,n).matched)}function Wh(t,e,n){return 0===e.length&&!t.children[n]}class Zh{constructor(t){this.segmentGroup=t||null}}class Qh{constructor(t){this.urlTree=t}}function Kh(t){return new v(e=>e.error(new Zh(t)))}function Jh(t){return new v(e=>e.error(new Qh(t)))}function Xh(t){return new v(e=>e.error(new Error(`Only absolute redirects can have named outlets. redirectTo: '${t}'`)))}class Yh{constructor(t,e,n,r,s){this.configLoader=e,this.urlSerializer=n,this.urlTree=r,this.config=s,this.allowRedirects=!0,this.ngModule=t.get(ro)}apply(){const t=Bh(this.urlTree.root,[],[],this.config).segmentGroup,e=new Nc(t.segments,t.children);return this.expandSegmentGroup(this.ngModule,this.config,e,vc).pipe(T(t=>this.createUrlTree(td(t),this.urlTree.queryParams,this.urlTree.fragment))).pipe(Du(t=>{if(t instanceof Qh)return this.allowRedirects=!1,this.match(t.urlTree);if(t instanceof Zh)throw this.noMatchError(t);throw t}))}match(t){return this.expandSegmentGroup(this.ngModule,this.config,t.root,vc).pipe(T(e=>this.createUrlTree(td(e),t.queryParams,t.fragment))).pipe(Du(t=>{if(t instanceof Zh)throw this.noMatchError(t);throw t}))}noMatchError(t){return new Error(`Cannot match any routes. URL Segment: '${t.segmentGroup}'`)}createUrlTree(t,e,n){const r=t.segments.length>0?new Nc([],{[vc]:t}):t;return new jc(r,e,n)}expandSegmentGroup(t,e,n,r){return 0===n.segments.length&&n.hasChildren()?this.expandChildren(t,e,n).pipe(T(t=>new Nc([],t))):this.expandSegment(t,n,e,n.segments,r,!0)}expandChildren(t,e,n){const r=[];for(const s of Object.keys(n.children))"primary"===s?r.unshift(s):r.push(s);return U(r).pipe(Il(r=>{const s=n.children[r],i=Vh(e,r);return this.expandSegmentGroup(t,i,s,r).pipe(T(t=>({segment:t,outlet:r})))}),Pu((t,e)=>(t[e.outlet]=e.segment,t),{}),function(t,e){const n=arguments.length>=2;return r=>r.pipe(t?Pl((e,n)=>t(e,n,r)):y,Lu(1),n?qu(e):Vu(()=>new wu))}())}expandSegment(t,e,n,r,s,i){return U(n).pipe(Il(o=>this.expandSegmentAgainstRoute(t,e,n,o,r,s,i).pipe(Du(t=>{if(t instanceof Zh)return Ol(null);throw t}))),Zu(t=>!!t),Du((t,n)=>{if(t instanceof wu||"EmptyError"===t.name){if(Wh(e,r,s))return Ol(new Nc([],{}));throw new Zh(e)}throw t}))}expandSegmentAgainstRoute(t,e,n,r,s,i,o){return Gh(r,e,s,i)?void 0===r.redirectTo?this.matchSegmentAgainstRoute(t,e,r,s,i):o&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(t,e,n,r,s,i):Kh(e):Kh(e)}expandSegmentAgainstRouteUsingRedirect(t,e,n,r,s,i){return"**"===r.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(t,n,r,i):this.expandRegularSegmentAgainstRouteUsingRedirect(t,e,n,r,s,i)}expandWildCardWithParamsAgainstRouteUsingRedirect(t,e,n,r){const s=this.applyRedirectCommands([],n.redirectTo,{});return n.redirectTo.startsWith("/")?Jh(s):this.lineralizeSegments(n,s).pipe(M(n=>{const s=new Nc(n,{});return this.expandSegment(t,s,e,n,r,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(t,e,n,r,s,i){const{matched:o,consumedSegments:a,lastChild:l,positionalParamSegments:u}=zh(e,r,s);if(!o)return Kh(e);const c=this.applyRedirectCommands(a,r.redirectTo,u);return r.redirectTo.startsWith("/")?Jh(c):this.lineralizeSegments(r,c).pipe(M(r=>this.expandSegment(t,e,n,r.concat(s.slice(l)),i,!1)))}matchSegmentAgainstRoute(t,e,n,r,s){if("**"===n.path)return n.loadChildren?(n._loadedConfig?Ol(n._loadedConfig):this.configLoader.load(t.injector,n)).pipe(T(t=>(n._loadedConfig=t,new Nc(r,{})))):Ol(new Nc(r,{}));const{matched:i,consumedSegments:o,lastChild:a}=zh(e,n,r);if(!i)return Kh(e);const l=r.slice(a);return this.getChildConfig(t,n,r).pipe(M(t=>{const r=t.module,i=t.routes,{segmentGroup:a,slicedSegments:u}=Bh(e,o,l,i),c=new Nc(a.segments,a.children);if(0===u.length&&c.hasChildren())return this.expandChildren(r,i,c).pipe(T(t=>new Nc(o,t)));if(0===i.length&&0===u.length)return Ol(new Nc(o,{}));const h=Mh(n)===s;return this.expandSegment(r,c,i,u,h?vc:s,!0).pipe(T(t=>new Nc(o.concat(t.segments),t.children)))}))}getChildConfig(t,e,n){return e.children?Ol(new Oh(e.children,t)):e.loadChildren?void 0!==e._loadedConfig?Ol(e._loadedConfig):this.runCanLoadGuards(t.injector,e,n).pipe(M(n=>n?this.configLoader.load(t.injector,e).pipe(T(t=>(e._loadedConfig=t,t))):function(t){return new v(e=>e.error(bc(`Cannot load children because the guard of the route "path: '${t.path}'" returned false`)))}(e))):Ol(new Oh([],t))}runCanLoadGuards(t,e,n){const r=e.canLoad;return r&&0!==r.length?Ol(r.map(r=>{const s=t.get(r);let i;if(function(t){return t&&Ih(t.canLoad)}(s))i=s.canLoad(e,n);else{if(!Ih(s))throw new Error("Invalid CanLoad guard");i=s(e,n)}return Rc(i)})).pipe(Nh(),Ku(t=>{if(!Ph(t))return;const e=bc(`Redirecting to "${this.urlSerializer.serialize(t)}"`);throw e.url=t,e}),T(t=>!0===t)):Ol(!0)}lineralizeSegments(t,e){let n=[],r=e.root;for(;;){if(n=n.concat(r.segments),0===r.numberOfChildren)return Ol(n);if(r.numberOfChildren>1||!r.children.primary)return Xh(t.redirectTo);r=r.children.primary}}applyRedirectCommands(t,e,n){return this.applyRedirectCreatreUrlTree(e,this.urlSerializer.parse(e),t,n)}applyRedirectCreatreUrlTree(t,e,n,r){const s=this.createSegmentGroup(t,e.root,n,r);return new jc(s,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(t,e){const n={};return kc(t,(t,r)=>{if("string"==typeof t&&t.startsWith(":")){const s=t.substring(1);n[r]=e[s]}else n[r]=t}),n}createSegmentGroup(t,e,n,r){const s=this.createSegments(t,e.segments,n,r);let i={};return kc(e.children,(e,s)=>{i[s]=this.createSegmentGroup(t,e,n,r)}),new Nc(s,i)}createSegments(t,e,n,r){return e.map(e=>e.path.startsWith(":")?this.findPosParam(t,e,r):this.findOrReturn(e,n))}findPosParam(t,e,n){const r=n[e.path.substring(1)];if(!r)throw new Error(`Cannot redirect to '${t}'. Cannot find '${e.path}'.`);return r}findOrReturn(t,e){let n=0;for(const r of e){if(r.path===t.path)return e.splice(n),r;n++}return t}}function td(t){const e={};for(const n of Object.keys(t.children)){const r=td(t.children[n]);(r.segments.length>0||r.hasChildren())&&(e[n]=r)}return function(t){if(1===t.numberOfChildren&&t.children.primary){const e=t.children.primary;return new Nc(t.segments.concat(e.segments),e.children)}return t}(new Nc(t.segments,e))}class ed{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}}class nd{constructor(t,e){this.component=t,this.route=e}}function rd(t,e,n){const r=t._root;return id(r,e?e._root:null,n,[r.value])}function sd(t,e,n){const r=function(t){if(!t)return null;for(let e=t.parent;e;e=e.parent){const t=e.routeConfig;if(t&&t._loadedConfig)return t._loadedConfig}return null}(e);return(r?r.module.injector:n).get(t)}function id(t,e,n,r,s={canDeactivateChecks:[],canActivateChecks:[]}){const i=rh(e);return t.children.forEach(t=>{!function(t,e,n,r,s={canDeactivateChecks:[],canActivateChecks:[]}){const i=t.value,o=e?e.value:null,a=n?n.getContext(t.value.outlet):null;if(o&&i.routeConfig===o.routeConfig){const l=function(t,e,n){if("function"==typeof n)return n(t,e);switch(n){case"pathParamsChange":return!Uc(t.url,e.url);case"pathParamsOrQueryParamsChange":return!Uc(t.url,e.url)||!Cc(t.queryParams,e.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!ph(t,e)||!Cc(t.queryParams,e.queryParams);case"paramsChange":default:return!ph(t,e)}}(o,i,i.routeConfig.runGuardsAndResolvers);l?s.canActivateChecks.push(new ed(r)):(i.data=o.data,i._resolvedData=o._resolvedData),id(t,e,i.component?a?a.children:null:n,r,s),l&&a&&a.outlet&&a.outlet.isActivated&&s.canDeactivateChecks.push(new nd(a.outlet.component,o))}else o&&od(e,a,s),s.canActivateChecks.push(new ed(r)),id(t,null,i.component?a?a.children:null:n,r,s)}(t,i[t.value.outlet],n,r.concat([t.value]),s),delete i[t.value.outlet]}),kc(i,(t,e)=>od(t,n.getContext(e),s)),s}function od(t,e,n){const r=rh(t),s=t.value;kc(r,(t,r)=>{od(t,s.component?e?e.children.getContext(r):null:e,n)}),n.canDeactivateChecks.push(new nd(s.component&&e&&e.outlet&&e.outlet.isActivated?e.outlet.component:null,s))}class ad{}function ld(t){return new v(e=>e.error(t))}class ud{constructor(t,e,n,r,s,i){this.rootComponentType=t,this.config=e,this.urlTree=n,this.url=r,this.paramsInheritanceStrategy=s,this.relativeLinkResolution=i}recognize(){const t=Bh(this.urlTree.root,[],[],this.config.filter(t=>void 0===t.redirectTo),this.relativeLinkResolution).segmentGroup,e=this.processSegmentGroup(this.config,t,vc);if(null===e)return null;const n=new lh([],Object.freeze({}),Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,{},vc,this.rootComponentType,null,this.urlTree.root,-1,{}),r=new nh(n,e),s=new uh(this.url,r);return this.inheritParamsAndData(s._root),s}inheritParamsAndData(t){const e=t.value,n=ah(e,this.paramsInheritanceStrategy);e.params=Object.freeze(n.params),e.data=Object.freeze(n.data),t.children.forEach(t=>this.inheritParamsAndData(t))}processSegmentGroup(t,e,n){return 0===e.segments.length&&e.hasChildren()?this.processChildren(t,e):this.processSegment(t,e,e.segments,n)}processChildren(t,e){const n=[];for(const s of Object.keys(e.children)){const r=e.children[s],i=Vh(t,s),o=this.processSegmentGroup(i,r,s);if(null===o)return null;n.push(...o)}const r=hd(n);return r.sort((t,e)=>t.value.outlet===vc?-1:e.value.outlet===vc?1:t.value.outlet.localeCompare(e.value.outlet)),r}processSegment(t,e,n,r){for(const s of t){const t=this.processSegmentAgainstRoute(s,e,n,r);if(null!==t)return t}return Wh(e,n,r)?[]:null}processSegmentAgainstRoute(t,e,n,r){if(t.redirectTo||!Gh(t,e,n,r))return null;let s,i=[],o=[];if("**"===t.path){const r=n.length>0?Tc(n).parameters:{};s=new lh(n,r,Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,fd(t),Mh(t),t.component,t,dd(e),pd(e)+n.length,gd(t))}else{const r=zh(e,t,n);if(!r.matched)return null;i=r.consumedSegments,o=n.slice(r.lastChild),s=new lh(i,r.parameters,Object.freeze(Object.assign({},this.urlTree.queryParams)),this.urlTree.fragment,fd(t),Mh(t),t.component,t,dd(e),pd(e)+i.length,gd(t))}const a=function(t){return t.children?t.children:t.loadChildren?t._loadedConfig.routes:[]}(t),{segmentGroup:l,slicedSegments:u}=Bh(e,i,o,a.filter(t=>void 0===t.redirectTo),this.relativeLinkResolution);if(0===u.length&&l.hasChildren()){const t=this.processChildren(a,l);return null===t?null:[new nh(s,t)]}if(0===a.length&&0===u.length)return[new nh(s,[])];const c=Mh(t)===r,h=this.processSegment(a,l,u,c?vc:r);return null===h?null:[new nh(s,h)]}}function cd(t){const e=t.value.routeConfig;return e&&""===e.path&&void 0===e.redirectTo}function hd(t){const e=[],n=new Set;for(const r of t){if(!cd(r)){e.push(r);continue}const t=e.find(t=>r.value.routeConfig===t.value.routeConfig);void 0!==t?(t.children.push(...r.children),n.add(t)):e.push(r)}for(const r of n){const t=hd(r.children);e.push(new nh(r.value,t))}return e.filter(t=>!n.has(t))}function dd(t){let e=t;for(;e._sourceSegment;)e=e._sourceSegment;return e}function pd(t){let e=t,n=e._segmentIndexShift?e._segmentIndexShift:0;for(;e._sourceSegment;)e=e._sourceSegment,n+=e._segmentIndexShift?e._segmentIndexShift:0;return n-1}function fd(t){return t.data||{}}function gd(t){return t.resolve||{}}function md(t){return Eu(e=>{const n=t(e);return n?U(n).pipe(T(()=>e)):Ol(e)})}class yd extends class{shouldDetach(t){return!1}store(t,e){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,e){return t.routeConfig===e.routeConfig}}{}const vd=new kn("ROUTES");class _d{constructor(t,e,n,r){this.loader=t,this.compiler=e,this.onLoadStartListener=n,this.onLoadEndListener=r}load(t,e){if(e._loader$)return e._loader$;this.onLoadStartListener&&this.onLoadStartListener(e);const n=this.loadModuleFactory(e.loadChildren).pipe(T(n=>{this.onLoadEndListener&&this.onLoadEndListener(e);const r=n.create(t);return new Oh(Ec(r.injector.get(vd,void 0,yt.Self|yt.Optional)).map(Fh),r)}),Du(t=>{throw e._loader$=void 0,t}));return e._loader$=new Z(n,()=>new C).pipe(q()),e._loader$}loadModuleFactory(t){return"string"==typeof t?U(this.loader.load(t)):Rc(t()).pipe(M(t=>t instanceof so?Ol(t):U(this.compiler.compileModuleAsync(t))))}}class wd{constructor(){this.outlet=null,this.route=null,this.resolver=null,this.children=new bd,this.attachRef=null}}class bd{constructor(){this.contexts=new Map}onChildOutletCreated(t,e){const n=this.getOrCreateContext(t);n.outlet=e,this.contexts.set(t,n)}onChildOutletDestroyed(t){const e=this.getContext(t);e&&(e.outlet=null)}onOutletDeactivated(){const t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let e=this.getContext(t);return e||(e=new wd,this.contexts.set(t,e)),e}getContext(t){return this.contexts.get(t)||null}}class Sd{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,e){return t}}function Cd(t){throw t}function xd(t,e,n){return e.parse("/")}function Ed(t,e){return Ol(null)}let Td=(()=>{class t{constructor(t,e,n,r,s,i,o,a){this.rootComponentType=t,this.urlSerializer=e,this.rootContexts=n,this.location=r,this.config=a,this.lastSuccessfulNavigation=null,this.currentNavigation=null,this.disposed=!1,this.lastLocationChangeInfo=null,this.navigationId=0,this.isNgZoneEnabled=!1,this.events=new C,this.errorHandler=Cd,this.malformedUriErrorHandler=xd,this.navigated=!1,this.lastSuccessfulId=-1,this.hooks={beforePreactivation:Ed,afterPreactivation:Ed},this.urlHandlingStrategy=new Sd,this.routeReuseStrategy=new yd,this.onSameUrlNavigation="ignore",this.paramsInheritanceStrategy="emptyOnly",this.urlUpdateStrategy="deferred",this.relativeLinkResolution="corrected",this.ngModule=s.get(ro),this.console=s.get(Io);const l=s.get(Bo);this.isNgZoneEnabled=l instanceof Bo&&Bo.isInAngularZone(),this.resetConfig(a),this.currentUrlTree=new jc(new Nc([],{}),{},null),this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.configLoader=new _d(i,o,t=>this.triggerEvent(new hc(t)),t=>this.triggerEvent(new dc(t))),this.routerState=ih(this.currentUrlTree,this.rootComponentType),this.transitions=new pu({id:0,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,extractedUrl:this.urlHandlingStrategy.extract(this.currentUrlTree),urlAfterRedirects:this.urlHandlingStrategy.extract(this.currentUrlTree),rawUrl:this.currentUrlTree,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:"imperative",restoredState:null,currentSnapshot:this.routerState.snapshot,targetSnapshot:null,currentRouterState:this.routerState,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.navigations=this.setupNavigations(this.transitions),this.processNavigations()}setupNavigations(t){const e=this.events;return t.pipe(Pl(t=>0!==t.id),T(t=>Object.assign(Object.assign({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl)})),Eu(t=>{let n=!1,r=!1;return Ol(t).pipe(Ku(t=>{this.currentNavigation={id:t.id,initialUrl:t.currentRawUrl,extractedUrl:t.extractedUrl,trigger:t.source,extras:t.extras,previousNavigation:this.lastSuccessfulNavigation?Object.assign(Object.assign({},this.lastSuccessfulNavigation),{previousNavigation:null}):null}}),Eu(t=>{const n=!this.navigated||t.extractedUrl.toString()!==this.browserUrlTree.toString();if(("reload"===this.onSameUrlNavigation||n)&&this.urlHandlingStrategy.shouldProcessUrl(t.rawUrl))return Ol(t).pipe(Eu(t=>{const n=this.transitions.getValue();return e.next(new nc(t.id,this.serializeUrl(t.extractedUrl),t.source,t.restoredState)),n!==this.transitions.getValue()?Su:Promise.resolve(t)}),(r=this.ngModule.injector,s=this.configLoader,i=this.urlSerializer,o=this.config,Eu(t=>function(t,e,n,r,s){return new Yh(t,e,n,r,s).apply()}(r,s,i,t.extractedUrl,o).pipe(T(e=>Object.assign(Object.assign({},t),{urlAfterRedirects:e}))))),Ku(t=>{this.currentNavigation=Object.assign(Object.assign({},this.currentNavigation),{finalUrl:t.urlAfterRedirects})}),function(t,e,n,r,s){return M(i=>function(t,e,n,r,s="emptyOnly",i="legacy"){try{const o=new ud(t,e,n,r,s,i).recognize();return null===o?ld(new ad):Ol(o)}catch(o){return ld(o)}}(t,e,i.urlAfterRedirects,n(i.urlAfterRedirects),r,s).pipe(T(t=>Object.assign(Object.assign({},i),{targetSnapshot:t}))))}(this.rootComponentType,this.config,t=>this.serializeUrl(t),this.paramsInheritanceStrategy,this.relativeLinkResolution),Ku(t=>{"eager"===this.urlUpdateStrategy&&(t.extras.skipLocationChange||this.setBrowserUrl(t.urlAfterRedirects,!!t.extras.replaceUrl,t.id,t.extras.state),this.browserUrlTree=t.urlAfterRedirects);const n=new oc(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot);e.next(n)}));var r,s,i,o;if(n&&this.rawUrlTree&&this.urlHandlingStrategy.shouldProcessUrl(this.rawUrlTree)){const{id:n,extractedUrl:r,source:s,restoredState:i,extras:o}=t,a=new nc(n,this.serializeUrl(r),s,i);e.next(a);const l=ih(r,this.rootComponentType).snapshot;return Ol(Object.assign(Object.assign({},t),{targetSnapshot:l,urlAfterRedirects:r,extras:Object.assign(Object.assign({},o),{skipLocationChange:!1,replaceUrl:!1})}))}return this.rawUrlTree=t.rawUrl,this.browserUrlTree=t.urlAfterRedirects,t.resolve(null),Su}),md(t=>{const{targetSnapshot:e,id:n,extractedUrl:r,rawUrl:s,extras:{skipLocationChange:i,replaceUrl:o}}=t;return this.hooks.beforePreactivation(e,{navigationId:n,appliedUrlTree:r,rawUrlTree:s,skipLocationChange:!!i,replaceUrl:!!o})}),Ku(t=>{const e=new ac(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot);this.triggerEvent(e)}),T(t=>Object.assign(Object.assign({},t),{guards:rd(t.targetSnapshot,t.currentSnapshot,this.rootContexts)})),function(t,e){return M(n=>{const{targetSnapshot:r,currentSnapshot:s,guards:{canActivateChecks:i,canDeactivateChecks:o}}=n;return 0===o.length&&0===i.length?Ol(Object.assign(Object.assign({},n),{guardsResult:!0})):function(t,e,n,r){return U(t).pipe(M(t=>function(t,e,n,r,s){const i=e&&e.routeConfig?e.routeConfig.canDeactivate:null;return i&&0!==i.length?Ol(i.map(i=>{const o=sd(i,e,s);let a;if(function(t){return t&&Ih(t.canDeactivate)}(o))a=Rc(o.canDeactivate(t,e,n,r));else{if(!Ih(o))throw new Error("Invalid CanDeactivate guard");a=Rc(o(t,e,n,r))}return a.pipe(Zu())})).pipe(Nh()):Ol(!0)}(t.component,t.route,n,e,r)),Zu(t=>!0!==t,!0))}(o,r,s,t).pipe(M(n=>n&&"boolean"==typeof n?function(t,e,n,r){return U(e).pipe(Il(e=>bu(function(t,e){return null!==t&&e&&e(new pc(t)),Ol(!0)}(e.route.parent,r),function(t,e){return null!==t&&e&&e(new gc(t)),Ol(!0)}(e.route,r),function(t,e,n){const r=e[e.length-1],s=e.slice(0,e.length-1).reverse().map(t=>function(t){const e=t.routeConfig?t.routeConfig.canActivateChild:null;return e&&0!==e.length?{node:t,guards:e}:null}(t)).filter(t=>null!==t).map(e=>xu(()=>Ol(e.guards.map(s=>{const i=sd(s,e.node,n);let o;if(function(t){return t&&Ih(t.canActivateChild)}(i))o=Rc(i.canActivateChild(r,t));else{if(!Ih(i))throw new Error("Invalid CanActivateChild guard");o=Rc(i(r,t))}return o.pipe(Zu())})).pipe(Nh())));return Ol(s).pipe(Nh())}(t,e.path,n),function(t,e,n){const r=e.routeConfig?e.routeConfig.canActivate:null;return r&&0!==r.length?Ol(r.map(r=>xu(()=>{const s=sd(r,e,n);let i;if(function(t){return t&&Ih(t.canActivate)}(s))i=Rc(s.canActivate(e,t));else{if(!Ih(s))throw new Error("Invalid CanActivate guard");i=Rc(s(e,t))}return i.pipe(Zu())}))).pipe(Nh()):Ol(!0)}(t,e.route,n))),Zu(t=>!0!==t,!0))}(r,i,t,e):Ol(n)),T(t=>Object.assign(Object.assign({},n),{guardsResult:t})))})}(this.ngModule.injector,t=>this.triggerEvent(t)),Ku(t=>{if(Ph(t.guardsResult)){const e=bc(`Redirecting to "${this.serializeUrl(t.guardsResult)}"`);throw e.url=t.guardsResult,e}const e=new lc(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot,!!t.guardsResult);this.triggerEvent(e)}),Pl(t=>{if(!t.guardsResult){this.resetUrlToCurrentUrlTree();const n=new sc(t.id,this.serializeUrl(t.extractedUrl),"");return e.next(n),t.resolve(!1),!1}return!0}),md(t=>{if(t.guards.canActivateChecks.length)return Ol(t).pipe(Ku(t=>{const e=new uc(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot);this.triggerEvent(e)}),Eu(t=>{let n=!1;return Ol(t).pipe((r=this.paramsInheritanceStrategy,s=this.ngModule.injector,M(t=>{const{targetSnapshot:e,guards:{canActivateChecks:n}}=t;if(!n.length)return Ol(t);let i=0;return U(n).pipe(Il(t=>function(t,e,n,r){return function(t,e,n,r){const s=Object.keys(t);if(0===s.length)return Ol({});const i={};return U(s).pipe(M(s=>function(t,e,n,r){const s=sd(t,e,r);return Rc(s.resolve?s.resolve(e,n):s(e,n))}(t[s],e,n,r).pipe(Ku(t=>{i[s]=t}))),Lu(1),M(()=>Object.keys(i).length===s.length?Ol(i):Su))}(t._resolve,t,e,r).pipe(T(e=>(t._resolvedData=e,t.data=Object.assign(Object.assign({},t.data),ah(t,n).resolve),null)))}(t.route,e,r,s)),Ku(()=>i++),Lu(1),M(e=>i===n.length?Ol(t):Su))})),Ku({next:()=>n=!0,complete:()=>{if(!n){const n=new sc(t.id,this.serializeUrl(t.extractedUrl),"At least one route resolver didn't emit any value.");e.next(n),t.resolve(!1)}}}));var r,s}),Ku(t=>{const e=new cc(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(t.urlAfterRedirects),t.targetSnapshot);this.triggerEvent(e)}))}),md(t=>{const{targetSnapshot:e,id:n,extractedUrl:r,rawUrl:s,extras:{skipLocationChange:i,replaceUrl:o}}=t;return this.hooks.afterPreactivation(e,{navigationId:n,appliedUrlTree:r,rawUrlTree:s,skipLocationChange:!!i,replaceUrl:!!o})}),T(t=>{const e=function(t,e,n){const r=fh(t,e._root,n?n._root:void 0);return new sh(r,e)}(this.routeReuseStrategy,t.targetSnapshot,t.currentRouterState);return Object.assign(Object.assign({},t),{targetRouterState:e})}),Ku(t=>{this.currentUrlTree=t.urlAfterRedirects,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t.rawUrl),this.routerState=t.targetRouterState,"deferred"===this.urlUpdateStrategy&&(t.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,!!t.extras.replaceUrl,t.id,t.extras.state),this.browserUrlTree=t.urlAfterRedirects)}),(i=this.rootContexts,o=this.routeReuseStrategy,a=t=>this.triggerEvent(t),T(t=>(new Rh(o,t.targetRouterState,t.currentRouterState,a).activate(i),t))),Ku({next(){n=!0},complete(){n=!0}}),(s=()=>{if(!n&&!r){this.resetUrlToCurrentUrlTree();const n=new sc(t.id,this.serializeUrl(t.extractedUrl),`Navigation ID ${t.id} is not equal to the current navigation id ${this.navigationId}`);e.next(n),t.resolve(!1)}this.currentNavigation=null},t=>t.lift(new Yu(s))),Du(n=>{if(r=!0,(s=n)&&s.ngNavigationCancelingError){const r=Ph(n.url);r||(this.navigated=!0,this.resetStateAndUrl(t.currentRouterState,t.currentUrlTree,t.rawUrl));const s=new sc(t.id,this.serializeUrl(t.extractedUrl),n.message);e.next(s),r?setTimeout(()=>{const e=this.urlHandlingStrategy.merge(n.url,this.rawUrlTree);this.scheduleNavigation(e,"imperative",null,{skipLocationChange:t.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy},{resolve:t.resolve,reject:t.reject,promise:t.promise})},0):t.resolve(!1)}else{this.resetStateAndUrl(t.currentRouterState,t.currentUrlTree,t.rawUrl);const r=new ic(t.id,this.serializeUrl(t.extractedUrl),n);e.next(r);try{t.resolve(this.errorHandler(n))}catch(i){t.reject(i)}}var s;return Su}));var s,i,o,a}))}resetRootComponentType(t){this.rootComponentType=t,this.routerState.root.component=this.rootComponentType}getTransition(){const t=this.transitions.value;return t.urlAfterRedirects=this.browserUrlTree,t}setTransition(t){this.transitions.next(Object.assign(Object.assign({},this.getTransition()),t))}initialNavigation(){this.setUpLocationChangeListener(),0===this.navigationId&&this.navigateByUrl(this.location.path(!0),{replaceUrl:!0})}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(t=>{const e=this.extractLocationChangeInfoFromEvent(t);this.shouldScheduleNavigation(this.lastLocationChangeInfo,e)&&setTimeout(()=>{const{source:t,state:n,urlTree:r}=e,s={replaceUrl:!0};if(n){const t=Object.assign({},n);delete t.navigationId,0!==Object.keys(t).length&&(s.state=t)}this.scheduleNavigation(r,t,n,s)},0),this.lastLocationChangeInfo=e}))}extractLocationChangeInfoFromEvent(t){var e;return{source:"popstate"===t.type?"popstate":"hashchange",urlTree:this.parseUrl(t.url),state:(null===(e=t.state)||void 0===e?void 0:e.navigationId)?t.state:null,transitionId:this.getTransition().id}}shouldScheduleNavigation(t,e){if(!t)return!0;const n=e.urlTree.toString()===t.urlTree.toString();return!(e.transitionId===t.transitionId&&n&&("hashchange"===e.source&&"popstate"===t.source||"popstate"===e.source&&"hashchange"===t.source))}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.currentNavigation}triggerEvent(t){this.events.next(t)}resetConfig(t){Uh(t),this.config=t.map(Fh),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.transitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0}createUrlTree(t,e={}){const{relativeTo:n,queryParams:r,fragment:s,queryParamsHandling:i,preserveFragment:o}=e,a=n||this.routerState.root,l=o?this.currentUrlTree.fragment:s;let u=null;switch(i){case"merge":u=Object.assign(Object.assign({},this.currentUrlTree.queryParams),r);break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=r||null}return null!==u&&(u=this.removeEmptyProps(u)),function(t,e,n,r,s){if(0===n.length)return vh(e.root,e.root,e,r,s);const i=function(t){if("string"==typeof t[0]&&1===t.length&&"/"===t[0])return new wh(!0,0,t);let e=0,n=!1;const r=t.reduce((t,r,s)=>{if("object"==typeof r&&null!=r){if(r.outlets){const e={};return kc(r.outlets,(t,n)=>{e[n]="string"==typeof t?t.split("/"):t}),[...t,{outlets:e}]}if(r.segmentPath)return[...t,r.segmentPath]}return"string"!=typeof r?[...t,r]:0===s?(r.split("/").forEach((r,s)=>{0==s&&"."===r||(0==s&&""===r?n=!0:".."===r?e++:""!=r&&t.push(r))}),t):[...t,r]},[]);return new wh(n,e,r)}(n);if(i.toRoot())return vh(e.root,new Nc([],{}),e,r,s);const o=function(t,e,n){if(t.isAbsolute)return new bh(e.root,!0,0);if(-1===n.snapshot._lastPathIndex){const t=n.snapshot._urlSegment;return new bh(t,t===e.root,0)}const r=mh(t.commands[0])?0:1;return function(t,e,n){let r=t,s=e,i=n;for(;i>s;){if(i-=s,r=r.parent,!r)throw new Error("Invalid number of '../'");s=r.segments.length}return new bh(r,!1,s-i)}(n.snapshot._urlSegment,n.snapshot._lastPathIndex+r,t.numberOfDoubleDots)}(i,e,t),a=o.processChildren?Ch(o.segmentGroup,o.index,i.commands):Sh(o.segmentGroup,o.index,i.commands);return vh(o.segmentGroup,a,e,r,s)}(a,this.currentUrlTree,t,u,l)}navigateByUrl(t,e={skipLocationChange:!1}){const n=Ph(t)?t:this.parseUrl(t),r=this.urlHandlingStrategy.merge(n,this.rawUrlTree);return this.scheduleNavigation(r,"imperative",null,e)}navigate(t,e={skipLocationChange:!1}){return function(t){for(let e=0;e<t.length;e++){const n=t[e];if(null==n)throw new Error(`The requested path contains ${n} segment at index ${e}`)}}(t),this.navigateByUrl(this.createUrlTree(t,e),e)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){let e;try{e=this.urlSerializer.parse(t)}catch(n){e=this.malformedUriErrorHandler(n,this.urlSerializer,t)}return e}isActive(t,e){if(Ph(t))return Ac(this.currentUrlTree,t,e);const n=this.parseUrl(t);return Ac(this.currentUrlTree,n,e)}removeEmptyProps(t){return Object.keys(t).reduce((e,n)=>{const r=t[n];return null!=r&&(e[n]=r),e},{})}processNavigations(){this.navigations.subscribe(t=>{this.navigated=!0,this.lastSuccessfulId=t.id,this.events.next(new rc(t.id,this.serializeUrl(t.extractedUrl),this.serializeUrl(this.currentUrlTree))),this.lastSuccessfulNavigation=this.currentNavigation,t.resolve(!0)},t=>{this.console.warn("Unhandled Navigation Error: ")})}scheduleNavigation(t,e,n,r,s){if(this.disposed)return Promise.resolve(!1);const i=this.getTransition(),o="imperative"!==e&&"imperative"===(null==i?void 0:i.source),a=(this.lastSuccessfulId===i.id||this.currentNavigation?i.rawUrl:i.urlAfterRedirects).toString()===t.toString();if(o&&a)return Promise.resolve(!0);let l,u,c;s?(l=s.resolve,u=s.reject,c=s.promise):c=new Promise((t,e)=>{l=t,u=e});const h=++this.navigationId;return this.setTransition({id:h,source:e,restoredState:n,currentUrlTree:this.currentUrlTree,currentRawUrl:this.rawUrlTree,rawUrl:t,extras:r,resolve:l,reject:u,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(t=>Promise.reject(t))}setBrowserUrl(t,e,n,r){const s=this.urlSerializer.serialize(t);r=r||{},this.location.isCurrentPathEqualTo(s)||e?this.location.replaceState(s,"",Object.assign(Object.assign({},r),{navigationId:n})):this.location.go(s,"",Object.assign(Object.assign({},r),{navigationId:n}))}resetStateAndUrl(t,e,n){this.routerState=t,this.currentUrlTree=e,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n),this.resetUrlToCurrentUrlTree()}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",{navigationId:this.lastSuccessfulId})}}return t.\u0275fac=function(e){return new(e||t)(Mn(An),Mn(Hc),Mn(bd),Mn(Ha),Mn($s),Mn(da),Mn(Vo),Mn(void 0))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),kd=(()=>{class t{constructor(t,e,n,r,s){this.parentContexts=t,this.location=e,this.resolver=n,this.changeDetector=s,this.activated=null,this._activatedRoute=null,this.activateEvents=new So,this.deactivateEvents=new So,this.name=r||vc,t.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.parentContexts.onChildOutletDestroyed(this.name)}ngOnInit(){if(!this.activated){const t=this.parentContexts.getContext(this.name);t&&t.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.resolver||null))}}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new Error("Outlet is not activated");this.location.detach();const t=this.activated;return this.activated=null,this._activatedRoute=null,t}attach(t,e){this.activated=t,this._activatedRoute=e,this.location.insert(t.hostView)}deactivate(){if(this.activated){const t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,e){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;const n=(e=e||this.resolver).resolveComponentFactory(t._futureSnapshot.routeConfig.component),r=this.parentContexts.getOrCreateContext(this.name).children,s=new Rd(t,r,this.location.injector);this.activated=this.location.createComponent(n,this.location.length,s),this.changeDetector.markForCheck(),this.activateEvents.emit(this.activated.instance)}}return t.\u0275fac=function(e){return new(e||t)(Ks(bd),Ks(oo),Ks(wi),("name",function(t,e){const n=t.attrs;if(n){const t=n.length;let r=0;for(;r<t;){const s=n[r];if(Ye(s))break;if(0===s)r+=2;else if("number"==typeof s)for(r++;r<t&&"string"==typeof n[r];)r++;else{if(s===e)return n[r+1];r+=2}}}return null}(xe(),"name")),Ks(Qi))},t.\u0275dir=qt({type:t,selectors:[["router-outlet"]],outputs:{activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"]}),t})();class Rd{constructor(t,e,n){this.route=t,this.childContexts=e,this.parent=n}get(t,e){return t===oh?this.route:t===bd?this.childContexts:this.parent.get(t,e)}}class Ad{}class Od{preload(t,e){return Ol(null)}}let Id=(()=>{class t{constructor(t,e,n,r,s){this.router=t,this.injector=r,this.preloadingStrategy=s,this.loader=new _d(e,n,e=>t.triggerEvent(new hc(e)),e=>t.triggerEvent(new dc(e)))}setUpPreloading(){this.subscription=this.router.events.pipe(Pl(t=>t instanceof rc),Il(()=>this.preload())).subscribe(()=>{})}preload(){const t=this.injector.get(ro);return this.processRoutes(t,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,e){const n=[];for(const r of e)if(r.loadChildren&&!r.canLoad&&r._loadedConfig){const t=r._loadedConfig;n.push(this.processRoutes(t.module,t.routes))}else r.loadChildren&&!r.canLoad?n.push(this.preloadConfig(t,r)):r.children&&n.push(this.processRoutes(t,r.children));return U(n).pipe(z(),T(t=>{}))}preloadConfig(t,e){return this.preloadingStrategy.preload(e,()=>(e._loadedConfig?Ol(e._loadedConfig):this.loader.load(t.injector,e)).pipe(M(t=>(e._loadedConfig=t,this.processRoutes(t.module,t.routes)))))}}return t.\u0275fac=function(e){return new(e||t)(Mn(Td),Mn(da),Mn(Vo),Mn($s),Mn(Ad))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})(),Pd=(()=>{class t{constructor(t,e,n={}){this.router=t,this.viewportScroller=e,this.options=n,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},n.scrollPositionRestoration=n.scrollPositionRestoration||"disabled",n.anchorScrolling=n.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.router.events.subscribe(t=>{t instanceof nc?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=t.navigationTrigger,this.restoredId=t.restoredState?t.restoredState.navigationId:0):t instanceof rc&&(this.lastId=t.id,this.scheduleScrollEvent(t,this.router.parseUrl(t.urlAfterRedirects).fragment))})}consumeScrollEvents(){return this.router.events.subscribe(t=>{t instanceof yc&&(t.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(t.position):t.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(t.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(t,e){this.router.triggerEvent(new yc(t,"popstate"===this.lastSource?this.store[this.restoredId]:null,e))}ngOnDestroy(){this.routerEventsSubscription&&this.routerEventsSubscription.unsubscribe(),this.scrollEventsSubscription&&this.scrollEventsSubscription.unsubscribe()}}return t.\u0275fac=function(e){return new(e||t)(Mn(Td),Mn(Xa),Mn(void 0))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();const jd=new kn("ROUTER_CONFIGURATION"),Nd=new kn("ROUTER_FORROOT_GUARD"),Dd=[Ha,{provide:Hc,useClass:Lc},{provide:Td,useFactory:function(t,e,n,r,s,i,o,a={},l,u){const c=new Td(null,t,e,n,r,s,i,Ec(o));if(l&&(c.urlHandlingStrategy=l),u&&(c.routeReuseStrategy=u),function(t,e){t.errorHandler&&(e.errorHandler=t.errorHandler),t.malformedUriErrorHandler&&(e.malformedUriErrorHandler=t.malformedUriErrorHandler),t.onSameUrlNavigation&&(e.onSameUrlNavigation=t.onSameUrlNavigation),t.paramsInheritanceStrategy&&(e.paramsInheritanceStrategy=t.paramsInheritanceStrategy),t.relativeLinkResolution&&(e.relativeLinkResolution=t.relativeLinkResolution),t.urlUpdateStrategy&&(e.urlUpdateStrategy=t.urlUpdateStrategy)}(a,c),a.enableTracing){const t=ba();c.events.subscribe(e=>{t.logGroup(`Router Event: ${e.constructor.name}`),t.log(e.toString()),t.log(e),t.logGroupEnd()})}return c},deps:[Hc,bd,Ha,$s,da,Vo,vd,jd,[class{},new Bn],[class{},new Bn]]},bd,{provide:oh,useFactory:function(t){return t.routerState.root},deps:[Td]},{provide:da,useClass:ga},Id,Od,class{preload(t,e){return e().pipe(Du(()=>Ol(null)))}},{provide:jd,useValue:{enableTracing:!1}}];function Ud(){return new ia("Router",Td)}let Hd=(()=>{class t{constructor(t,e){}static forRoot(e,n){return{ngModule:t,providers:[Dd,Vd(e),{provide:Nd,useFactory:Md,deps:[[Td,new Bn,new qn]]},{provide:jd,useValue:n||{}},{provide:Pa,useFactory:Fd,deps:[Ca,[new zn(Na),new Bn],jd]},{provide:Pd,useFactory:Ld,deps:[Td,Xa,jd]},{provide:Ad,useExisting:n&&n.preloadingStrategy?n.preloadingStrategy:Od},{provide:ia,multi:!0,useFactory:Ud},[$d,{provide:Co,multi:!0,useFactory:zd,deps:[$d]},{provide:qd,useFactory:Bd,deps:[$d]},{provide:Oo,multi:!0,useExisting:qd}]]}}static forChild(e){return{ngModule:t,providers:[Vd(e)]}}}return t.\u0275fac=function(e){return new(e||t)(Mn(Nd,8),Mn(Td,8))},t.\u0275mod=zt({type:t}),t.\u0275inj=ut({}),t})();function Ld(t,e,n){return n.scrollOffset&&e.setOffset(n.scrollOffset),new Pd(t,e,n)}function Fd(t,e,n={}){return n.useHash?new Ua(t,e):new Da(t,e)}function Md(t){return"guarded"}function Vd(t){return[{provide:Rn,multi:!0,useValue:t},{provide:vd,multi:!0,useValue:t}]}let $d=(()=>{class t{constructor(t){this.injector=t,this.initNavigation=!1,this.resultOfPreactivationDone=new C}appInitializer(){return this.injector.get(Ea,Promise.resolve(null)).then(()=>{let t=null;const e=new Promise(e=>t=e),n=this.injector.get(Td),r=this.injector.get(jd);return"disabled"===r.initialNavigation?(n.setUpLocationChangeListener(),t(!0)):"enabled"===r.initialNavigation||"enabledBlocking"===r.initialNavigation?(n.hooks.afterPreactivation=()=>this.initNavigation?Ol(null):(this.initNavigation=!0,t(!0),this.resultOfPreactivationDone),n.initialNavigation()):t(!0),e})}bootstrapListener(t){const e=this.injector.get(jd),n=this.injector.get(Id),r=this.injector.get(Pd),s=this.injector.get(Td),i=this.injector.get(ca);t===i.components[0]&&("enabledNonBlocking"!==e.initialNavigation&&void 0!==e.initialNavigation||s.initialNavigation(),n.setUpPreloading(),r.init(),s.resetRootComponentType(i.componentTypes[0]),this.resultOfPreactivationDone.next(null),this.resultOfPreactivationDone.complete())}}return t.\u0275fac=function(e){return new(e||t)(Mn($s))},t.\u0275prov=lt({token:t,factory:t.\u0275fac}),t})();function zd(t){return t.appInitializer.bind(t)}function Bd(t){return t.bootstrapListener.bind(t)}const qd=new kn("Router Initializer");function Gd(t,e){if(1&t&&(Ys(0,"div",2),Ys(1,"div",3),ai(2),ti(),ti()),2&t){const t=oi();Vr(2),li(t.adData.text)}}function Wd(t,e){1&t&&(we.lFrame.currentNamespace=ae,Ys(0,"svg",13),ei(1,"polyline",14),ti())}const Zd=function(t){return{"background-color":t}};function Qd(t,e){if(1&t&&(Ys(0,"div",9),ri("click",function(){const t=e.$implicit;return t.selected=!t.selected}),Ys(1,"div",10),ai(2),ti(),Ys(3,"div",11),Qs(4,Wd,2,0,"svg",12),ti(),ti()),2&t){const t=e.$implicit;Vr(2),ui(" ",t.question," "),Vr(1),Js("ngStyle",(n=3,r=Zd,s=t.selected?"#1abc9c1a":"transparent",function(t,e,n,r,s,i){const o=e+n;return Zs(t,o,s)?function(t,e,n){return t[e]=n}(t,o+1,i?r.call(i,s):r(s)):function(t,e){const n=t[e];return n===Mr?void 0:n}(t,o+1)}(Se(),function(){const t=we.lFrame;let e=t.bindingRootIndex;return-1===e&&(e=t.bindingRootIndex=t.tView.bindingStartIndex),e}(),n,r,s,i))),Vr(1),Js("ngIf",t.selected)}var n,r,s,i}function Kd(t,e){if(1&t&&(Ys(0,"div",2),Ys(1,"div",4),Ys(2,"div",5),ai(3),ti(),Qs(4,Qd,5,5,"div",6),ei(5,"div",7),Ys(6,"div",8),ai(7," Submit survey "),ti(),ti(),ti()),2&t){const t=oi();Vr(3),ui("",t.adData.title,"?"),Vr(1),Js("ngForOf",t.adData.questions)}}function Jd(t,e){if(1&t&&(Ys(0,"div",15),ei(1,"video",16),ti()),2&t){const t=oi();Vr(1),Js("src",t.videoLink,Kn)}}function Xd(t,e){if(1&t&&(Ys(0,"div",15),ei(1,"img",17),ti()),2&t){const t=oi();Vr(1),Js("src",t.imageLink,Kn)}}const Yd=[{path:":adID",component:(()=>{class t{constructor(t,e){this.http=t,this.activatedRoute=e,this.adData=null,this.videoLink=null,this.imageLink=null}ngOnInit(){return t=this,void 0,n=function*(){const t=this.activatedRoute.snapshot.params.adID;this.adData=yield this.http.get(`https://elegant-liskov.52-172-165-186.plesk.page/ads/${t}`).pipe(T(t=>{const e=t.data.data;return e.questions&&(e.questions=JSON.parse(e.questions),e.questions=e.questions.map(t=>({question:t,selected:!1}))),e})).toPromise(),this.videoLink="https://elegant-liskov.52-172-165-186.plesk.page/ads/videos/"+this.adData.id+"-"+this.adData.video_name,this.imageLink="https://elegant-liskov.52-172-165-186.plesk.page/ads/images/"+this.adData.id+"-"+this.adData.image_name,console.log(this.imageLink)},new((e=void 0)||(e=Promise))(function(r,s){function i(t){try{a(n.next(t))}catch(e){s(e)}}function o(t){try{a(n.throw(t))}catch(e){s(e)}}function a(t){var n;t.done?r(t.value):(n=t.value,n instanceof e?n:new e(function(t){t(n)})).then(i,o)}a((n=n.apply(t,[])).next())});var t,e,n}}return t.\u0275fac=function(e){return new(e||t)(Ks(Jl),Ks(oh))},t.\u0275cmp=Ft({type:t,selectors:[["app-ad"]],decls:4,vars:4,consts:[["class","font width100 height100 vhCenter noselect",4,"ngIf"],["class","font width100 height100 noselect oHide",4,"ngIf"],[1,"font","width100","height100","vhCenter","noselect"],[1,"fs24","lp100","rp100","tCenter"],[1,"flexBottom",2,"width","720px"],[1,"fs16"],["class","mt15 tp15 bp15 lp25 rp25 black5 blackHov10 flexRight vCenter",3,"click",4,"ngFor","ngForOf"],[1,"h15"],[1,"autoMarginX","tWhite100","tp15","bp15","lp25","rp25","vhCenter","pointer",2,"background-color","#1abc9c"],[1,"mt15","tp15","bp15","lp25","rp25","black5","blackHov10","flexRight","vCenter",3,"click"],[1,"flex1"],[1,"h25","w25","vhCenter",2,"border","1px solid #1abc9c",3,"ngStyle"],["xmlns","http://www.w3.org/2000/svg","width","24","height","24","viewBox","0 0 24 24","fill","none","stroke","#1abc9c","stroke-width","4","stroke-linecap","round","stroke-linejoin","round","class","w15",4,"ngIf"],["xmlns","http://www.w3.org/2000/svg","width","24","height","24","viewBox","0 0 24 24","fill","none","stroke","#1abc9c","stroke-width","4","stroke-linecap","round","stroke-linejoin","round",1,"w15"],["points","20 6 9 17 4 12"],[1,"font","width100","height100","noselect","oHide"],["autoplay","",3,"src"],[3,"src"]],template:function(t,e){1&t&&(Qs(0,Gd,3,1,"div",0),Qs(1,Kd,8,2,"div",0),Qs(2,Jd,2,1,"div",1),Qs(3,Xd,2,1,"div",1)),2&t&&(Js("ngIf","TEXT"==(null==e.adData?null:e.adData.type)),Vr(1),Js("ngIf","SURVEY"==(null==e.adData?null:e.adData.type)),Vr(1),Js("ngIf","VIDEO"==(null==e.adData?null:e.adData.type)),Vr(1),Js("ngIf","IMAGE"==(null==e.adData?null:e.adData.type)))},directives:[Wa,qa,Ka],styles:["img[_ngcontent-%COMP%], video[_ngcontent-%COMP%]{position:relative;left:50%;transform:translateX(-50%);width:100vw}"]}),t})()}];let tp=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=zt({type:t}),t.\u0275inj=ut({imports:[[Hd.forRoot(Yd,{useHash:!0})],Hd]}),t})(),ep=(()=>{class t{constructor(){this.title="r2v2-support"}}return t.\u0275fac=function(e){return new(e||t)},t.\u0275cmp=Ft({type:t,selectors:[["app-root"]],decls:1,vars:0,template:function(t,e){1&t&&ei(0,"router-outlet")},directives:[kd],styles:[""]}),t})(),np=(()=>{class t{}return t.\u0275fac=function(e){return new(e||t)},t.\u0275mod=zt({type:t,bootstrap:[ep]}),t.\u0275inj=ut({providers:[],imports:[[Al,tp,du]]}),t})();(function(){if(ra)throw new Error("Cannot enable prod mode after platform setup.");na=!1})(),kl().bootstrapModule(np).catch(t=>console.error(t))},zn8P:function(t,e){function n(t){return Promise.resolve().then(function(){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e})}n.keys=function(){return[]},n.resolve=n,t.exports=n,n.id="zn8P"}},[[0,0]]]);