
const dbpool = require('../../startup/db.js');
const CONSTANTS = require("../../misc/constants.js");
const Cache = require('../../misc/cache.js');
const helpers = require('../../misc/helpers.js');
const logger = require('../../misc/logger.js');
//const eventEmitter = require('./eventEmitter');
const ccDB = process.env.DB_DATABASE || "channelconnector";

class RoleService {
    
    // async getRolePermissions(roleId) {
    //     const cacheKey = CacheService.generateKey('role_permissions', roleId);
    //     let permissions = await CacheService.get(cacheKey);

    //     // If not in cache, fetch from database
    //     if (!permissions) {
    //         const [rows] = await pool.execute(
    //             `SELECT p.* 
    //              FROM permissions p
    //              JOIN role_permissions rp ON p.id = rp.permission_id
    //              WHERE rp.role_id = ?`,
    //             [roleId]
    //         );
    //         permissions = rows;
            
    //         // Store in cache for future requests
    //         await CacheService.set(cacheKey, permissions, CACHE_TTL.PERMISSIONS);
    //     }

    //     return permissions;
    // }

    // // Updates the permissions assigned to a role
    // async updateRolePermissions(roleId, permissionIds) {
    //     // Start a database transaction for atomicity
    //     await pool.execute('START TRANSACTION');

    //     try {
    //         // Remove all existing permissions for this role
    //         await pool.execute(
    //             'DELETE FROM role_permissions WHERE role_id = ?',
    //             [roleId]
    //         );

    //         // Add new permissions
    //         for (const permissionId of permissionIds) {
    //             await pool.execute(
    //                 'INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)',
    //                 [roleId, permissionId]
    //             );
    //         }

    //         // Commit the transaction
    //         await pool.execute('COMMIT');

    //         // Invalidate related caches
    //         await CacheService.del(CacheService.generateKey('role_permissions', roleId));
    //         await CacheService.delPattern('user_permissions:*');

    //         // Emit event for other parts of the system
    //         eventEmitter.emit('role_updated', roleId);

    //         return true;
    //     } catch (error) {
    //         // Rollback transaction if anything fails
    //         await pool.execute('ROLLBACK');
    //         throw error;
    //     }
    // }

    // Creates a new role
    // async createRole(name, organizationId) {
    //     const [result] = await pool.execute(
    //         'INSERT INTO roles (name, organization_id) VALUES (?, ?)',
    //         [name, organizationId]
    //     );

    //     return {
    //         id: result.insertId,
    //         name,
    //         organizationId
    //     };
    // }

    // Gets a role by ID with its permissions
    // async getRoleById(roleId) {
    //     const cacheKey = CacheService.generateKey('role', roleId);
    //     let role = await CacheService.get(cacheKey);

    //     if (!role) {
    //         const [rows] = await pool.execute(
    //             'SELECT * FROM roles WHERE id = ?',
    //             [roleId]
    //         );
    //         role = rows[0];

    //         if (role) {
    //             // Get permissions for this role
    //             role.permissions = await this.getRolePermissions(roleId);
    //             await CacheService.set(cacheKey, role, CACHE_TTL.ROLES);
    //         }
    //     }

    //     return role;
    // }

    // get all roles
    async getRoles() {
        try {
            const cacheKey = CONSTANTS.CACHE.ROLES.KEY;
            const cacheTTL = CONSTANTS.CACHE.ROLES.TTL;
            let roles = await Cache.get(cacheKey);
            if (!roles) {
                const query= `${ccDB}.GetAllRoles`;
                const [rows] = await dbpool.executeProcedure(query, [], ccDB);
                roles = rows;
                console.log(roles);
                await Cache.set(cacheKey, roles, cacheTTL);
            }    
            return roles;
        } catch (error) {
            throw error;
        }   
    }

    // Gets all roles for an organization
    // async getOrganizationRoles(organizationId) {
    //     const cacheKey = CacheService.generateKey('org_roles', organizationId);
    //     let roles = await CacheService.get(cacheKey);

    //     if (!roles) {
    //         const [rows] = await pool.execute(
    //             'SELECT * FROM roles WHERE organization_id = ?',
    //             [organizationId]
    //         );
    //         roles = rows;

    //         // Get permissions for each role
    //         for (let role of roles) {
    //             role.permissions = await this.getRolePermissions(role.id);
    //         }

    //         await CacheService.set(cacheKey, roles, CACHE_TTL.ROLES);
    //     }

    //     return roles;
    // }

    // Deletes a role and its permission assignments
    // async deleteRole(roleId) {
    //     await pool.execute('START TRANSACTION');

    //     try {
    //         // Delete role permissions first (foreign key constraint)
    //         await pool.execute(
    //             'DELETE FROM role_permissions WHERE role_id = ?',
    //             [roleId]
    //         );

    //         // Delete the role
    //         await pool.execute(
    //             'DELETE FROM roles WHERE id = ?',
    //             [roleId]
    //         );

    //         await pool.execute('COMMIT');

    //         // Clean up caches
    //         await CacheService.del(CacheService.generateKey('role', roleId));
    //         await CacheService.del(CacheService.generateKey('role_permissions', roleId));
    //         await CacheService.delPattern('user_permissions:*');
    //         await CacheService.delPattern('org_roles:*');

    //         return true;
    //     } catch (error) {
    //         await pool.execute('ROLLBACK');
    //         throw error;
    //     }
    // }
}

// eventEmitter.on('role_updated', async (roleId) => {
//     await CacheService.del(CacheService.generateKey('role_permissions', roleId));
//     await CacheService.delPattern('user_permissions:*');
// });

// eventEmitter.on('org_updated', async (orgId) => {
//     await CacheService.delPattern(`org_page:${orgId}:*`);
//     await CacheService.del(CacheService.generateKey('org_details', orgId));
// });

module.exports = new RoleService();