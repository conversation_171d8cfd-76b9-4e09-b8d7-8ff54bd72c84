USE channelconnector;

DROP  PROCEDURE IF EXISTS channelconnector.addOrderMaster;

DELIMITER $$ 
CREATE  PROCEDURE channelconnector.addOrderMaster(
 `in_channel` VARCHAR(256),
 `in_channel_code` VARCHAR(20),
 `in_ownr_code` VARCHAR(20),
 `in_fby_id` VARCHAR(128),
 `in_account_id` INT(11),
 `in_order_no` VARCHAR(256),
 `in_selr_ordr_id` VARCHAR(100),
 `in_prchse_dt` DATETIME,
 `in_payment_time` DATETIME,
 `in_recipient_nm` VARCHAR(256),
 `in_company` VARCHAR(256),
 `in_shiper_adr1` VARCHAR(256),
 `in_shiper_adr2` VARCHAR(256),
 `in_shiper_city` VARCHAR(256),
 `in_shiper_state` VARCHAR(64),
 `in_shiper_state_code` VARCHA<PERSON>(4),
 `in_shiper_zip` VARCHAR(256),
 `in_shiper_country` VARCHAR(256),
 `in_shiper_country_code` VARCHAR(20),
 `in_shiper_phone` VARCHAR(256),
 `in_total_order` DECIMAL(10,2),
 `in_total_items` INT(11),
 `in_total_item_price` DECIMAL(10,2),
 `in_total_ship_price` DECIMAL(10,2),
 `in_total_tax` DECIMAL(10,2),
 `in_total_discount` DECIMAL(10,2),
 `in_payment_id` VARCHAR(256),
 `in_payment_method` VARCHAR(256),
 `in_currency_code` VARCHAR(20),
 `in_buyer_id` VARCHAR(128),
 `in_buyer_email` VARCHAR(256),
 `in_buyer_name` VARCHAR(256),
 `in_sales_record_no` VARCHAR(128),
 `in_payment_status` VARCHAR(20),
 `in_order_status` VARCHAR(20),
 `in_crn_name` VARCHAR(60),
 `in_crn_id` VARCHAR(100)
)
BEGIN
	/*
    
		call  channelconnector.`addOrderMaster`(
        
			'woocommerce IT',		#	in_channel,
			'WCIT',					#	in_channel_code,
			'in_ownr_code',			#	in_ownr_code,
			'27',					#	in_fby_id,
			'123456',				#	in_account_id,
			'wc_order_KK6GN241vQJU9',			#	in_order_no,
			'in_selr_ordr_id',		#	in_selr_ordr_id,
			 NOW(),					#	in_prchse_dt,
			 NOW(),					#	in_payment_time,
			'in_recipient_nm',		#	in_recipient_nm,
			'in_company',			#	in_company,
			'in_shiper_adr1',		#	in_shiper_adr1,
			'in_shiper_adr2',		#	in_shiper_adr2,
			'in_shiper_city',		#	in_shiper_city,
			'in_shiper_state',		#	in_shiper_state,
			'1',					#	in_shiper_state_code,
			'in_shiper_zip',		#	in_shiper_zip,
			'in_shiper_country',	#	in_shiper_country,
			'1',					#	in_shiper_country_code,
			'in_shiper_phone',		#	in_shiper_phone,
			'1',					#	in_total_order,
			'1',					#	in_total_items,
			'10',					#	in_total_item_price,
			'10',					#	in_total_ship_price,
			'10',					#	in_total_tax,
			'0',					#	in_total_discount,
			'1',					#	in_payment_id,
			'cash',					#	in_payment_method,
			'EUR',					#	in_currency_code,
			'in_buyer_id',			#	in_buyer_id,
			'in_buyer_email',		#	in_buyer_email,
			'in_buyer_name',		#	in_buyer_name,
			'in_sales_record_no',	#	in_sales_record_no,
			'in_payment_status',	#	in_payment_status,
			'in_order_status',		#	in_order_status,
			'in_crn_name',			#	in_crn_name,
			'in_crn_id'				#	in_crn_id

		);
        
        Select * from channelconnector.order_masters
        order by 1 DESC
        limit 10;
        
        
			SET SQL_SAFE_UPDATES = 0;    
			Delete from channelconnector.order_masters
			Where id = 700;
			
			SET SQL_SAFE_UPDATES = 1;    
        
        
    */
	
    DECLARE var_channel_name VARCHAR(200);
    DECLARE is_Order_Exists BIT;

	SET var_channel_name  = (
		select LOWER(ch.channelName) from _2_channel as ch 
		Where  ch.channelId = in_fby_id
		AND ch.isActive = 1 
		limit 1
	);
    
    SET is_Order_Exists = (
	SELECT 
		1
	FROM
		order_masters
	WHERE
		order_no = in_order_no
		AND fby_user_id = in_fby_id
		AND `channel` = in_channel
		AND channel_code = in_channel_code
		AND owner_code = in_ownr_code
		AND account_id = in_account_id
		AND seller_order_id = in_selr_ordr_id
        limit 1
    );

	
	IF(var_channel_name like '%woo%comm%' AND is_Order_Exists IS NULL)
	THEN
		SET in_order_no = replace(in_order_no,'wc_order_','order_');
	END IF;
	-- Select is_Order_Exists, var_channel_name,in_order_no;
    
	INSERT IGNORE INTO order_masters(
		`channel`,	
		channel_code,	
		owner_code	,
		fby_user_id,	
		account_id,	
		order_no,	
		seller_order_id,	
		purchase_date,	
		payment_date,	
		recipient_name,	
		ship_company,	
		ship_address_1,	
		ship_address_2,	
		ship_city,	
		ship_state,	
		ship_state_code,	
		ship_postal_code,	
		ship_country,	
		ship_country_code,	
		ship_phone_number,	
		total_order,	
		total_items,	
		total_items_price,	
		total_shipping_price,	
		total_tax,	
		total_discount,	
		payment_transaction_id,	
		payment_method,	
		currency_code,	
		buyer_id,	
		buyer_email,	
		buyer_name,	
		sales_record_no,	
		payment_status,	
		order_status,	
		cron_name,	
		cron_id
)
VALUES(
	in_channel,	
	in_channel_code,	
	in_ownr_code,	
	in_fby_id,	
	in_account_id,	
	in_order_no,	
	in_selr_ordr_id,	
	in_prchse_dt,	
	in_payment_time,	
	in_recipient_nm,	
	in_company,	
	in_shiper_adr1,	
	in_shiper_adr2,	
	in_shiper_city,	
	in_shiper_state,	
	in_shiper_state_code,	
	in_shiper_zip,	
	in_shiper_country,	
	in_shiper_country_code,	
	in_shiper_phone,	
	in_total_order,	
	in_total_items,	
	in_total_item_price,	
	in_total_ship_price,	
	in_total_tax,	
	in_total_discount,	
	in_payment_id,	
	in_payment_method,	
	in_currency_code,	
	in_buyer_id,	
	in_buyer_email,	
	in_buyer_name,	
	in_sales_record_no,	
	in_payment_status,	
	in_order_status,	
	in_crn_name,	
	in_crn_id	
)
ON DUPLICATE KEY
	UPDATE
	
	purchase_date       =       			in_prchse_dt,
	payment_date       =       			in_payment_time,
	recipient_name       =       			in_recipient_nm,
	ship_company       =       			in_company,
	ship_address_1       =       			in_shiper_adr1,
	ship_address_2       =       			in_shiper_adr2,
	ship_city       =       			in_shiper_city,
	ship_state       =       			in_shiper_state,
	ship_state_code       =       			in_shiper_state_code,
	ship_postal_code       =       			in_shiper_zip,
	ship_country       =       			in_shiper_country,
	ship_country_code       =       			in_shiper_country_code,
	ship_phone_number       =       			in_shiper_phone,
	total_order       =       			in_total_order,
	total_items       =       			in_total_items,
	total_items_price       =       			in_total_item_price,
	total_shipping_price       =       			in_total_ship_price,
	total_tax       =       			in_total_tax,
	total_discount       =       			in_total_discount,
	payment_transaction_id       =       			in_payment_id,
	payment_method       =       			in_payment_method,
	currency_code       =       			in_currency_code,
	buyer_id       =       			in_buyer_id,
	buyer_email       =       			in_buyer_email,
	buyer_name       =       			in_buyer_name,
	sales_record_no       =       			in_sales_record_no,
	payment_status       =       			in_payment_status,
	order_status       =       			in_order_status,
	cron_name       =       			in_crn_name,
	cron_id			= in_crn_id;

    
	SELECT 
		*
	FROM
		order_masters
	WHERE
		order_no = in_order_no
		AND fby_user_id = in_fby_id
		AND `channel` = in_channel
		AND channel_code = in_channel_code
		AND owner_code = in_ownr_code
		AND account_id = in_account_id
		AND seller_order_id = in_selr_ordr_id;
    
END$$
DELIMITER ;