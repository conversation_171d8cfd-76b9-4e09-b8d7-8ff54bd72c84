const Joi = require('joi');

const trackingEventCreateSchema = Joi.array().items(
    Joi.object({
        warehouseId: Joi.number().integer().required(),
        hubName: Joi.string().min(3).required(),
        // eventType: Joi.string().valid('Arrived', 'Departed', 'Delivery Attempted', 'Returned', 'OutForDelivery','Delivered').required(),
        eventType: Joi.string().required(),
        status: Joi.string().required(),
        timestamp: Joi.string().isoDate().required(),
        statusMessage: Joi.string().min(5).required(),
        location: Joi.string().min(2).required(),
        mode: Joi.string().required(),
        hubsTrackingType: Joi.string().required(),
        returnReason: Joi.string().optional(),
        deliveryAgent: Joi.object({
            agentName: Joi.string().optional().allow(''),
            agentContact: Joi.string().optional().pattern(/^\+?[0-9]{10,15}$/).allow(''),
            agentEmail: Joi.string().optional().email().allow(''),
            agentId: Joi.string().optional().allow('')
        }).optional()
    })
).min(1);

module.exports = { trackingEventCreateSchema };
