// partnerFactory.js
const ShipxboxService = require('./shipxbox/shipxboxService');
const InternalShippingService = require('./shipxbox/internalShippingService');
const DTDCService = require('./dtdc/dtdcService');
// const EcomExpressService = require('./ecomexpress/ecomExpressService');
const BluedartService = require('./bluedart/bluedartService');
const CONSTANT = require('../../misc/constants');
const { SHIPMENT_TYPES } = require('../../misc/enums/orderStatusEnum');
// Add more as needed

function getShippingService(providerDetails, type = SHIPMENT_TYPES.FORWARD) {
  if (providerDetails?.providerType?.toUpperCase() === CONSTANT.SHIPPING.PROVIDER_TYPE.INTERNAL) {
    return new InternalShippingService(providerDetails, type);
  }

  const providerCode = providerDetails?.providerCode?.toUpperCase();

  switch (providerCode) {
    case 'SHIPXBOX':
      return new ShipxboxService(providerDetails, type);
    case 'DTDC':
      return new DTDCService(providerDetails, type);
    case 'ECOMEXPRESS':
      throw new Error('EcomExpressService is not implemented');
    case 'BLUEDART':
      return new BluedartService(providerDetails, type);
    default:
      throw new Error(`Unknown shipping partner: ${providerCode}`);
  }
}

module.exports = { getShippingService };
