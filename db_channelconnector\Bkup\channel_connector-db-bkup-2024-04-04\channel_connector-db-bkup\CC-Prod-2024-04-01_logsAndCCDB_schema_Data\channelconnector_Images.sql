-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `Images`
--

DROP TABLE IF EXISTS `Images`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `Images` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sku` varchar(128) DEFAULT NULL,
  `image` varchar(700) DEFAULT NULL,
  `skuFamily` varchar(128) DEFAULT NULL,
  `fby_user_id` int DEFAULT NULL,
  `channel_image_id` varchar(128) DEFAULT NULL,
  `imageOrder` int DEFAULT NULL,
  `createdOn` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Images`
--

LOCK TABLES `Images` WRITE;
/*!40000 ALTER TABLE `Images` DISABLE KEYS */;
INSERT INTO `Images` VALUES (1,'RDM_NPN_MFH001_E8480_BJ999','https://cdn.yocabe.com/nero-pantera/NP1127114-521_5.jpg','RDM_NPN_MFH001',41,'3',3,'2022-12-08 18:41:25'),(2,'RDM_NPN_MFH001_E8480_BJ999','https://cdn.yocabe.com/nero-pantera/IT/NP1127114_Size.jpg','RDM_NPN_MFH001',41,'2',2,'2022-12-08 18:41:25'),(3,'RDM_NPN_MFH001_E8480_BJ999','http://cdn.yocabe.com/nero-pantera/NP1127114-521_1.jpg','RDM_NPN_MFH001',41,'1',1,'2022-12-08 18:41:25'),(4,'RDM_NPN_MFH001_E8480_BJ999','http://cdn.yocabe.com/nero-pantera/NP1127114-521_6.jpg','RDM_NPN_MFH001',41,'5',5,'2022-12-08 18:41:25'),(5,'RDM_NPN_MFH001_E8480_BJ999','https://cdn.yocabe.com/nero-pantera/IT/NP1127114_RFID.jpg','RDM_NPN_MFH001',41,'4',4,'2022-12-08 18:41:25'),(6,'RDM_NPN_MFH002_J3704_BJ999','http://cdn.yocabe.com/nero-pantera/NP288037-20_7.jpg','RDM_NPN_MFH002',41,'40052169343250',5,'2022-12-08 18:54:24'),(7,'RDM_NPN_MFH002_J3704_BJ999','http://cdn.yocabe.com/nero-pantera/NP288037-20_1.jpg','RDM_NPN_MFH002',41,'40052169834770',1,'2022-12-08 18:54:24'),(8,'RDM_NPN_MFH002_J3704_BJ999','http://cdn.yocabe.com/nero-pantera/NP288037-20_6.jpg','RDM_NPN_MFH002',41,'40052170129682',3,'2022-12-08 18:54:24'),(9,'RDM_NPN_MFH002_J3704_BJ999','http://cdn.yocabe.com/nero-pantera/IT/NP288037_Marrone_Size.jpg','RDM_NPN_MFH002',41,'40052170522898',2,'2022-12-08 18:54:24'),(10,'RDM_NPN_MFH002_J3704_BJ999','http://cdn.yocabe.com/nero-pantera/IT/NP288037_Marrone_RFID.jpg','RDM_NPN_MFH002',41,'40052170817810',4,'2022-12-08 18:54:24'),(11,'RDM_BTA_MCD471_J3704_T8','http://cdn.yocabe.com/bata/zalando/8934539-3.jpg','RDM_BTA_MCD471',41,'40054482403602',2,'2022-12-09 00:00:03'),(12,'RDM_BTA_MCD471_J3704_T8','http://cdn.yocabe.com/bata/zalando/8934539-5.jpg','RDM_BTA_MCD471',41,'40054484074770',4,'2022-12-09 00:00:03'),(13,'RDM_BTA_MCD471_J3704_T8','http://cdn.yocabe.com/bata/zalando/8934539-6.jpg','RDM_BTA_MCD471',41,'40054484566290',5,'2022-12-09 00:00:03'),(14,'RDM_BTA_MCD471_J3704_T8','http://cdn.yocabe.com/bata/zalando/8934539-2.jpg','RDM_BTA_MCD471',41,'40054484926738',1,'2022-12-09 00:00:03'),(15,'RDM_BTA_MCD471_J3704_T8','http://cdn.yocabe.com/bata/zalando/8934539-4.jpg','RDM_BTA_MCD471',41,'40054486008082',3,'2022-12-09 00:00:03'),(16,'RDM_NPN_MFH003_A2682_BJ999','http://cdn.yocabe.com/nero-pantera/NP3057117-10_6.jpg','RDM_NPN_MFH003',41,'40059238121746',5,'2022-12-09 11:30:03'),(17,'RDM_NPN_MFH003_A2682_BJ999','http://cdn.yocabe.com/nero-pantera/NP3057117-10_1.jpg','RDM_NPN_MFH003',41,'40059239301394',1,'2022-12-09 11:30:03'),(18,'RDM_NPN_MFH003_A2682_BJ999','http://cdn.yocabe.com/nero-pantera/IT/NP3057117_Nero_Size.jpg','RDM_NPN_MFH003',41,'40059239465234',2,'2022-12-09 11:30:03'),(19,'RDM_NPN_MFH003_A2682_BJ999','http://cdn.yocabe.com/nero-pantera/IT/NP3057117_Nero_RFID.jpg','RDM_NPN_MFH003',41,'40059239661842',4,'2022-12-09 11:30:03'),(20,'RDM_NPN_MFH003_A2682_BJ999','http://cdn.yocabe.com/nero-pantera/NP3057117-10_5.jpg','RDM_NPN_MFH003',41,'40059240120594',3,'2022-12-09 11:30:03'),(36,'RDM_HGB_MEA001_I18499_F9','http://cdn.yocabe.com/aerial-vision/827886007387_2d_0001.jpg','RDM_HGB_MEA001',41,'40077480263954',1,'2022-12-12 08:41:02'),(37,'RDM_HGB_MEA001_I18499_F9','http://cdn.yocabe.com/aerial-vision/827886007387_2d_0002.jpg','RDM_HGB_MEA001',41,'40077480657170',2,'2022-12-12 08:41:02'),(38,'RDM_HGB_MEA001_I18499_F9','http://cdn.yocabe.com/aerial-vision/827886007387_2d_0003.jpg','RDM_HGB_MEA001',41,'40077480984850',3,'2022-12-12 08:41:02');
/*!40000 ALTER TABLE `Images` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:27:42
