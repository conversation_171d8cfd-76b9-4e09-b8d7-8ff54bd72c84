DROP PROCEDURE IF EXISTS channelconnector._3_winston_logs_clean;

DELIMITER $$
CREATE PROCEDURE channelconnector.`_3_winston_logs_clean`(
	`in_partition_key` date
 )
BEGIN
	/*
		Select * from `channelconnector`.`winston_logs` order 
        
		Call channelconnector._3_winston_logs_clean('2022-02-28');
                
    */
    SET SQL_SAFE_UPDATES = 0;
	DELETE
	FROM 
		`channelconnector`.`winston_logs`
	WHERE 
		 CAST(`timestamp` as date)< CAST(`in_partition_key`as date)  ;
    SET SQL_SAFE_UPDATES = 1;    
    
END$$
DELIMITER ;
