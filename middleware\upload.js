const CONSTANTS = require("../misc/constants.js");
const multer = require("multer");
const path = require("path");
const fs = require('fs');

// Multer setup for logo upload
const orgLogoDir = CONSTANTS.MEDIA_PATH.ORG_LOGO_DIR;

if (!fs.existsSync(orgLogoDir)) {
    fs.mkdirSync(orgLogoDir, { recursive: true }); // Create directory if it doesn't exist
}

const logoStorage = multer.diskStorage({
  destination: function (req, file, cb) {
      cb(null, orgLogoDir);
  },
  filename: function (req, file, cb) {
      const ext = path.extname(file.originalname);
      const uniqueSuffix = Date.now();
      cb(null, `org-logo-${uniqueSuffix}${ext}`);
  }
});

const imageFiler = (req, file, cb) => {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
  } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and WEBP images are allowed.'), false);
  }
};

const limits = {
  fileSize: 1 * 1024 * 1024 // 2MB limit
};


exports.orgLogoUpload = multer({  
  storage: logoStorage,
  fileFilter: imageFiler,
  limits: { fileSize: 1 * 1024 * 1024 }
});


// Multer storage configuration for order uploads
const ordersDir = path.join(CONSTANTS.MEDIA_PATH.IMPORT_DIR, 'orders');

if (!fs.existsSync(ordersDir)) {
    fs.mkdirSync(ordersDir, { recursive: true }); 
}

const orderStorage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, ordersDir); 
    },
    filename: function (req, file, cb) {
        cb(null, `${Date.now()}-${file.originalname}`);
    }
});

// File filter to allow only CSV and Excel files
const orderFileFilter = (req, file, cb) => {
    const allowedTypes = ['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
    
    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('Only CSV and Excel files are allowed!'), false);
    }
};

exports.orderUpload = multer({ 
    storage: orderStorage,
    fileFilter: orderFileFilter,
    limits: { fileSize: 5 * 1024 * 1024 } // Limit file size to 5MB
});

