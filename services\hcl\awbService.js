const dbpool = require('../../startup/db');
const miscConstants = require("../../misc/constants");
const cache = require('../../misc/cache.js');
const logger = require('../../misc/logger.js');
const hcldb = process.env.INITIAL_CATALOG || "hcl";

class AWBService {

    /**
     * Get AWB for an order using stored procedure
     * @param {number} shippingProviderId - Shipping provider ID
     * @param {number} orderId - Order ID
     * @returns {Promise<string>} AWB number
     */

    static async getAWBForOrder(providerId, orderId) {
        try {
            const sql = `${hcldb}.GetAWBForOrder`;
            const [result] = await dbpool.executeProcedure(sql, [orderId, providerId]);
            
            if (!result || !result[0] || !result[0]['awb']) {
                throw new Error('No available AWBs for this provider');
            }
            
            return result[0]['awb']; // Returns format like "FX00012345"
        } catch (error) {
            throw new Error(`AWB Error: ${error.message}`);
        }
    }

    /**
     * Populate AWBs for a provider using stored procedure
     * @param {number} providerId - Shipping provider ID
     * @param {number} quantity - Number of AWBs to generate
     * @param {string|null} [prefix=null] - AWB prefix (optional)
     * @param {number} [awbLength=10] - Length of numeric part
     * @returns {Promise<{success: boolean, message: string, count: number}>}
     */
    static async populateAWBs(providerId, quantity = miscConstants.SHIPPING.PRE_POPULATE_AWB_QTY) {
        try {
            const sql = `${hcldb}.GenerateAWBsForProvider`;
            const [result] = await dbpool.executeProcedure(sql, [providerId, quantity]);
            
            return {
                success: true,
                message: result['message'] || 'AWBs generated successfully',
                count: quantity
            };
        } catch (error) {
            throw new Error(`AWB Population Error: ${error.message}`);
        }
    }

    /**
     * Check AWB availability for a provider
     * @param {number} providerId - Shipping provider ID
     * @returns {Promise<{available: number}>} Count of available AWBs
     */
    static async checkAWBAvailability(providerId) {
        try {
            const sql = `${hcldb}.CheckAWBAvailability`;
            const [result] = await dbpool.executeProcedure(sql, [providerId]);
            return {
                availableAws: result[0]['available_count'] || 0
            };
        } catch (error) {
            throw new Error(`Availability Check Error: ${error.message}`);
        }
    }

}

module.exports = AWBService;     