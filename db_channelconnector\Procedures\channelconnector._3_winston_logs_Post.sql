DROP PROCEDURE IF EXISTS channelconnector._3_winston_logs_Post;

DELIMITER $$
CREATE PROCEDURE channelconnector.`_3_winston_logs_Post`(
	`in_level` varchar(255) ,
	`in_message` text ,
	`in_meta` text ,
	`in_operation_id` varchar(255)
 )
BEGIN

	INSERT INTO `channelconnector`.`winston_logs`
	( 
		`level`,
		`message`,
		`meta`,
		`operation_id`
	)
	VALUES
	(
		`in_level` ,
		`in_message`,
		`in_meta`  ,
		`in_operation_id`
	);
    
	SET SQL_SAFE_UPDATES = 0;

	DELETE FROM `channelconnector`.`winston_logs` 
	WHERE
		`timestamp` < DATE_ADD(NOW(), INTERVAL -7 DAY);



END$$
DELIMITER ;