DROP PROCEDURE IF EXISTS channelconnector.addProduct;

DEL<PERSON>ITER $$
CREATE  PROCEDURE `addProduct`(
	`in_fby_user_id` VARCHAR(128),
	`in_chanel` VARCHAR(20),
	`in_domain` VARCHAR(64),
	`in_ownr_code` VARCHAR(20),
	`in_sku` VARCHAR(128),
	`in_barcode` VARCHAR(128),
	`in_item_id` VARCHAR(127),
	`in_title` VARCHAR(300),
	`in_item_product_id` VARCHAR(127),
	`in_inventory_item_id` VARCHAR(127),
	`in_old_quantity` INT(11),
	`in_new_quantity` INT(11),
	`in_image` TEXT,
	`in_price` DECIMAL(10, 2),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
    `in_location_Id` VARCHAR(100)
)
BEGIN
	DECLARE var_channel_name varchar(200);
    
    SET var_channel_name  = (
	select LOWER(ch.channelName) from _2_channel as ch 
    Where  ch.channelId = in_fby_user_id
    AND ch.isActive = 1 
    limit 1
	);
    
	SET SQL_SAFE_UPDATES = 0;
	-- SET in_old_quantity = -1;
    SET in_chanel = (
		Select channelName from channelconnector._2_channel
        Where channelId = in_fby_user_id
        limit 1
    );
    IF(in_inventory_item_id is null) 
    then
		SET in_inventory_item_id = 0;
    END IF;
    
    IF(in_old_quantity is null) 
    then
		SET in_old_quantity = 0;
    END IF;
    
     IF(in_new_quantity is null) 
    then
		SET in_new_quantity = 0;
    END IF;
    
	IF (in_sku IS NOT NULL AND  in_sku <> '') THEN
	INSERT INTO
		products(
			fby_user_id,
			channel,
			domain,
			owner_code,
			sku,
			barcode,
			item_id,
			title,
			item_product_id,
			inventory_item_id,
			previous_inventory_quantity,
			inventory_quantity,
			image,
			price,
			cron_name,
			cron_id,
			location_Id
		)
	VALUES
		(
				in_fby_user_id,
				in_chanel,
				in_domain,
				in_ownr_code,
				in_sku,
				in_barcode,
				in_item_id,
				in_title,
				in_item_product_id,
				in_inventory_item_id,
				in_old_quantity,
				in_new_quantity,
				in_image,
				in_price,
				in_crn_name,
				in_crnid,
				in_location_Id
		) 
		ON DUPLICATE KEY
		UPDATE
			domain = in_domain,
			barcode = in_barcode,
			item_id = in_item_id,
			title = in_title,
			inventory_item_id = in_inventory_item_id,
			price = in_price,
			location_Id = case when in_location_Id > 0 then in_location_Id else location_Id end,
			previous_inventory_quantity =	case  when var_channel_name like '%woo%comm%'  then previous_inventory_quantity else in_old_quantity end,
			inventory_quantity =  case  when var_channel_name like '%woo%comm%'  then inventory_quantity else in_new_quantity end,
            image = in_image
		;
        
        IF(var_channel_name like '%woo%comm%')
        THEN
			SET SQL_SAFE_UPDATES = 0;
			Update channelconnector.products as p5, (
			Select ROW_NUMBER() over() as seq, 
			p1.id,p1.channel,
			p1.Sku,p1.item_id , p1.item_product_id,p2.item_product_id as parentId,p1.inventory_quantity,p2.familty_qty_sum
			from channelconnector.products as p1 
			Left join (
						SELECT 
							fby_User_id,
							item_product_id,
							`channel`,
							SUM(inventory_quantity) AS familty_qty_sum
						FROM
							channelconnector.products AS p
						WHERE
							fby_User_id = in_fby_user_id
								AND item_id <> item_product_id
								AND barcode <> ''
								AND LOWER(channel) LIKE 'woo%comm%'
						GROUP BY fby_User_id ,  item_product_id, channel 
			 ) as p2
			 on p2.item_product_id = p1.item_id
			 Where lower(p1.channel) like 'woo%comm%'
			  and p1.item_id = p1.item_product_id
			  and p1.barcode = ''
			  and p1.fby_User_id = in_fby_user_id
			  and p1.inventory_quantity <> p2.familty_qty_sum
			  ) as p6
			 Set p5.inventory_quantity = p6.familty_qty_sum
			 Where p5.id = p6.id;
             
		 END IF;
    END IF;    
END$$
DELIMITER ;
