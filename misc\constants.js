const path = require('path');
const { ORDER_STATUS, OPERATION_TYPE } = require('../server/constants/constants');
const { groupBy } = require('lodash');

module.exports.ACTION = {
    INSERT: "insert",
    UPDATE: "update",
    DELETE: "delete",
    GET: "get"
};

module.exports.HTTPSTATUSCODES = {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    INTERNAL_SERVER_ERROR: 500,
    UNAUTHORIZED : 401, 
};

module.exports.COD_REMITTANCE_DAYS = process.env.COD_REMITTANCE_DAYS;

module.exports.ERRORCODES = {
    FORBIDDEN:`PERMISSION_DENIED`,
    NOT_FOUND: `NOT_FOUND`,
    DUPLICATE_RESOURCE: `DUPLICATE_RESOURCE`,
    INTERNAL_SERVER_ERROR: `SERVICE_NOT_AVAILABLE`,
    VALIDATION_ERROR: `INVALID_PAYLOAD_PARAMS`,
};

module.exports.SUCESSSMESSAGES = {
    INSERT: `Requested data has been created successfully.`,
    UPDATE: `Requested data has been updated successfully.`,
    DELETE: `Requested data has been deleted successfully.`,
    GET: `Requested data has been fetched successfully.`,
    EXECUTED: ` action processed successfully.`,
    INSERT_UPDATE: `Requested data has been added/updated successfully.`,
};

module.exports.ERRORMESSAGES = {
    FORBIDDEN: `Permission denied`,
    NOT_FOUND: `Requested data not found!. Please check request data.`,
    DUPLICATE_RESOURCE: `Resource already exists!`,
    INTERNAL_SERVER_ERROR: `Something went wrong! Please try again later.`,

};

module.exports.CUSTOM_MESSAGES = {
    GET_UNSEND_PRODUCT:"Get unsend products",
    GET_UNSEND_ORDER:"Get unsend orders",
    GET_UNTRACKED_ORDER:"Get untracked orders",
    GET_TRACKABLE_LISTITEM:"Get tracked order details",
    GET_ORDER_DETAIL:"Get order details by order number",
    GET_USER:"Get user detail from users by fby_user_id",
    GET_PRESTASHOP_USER:"Get prestashop user detail from prestashop_accoount by fby_user_id",
    GET_PRODUCT_BY_DOMAIN:"Get product details by domain from products to update quantity",
    GET_CANCELED_ORDERS:"Get Canceled Orders from order_details",
    GET_JWT_TOKEN:"Get JWT Token",
    BLANK:"",
    CURRENT_STATE:"4",
    GET_CHHANEL_USER: " user detail from _2_channel by channelid",
};

module.exports.API_TYPES = {
    FBY: "FBY",
    SHOPIFY: "SHOPIFY",
    STOREDEN: "STOREDEN",
    PRESTASHOP: "PRESTASHOP",
    WOOCOMMERCE: "WOOCOMMERCE",
    EBAY: "EBAY",
    MIRAKL: "MIRAKL",
    AMAZON: "AMAZON",
    MAGENTO: "MAGENTO"
};

module.exports.EBAY_KEYS = {
    ALL_ITEM: 'GetMyeBaySelling',
    SINGLE_ITEM: 'GetItem',
    GET_ORDERS: 'GetOrders',
    UPDATE_QUANTITY: "ReviseInventoryStatus",
    UPDATE_TRACKING: "CompleteSale",
};


module.exports.CACHE_KEYS = {
    FBYUSER: 'GET_USERS',
    CHANNEL_USER: 'GET_CHANNEL_USER',
   
};

module.exports.SHIPPING = {
    PRE_POPULATE_AWB_QTY: 20,
    MAX_WEIGHT: {
        'DEFAULT': 1000,
        'AIR': 100,    
    },
    MIN_WEIGHT: {
        'B2B': 10,
        'AIR': 10, 
        'DEFAULT': 10,   
    },   
    PROVIDER_TYPE: {
        INTERNAL: 'INTERNAL',
        EXTERNAL: 'EXTERNAL',
    },
    MODE: { AIR: 'air', SURFACE: 'surface', RAIL: 'rail', DP: 'dp' },
    MODE_ARRAY: [ 'air', 'surface', 'rail', 'dp' ]
};

module.exports.WALLET = {
    TRANSACTION_TYPE: {
        ORDER_TRXN: 'ORDER_TRXN', 
        ADMIN_TRXN: 'ADMIN_TRXN',
        CLIENT_TRXN: 'CLIENT_TRXN'
    },
    OPERATION: {
        ORDER_CHARGE: 'ORDER_CHARGE',
        ORDER_REFUND: 'ORDER_REFUND',
        WALLET_RECHARGED: 'WALLET_RECHARGED',
        WALLET_DEDUCTED: 'WALLET_DEDUCTED'
    },
    TRANSACTION_REF: {
        ADMIN_RECHARGED: 'Admin added money to client wallet:',
        ADMIN_DEDUCTED: 'Admin deducted money from wallet:',
        ORDER_CHARGE: 'Order shipment charged:',
        ORDER_REFUND: 'Order shipment charge refunded:',
        CLIENT_RECHARGE: 'Client recharged the wallet:'
    }
};

module.exports.MEDIA_PATH = {
    "UPLOADS_DIR": process.env.UPLOAD_DIR || 'uploads',
    "IMPORT_DIR": `${process.env.UPLOAD_DIR}/imports` || 'uploads',
    "ORG_LOGO_DIR": `${process.env.UPLOAD_DIR}/org-logos` || 'uploads',

};

module.exports.CACHE = {

    ORGANIZATIONS: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'ORG:', 
    },
    ORG_PAGE: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'ORG_PAGE:', 
    },
    USERS: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'USERS:', 
    },
    PERMISSION: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'PERMISSION:', 
    },
    ROLES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'ROLES:', 
    },
    CLIENTS: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'CLIENTS', 
    },
    CLIENT_PROVIDERS: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'CLIENT_PROVIDERS:', 
    },
    CLIENT_ZONES_RATES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        GROUPKEY: 'CLIENT_ZONES_RATES_GROUP', 
        KEY:'CLIENT_ZONES_RATES',
    },
    CLIENT_ADDITIONAL_RATES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY:'CLIENT_ADDITIONAL_RATES:',
    },
    SHIPPING_PROVIDER: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'PROVIDERS:', 
        NAMEKEY: 'PROVIDERS_NAME:',
        IDKEY: 'PROVIDERS_ID:'
    },
    SHIPPING_PROVIDER_WEIGHT: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'SHIPPING_PROVIDER_WEIGHT', 
    },
    SHIPPING_PROVIDER_RATES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        GROUPKEY: 'SHIPPING_PROVIDER_RATES_GROUP', 
        KEY:'SHIPPING_PROVIDER_RATES',
    },
    SHIPPING_PROVIDER_Additional_RATES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY:'PROVIDER_Additional_RATES:',
    },
    SHIPPING_PRIORITIES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'PRIORITIES', 
        GROUPKEY: 'PRIORITIES_GROUP',
    },
    ORDER_STATUSES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'ORDER_STATUSES', // TTL in seconds
        GROUPKEY: 'ORDER_STATUSES_STATE',
    },
    STATUSES_MASTER: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'STATUSES_MASTER', // TTL in seconds
    },
    PINCODE: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'PINCODE:', 
    },
    MANIFEST: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'MANIFEST:', 
    },
    ZONES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'ZONES:', 
    },
    ZONES_DELIVERY_DAYS: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'DELIVERY_DAYS:', 
    },
    CLIENT_COD_RATES: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'CLIENT_COD_RATES', 
    },
    WEIGHT: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'WEIGHT:', 
    },
    ORDER_TYPE_MASTER: {
        TTL: process.env.CACHE_TTL || 24 * 60 * 60, // TTL in seconds
        KEY: 'ORDER_TYPE_MASTERS', 
    },
    DASHBOARD: {
        TTL:  1 * 60 * 60, // TTL in seconds
        KEY: 'DASHBOARD:', 
    },
    DASHBOARD_PROVIDER_DISTRIBUTION: {
        TTL: 1 * 60 * 60, // TTL in seconds
        KEY: 'PROVIDER_DISTRIBUTION:', 
    },
    DASHBOARD_STATUS_DISTRIBUTION: {
        TTL: 1 * 60 * 60, // TTL in seconds
        KEY: 'DASHBOARD_STATUS_DISTRIBUTION:', 
    },
  
};
