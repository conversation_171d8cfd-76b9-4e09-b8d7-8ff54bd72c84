const BaseService = require('../baseService.js');
const { getShipxboxConfig } = require('../../../misc/shippingConfig.js');
const logger = require('../../../misc/logger.js');
const AWBService = require('../../hcl/awbService.js');
const OrderService = require('../../hcl/orderService.js');

/**
 * Shipxbox services
 */
class ShipxboxService extends BaseService {
    constructor(providerDetails, type) {
        const config = getShipxboxConfig();
        super(config, type, providerDetails);
    
        if (!providerDetails?.id || !config?.shippingProviderId) {
          logger.logError('Internal Shipping: Missing required API configuration.');
          throw new Error('Missing required API configuration.');
        }
    
        this.shippingProviderId = providerDetails.id || config.shippingProviderId;
        this.shippingProviderName = providerDetails.name || config.shippingProviderName;
      }
    
      /**
       * Create shipment
       * @param {Object} order
       * @returns {Object}
       */
      async createShipment(order) {
        try {
          
          const referenceCode = order.invoiceNo;
          const awbNumber = await AWBService.getAWBForOrder(this.shippingProviderId, order.orderId);
          
          if (!awbNumber) {
            logger.logError('Shipxbox: Failed to get AWB number.');
            return { success: false, awbNumber: null, tokenNumber: '', referenceCode, message: 'Not able to get AWB.', waybillInfo: { isErrror: true } };
          }
    
          logger.logInfo(`Shipxbox: AWB generated successfully. AWB: ${awbNumber}, Invoice: ${referenceCode}`);

          const waybillInfo =  {  
                  isErrror: false,
                  awb : awbNumber,
                  tokenNumber: '',
                  referenceCode
                };
          return { success: true, awbNumber, tokenNumber: '', referenceCode, message: 'AWB generated successfully.', waybillInfo};

        } catch (error) {
          logger.logError(`Shipxbox: Error in createShipment: ${error.message}`);
          throw new Error(`Shipment Error: ${error.message}`);
        }
      }
    
      /**
       * Track shipment
       * @param {string} trackingNumber
       * @param {string} format
       * @returns {Object}
       */
      async trackShipment(trackingNumber, format = 'json') {
        try {
          const shippingDetails = await OrderService.getShippingDetailsByAWB(trackingNumber);
          if (!shippingDetails || shippingDetails.shippingProviderId !== this.shippingProviderId) {
            logger.logError(`Shipxbox: Tracking failed. Tracking Number: ${trackingNumber}`);
            return { success: false, trackingNumber, message: `Order has not been shipped by ${this.shippingProviderName}` };
          }
    
          const orderId = shippingDetails.orderId;
          const trackDetails = await OrderService.getTrackingEvents(orderId);
    
          const finalResponse = {
            success: true,
            data: {
              trackHeader: {
                awb: trackingNumber,
                orderType: '',
                serviceType: '',
                status: '',
                clientName: '',
                paymentMode: '',
                orderAmount: '',
                orderDate: '',
                orderId,
                shippingProviderName: this.shippingProviderName,
                origin: '',
                destination: '',
                customer: '',
                timestamp: '',
                expectedDeliveryDate: ''
              },
              trackDetails
            }
          };
    
          logger.logInfo(`Shipxbox: Tracking completed. Tracking Number: ${trackingNumber}`);
          return finalResponse;
        } catch (error) {
          logger.logError(`Shipxbox: Error in trackShipment: ${error.message}`);
          throw new Error(`Error in tracking shipment: ${error.message}`);
        }
    }
}

module.exports = ShipxboxService;
