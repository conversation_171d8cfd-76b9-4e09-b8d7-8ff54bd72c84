-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `_1_client`
--

DROP TABLE IF EXISTS `_1_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `_1_client` (
  `id` int NOT NULL AUTO_INCREMENT,
  `clientId` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `name` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `ownerCode` varchar(1024) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
  `isActive` tinyint DEFAULT NULL,
  `createdOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `modifiedOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `_1_client`
--

LOCK TABLES `_1_client` WRITE;
/*!40000 ALTER TABLE `_1_client` DISABLE KEYS */;
INSERT INTO `_1_client` VALUES (1,'id-000','Chicco','YT',1,'2022-01-19 20:59:01','2022-01-19 20:59:01'),(2,'id123','Chicco','PI',1,'2022-01-31 02:36:05','2022-01-31 02:36:05'),(3,'57','demo_fby','FDM',1,'2022-02-24 20:59:01','2022-02-24 20:59:01'),(8,'122','fby_owner','TEST',1,'2022-09-12 15:53:38','2022-09-12 15:53:38'),(9,'123','storeden_owner','STO',1,'2022-09-12 15:53:39','2022-09-12 15:53:39'),(10,'46','demo_fby','FDM',1,'2022-09-14 10:04:59','2022-09-14 10:04:59'),(11,'47','Isbag','FIS',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(12,'49','Persian Gourmet','FPR',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(13,'53','DocHq','FDC',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(14,'54','Golden Days Milano','FGL',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(15,'55','Digitalnow International','FDG',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(16,'56','Abm Top','FAB',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(17,'58','Cirkolo Roma','FCR',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(18,'59','Adsera MB','FAD',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(19,'60','Peachday','FPC',1,'2022-09-14 10:05:00','2022-09-14 10:05:00'),(20,'62','Sirt500','FSR',1,'2022-09-14 10:05:01','2022-09-14 10:05:01'),(21,'63','De Lamour Presents','FDL',1,'2022-09-14 10:05:01','2022-09-14 10:05:01'),(22,'4','FBY_TEST_USER','FAB',1,'2023-01-02 10:57:05','2023-01-02 10:57:05');
/*!40000 ALTER TABLE `_1_client` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:51:04
