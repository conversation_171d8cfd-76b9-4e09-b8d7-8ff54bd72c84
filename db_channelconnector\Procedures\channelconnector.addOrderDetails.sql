DROP PROCEDURE IF EXISTS channelconnector.addOrderDetails;

DELIMITER $$
CREATE  PROCEDURE channelconnector.`addOrderDetails`(
	`in_chanl` VARCHAR(128),
	`in_channel_code` VARCHAR(128),
	`in_ownr_code` VARCHAR(128),
	`in_fby_id` VARCHAR(128),
	`in_account_id` INT(11),
	`in_order_no` VARCHAR(256),
	`in_location_id` VARCHA<PERSON>(256),
	`in_selr_ordr_id` VARCHAR(100),
	`in_prchse_dt` DATETIME,
	`in_payment_time` DATETIME,
	`in_ordr_line_id` VARCHAR(256),
	`in_sku` VARCHAR(256),
	`in_barcode` VARCHAR(128),
	`in_ordr_itm_id` VARCHAR(128),
	`in_trnsactn_id` VARCHAR(128),
	`in_prod_nm` VARCHAR(256),
	`in_qntity_prchsed` INT(11),
	`in_curncy` VARCHAR(10),
	`in_exchng_rt` FLOAT,
	`in_itm_price` DECIMAL(10, 2),
	`in_line_itm_price` DECIMAL(10, 2),
	`in_itm_tx` DECIMAL(10, 2),
	`in_itm_totl_tx` DECIMAL(10, 2),
	`in_promotion_discount` DECIMAL(10, 2),
	`in_item_total_price` DECIMAL(10, 2),
	`in_item_total_ship_price` DECIMAL(10, 2),
	`in_crn_name` VARCHAR(60),
	`in_crn_id` VARCHAR(100),
	`in_financial_status` VARCHAR(128),
	`in_order_status` VARCHAR(128)
)
BEGIN

DECLARE var_channel_name VARCHAR(200);
    DECLARE is_Order_Exists BIT;

	SET var_channel_name  = (
		select LOWER(ch.channelName) from _2_channel as ch 
		Where  ch.channelId = in_fby_id
		AND ch.isActive = 1 
		limit 1
	);
    
    SET is_Order_Exists = (
	SELECT 
		1
	FROM
		channelconnector.order_details t
	WHERE
		t.fby_user_id = in_fby_id
		AND t.order_no = in_order_no
		AND t.seller_order_id = in_selr_ordr_id
		AND t.order_line_item_id = in_ordr_line_id
		AND t.order_item_id = in_ordr_itm_id
		AND t.sku = in_sku
		AND t.barcode = in_barcode    
    );

	
	IF(var_channel_name like '%woo%comm%' AND is_Order_Exists IS NULL)
	THEN
		SET in_order_no = replace(in_order_no,'wc_order_','order_');
	END IF;

INSERT INTO
	order_details(
		`channel`,
		channel_code,
		owner_code,
		fby_user_id,
		account_id,
		order_no,
		location_id,
		seller_order_id,
		purchase_date,
		payment_time,
		order_line_item_id,
		sku,
		barcode,
		order_item_id,
		transaction_id,
		product_name,
		quantity_purchased,
		currency,
		exchange_rate,
		item_price,
		line_item_price,
		item_tax,
		item_total_tax,
		promotion_discount,
		item_total_price,
		item_total_ship_price,
		payment_status,
		order_status,
		cron_name,
		cron_id
	)
VALUES
(
		in_chanl,
		in_channel_code,
		in_ownr_code,
		in_fby_id,
		in_account_id,
		in_order_no,
		in_location_id,
		in_selr_ordr_id,
		in_prchse_dt,
		in_payment_time,
		in_ordr_line_id,
		in_sku,
		in_barcode,
		in_ordr_itm_id,
		in_trnsactn_id,
		in_prod_nm,
		in_qntity_prchsed,
		in_curncy,
		in_exchng_rt,
		in_itm_price,
		in_line_itm_price,
		in_itm_tx,
		in_itm_totl_tx,
		in_promotion_discount,
		in_item_total_price,
		in_item_total_ship_price,
		in_financial_status,
		in_order_status,
		in_crn_name,
		in_crn_id
	) 
    ON DUPLICATE KEY
	UPDATE
		`channel` = 	in_chanl,
		channel_code = 	in_channel_code,
		owner_code = 	in_ownr_code,
		fby_user_id = 	in_fby_id,
		account_id = 	in_account_id,
		location_id = 	in_location_id,
		seller_order_id = 	in_selr_ordr_id,
		purchase_date = 	in_prchse_dt,
		payment_time = 	in_payment_time,
		order_line_item_id = 	in_ordr_line_id,
		sku = 	in_sku,
		barcode = 	in_barcode,
		order_item_id = 	in_ordr_itm_id,
		transaction_id = 	in_trnsactn_id,
		product_name = 	in_prod_nm,
		quantity_purchased = 	in_qntity_prchsed,
		currency = 	in_curncy,
		exchange_rate = 	in_exchng_rt,
		item_price = 	in_itm_price,
		line_item_price = 	in_line_itm_price,
		item_tax = 	in_itm_tx,
		item_total_tax = 	in_itm_totl_tx,
		promotion_discount = 	in_promotion_discount,
		item_total_price = 	in_item_total_price,
		item_total_ship_price = 	in_item_total_ship_price,
		payment_status = 	in_financial_status,
		order_status = 	in_order_status,
		cron_name = 	in_crn_name,
		cron_id	= in_crn_id;

	SELECT * from channelconnector.order_details t
    Where t.fby_user_id = in_fby_id
    AND t.order_no = in_order_no
    AND t.seller_order_id = in_selr_ordr_id
    AND t.order_line_item_id = 	in_ordr_line_id
    AND t.order_item_id = 	in_ordr_itm_id
    AND t.sku = 	in_sku
	AND t.barcode = 	in_barcode    ;
    
    SET SQL_SAFE_UPDATES = 0;
    
    IF( var_channel_name like '%storeden%')
    THEN
		UPDATE channelconnector.order_details AS o,
		channelconnector.products AS p 
		SET 
			o.location_id = p.location_id,
			o.barcode = p.barcode
		WHERE
			o.sku = p.sku 
			AND o.fby_user_id = p.fby_user_id
			AND o.order_item_id = p.item_id
			;
    ELSE
		
		UPDATE channelconnector.order_details AS o,
		channelconnector.products AS p 
		SET 
			o.location_id = p.location_id,
			o.barcode = p.barcode
		WHERE
			o.sku = p.sku 
			AND o.fby_user_id = p.fby_user_id
			AND o.order_item_id = p.item_id
			AND p.location_id <> '0'
            AND p.location_id <> ''
            
			AND ( o.location_id IS NULL  OR o.location_id = '' OR o.location_id='0');
		END IF;    
	SET SQL_SAFE_UPDATES = 1;

END$$
DELIMITER ;
