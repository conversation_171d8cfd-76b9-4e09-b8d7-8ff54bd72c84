const helpers = require('../../../misc/helpers');
const logger = require('../../../misc/logger.js');
const miscConstants = require("../../../misc/constants.js");
const DashbiardService = require('../../../services/hcl/dashboardService.js');

exports.getDashboardData = async (req, res) => {
    try {
        const userData = req.user;
        const dashboardData = await DashbiardService.getDashboardData(userData);
       
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            dashboardData, 
            req.body
        );
    } catch (error) {
        console.error(`Error fetching dashboard data: ${error.message}`, error);
        logger.logError(`Error fetching dashboard data: ${error.message}`, error);
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            'Error fetching dashboard data', 
            req.body
        );
    }
};
