const dbpool = require('../../startup/db');
const miscConstants = require("../../misc/constants");
const logger = require("../../misc/logger.js");
const { GroupByShipingProviders } = require('../../misc/enums/orderStatusEnum');
const cache = require('../../misc/cache.js');
const { filter } = require('rxjs');
const { cacheTTL } = require('./manifestService.js');
const hcldb = process.env.INITIAL_CATALOG || "hcl";

class ShippingProvideService {

    static shippingProvidersConfig = {};

    /**
     * 
     * @returns 
     */
    static async addShippingProvider(data) {
        try {
            const query = `${hcldb}.AddShippingProvider`;
            const values = [
            data.name,
            data.providerCode.toUppercase(),
            data.providerType,
            data.description || null,
            data.isActive ?? 1,
            data.isDeleted ?? 0,
            data.apiKey || null,
            data.apiSecret || null,
            data.apiBaseUrl || null,
            data.contactEmail || null,
            data.contactPhone || null,
            data.createdBy,
            ];

            const results = await dbpool.executeProcedure(query, values);
            await this.clearProvidersCache();
            return results;
        } catch (error) {
            logger.logError('Error adding shipping provider:', error);
            throw new Error(`Failed to add shipping provider: ${error.message}`);
        }
    }
      
    /**
     * 
     * @param {*} data 
     * @returns 
     */
    static async updateShippingProvider(data) {
        try {
          const query = `${hcldb}.UpdateShippingProvider`;
          const values = [
            data.providerId,
            data.name,
            data.providerCode.toUppercase(),
            data.providerType,
            data.description || null,
            data.isActive ?? 1,
            data.isDeleted ?? 0,
            data.apiKey || null,
            data.apiSecret || null,
            data.apiBaseUrl || null,
            data.contactEmail || null,
            data.contactPhone || null,
            data.updatedBy,
          ];
      
          const result = await dbpool.executeProcedure(query, values);
          await this.clearProvidersCache();
          return result;
        } catch (error) {
          logger.logError('Error updating shipping provider:', error);
          throw new Error(`Failed to update shipping provider: ${error.message}`);
        }
    }
         
    static async getShippingProviders(isActiveFilter = true, useCache = true) {
        try {
            const cacheKey = `${miscConstants.CACHE.SHIPPING_PROVIDER.KEY}${isActiveFilter ? 'active' : 'all'}`;
            const cacheTTL = miscConstants.CACHE.SHIPPING_PROVIDER.TTL;
            
            if (useCache) {
                const cachedProviders = await cache.get(cacheKey);
                if (cachedProviders) {
                    
                    return cachedProviders;
                }
            }
            const sql = `${hcldb}.GetShippingProviders`;
            const [rows] = await dbpool.executeProcedure(sql, []);
            
            // Apply filter if includeInactive is false
            const providers = isActiveFilter ? rows.filter(provider => provider.isActive) : rows;
        
            await cache.set(cacheKey, providers, cacheTTL);
            return providers;
        } catch (error) {
            throw error;
        }
      }

     // Initialize the shipping providers configuration object
    static async initializeShippingProvidersConfig() {
        try {
          // Only load active providers
          const providers = await this.getShippingProviders();
      
          this.shippingProvidersConfig = providers.reduce((config, provider) => {
            config.byName[provider.name] = provider;
            config.byId[provider.id] = provider;
            return config;
          }, { byName: {}, byId: {} });
      
          logger.logInfo('Shipping providers config initialized:', this.shippingProvidersConfig);
          console.log('Shipping providers config initialized:', this.shippingProvidersConfig);
        } catch (error) {
          console.error('Error initializing shipping providers config:', error);
          logger.logError('Error initializing shipping providers config:', error);
          throw error;
        }
      }



    // Get shipping provider details by name
    static getShippingProviderByName(name) {
            const provider = this.shippingProvidersConfig.byName[name];
        if (!provider) {
            throw new Error(`Shipping provider with name ${name} not found`);
        }
        return provider;
    }
  
    /**
     * get shipping provider details by id
     * @param {*} providerId 
     * @returns 
     */
    static async getShippingProviderDeatils (providerId) {
        const provider = this.shippingProvidersConfig.byId[providerId];
        if (!provider) {
            throw new Error(`Shipping provider with ID ${providerId} not found`);
        }
        return provider;
    }

    static async getShippingProviderName(providerId) {
        try {
            const provider = this.getShippingProviderDeatils(providerId);
            if (!provider) {
                throw new Error('Shipping provider is not registered.');
            }
            return provider.name;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Clear the shipping providers cache
     */
    static async clearProvidersCache() {
        try {
          const allCacheKeys = [
            `${miscConstants.CACHE.SHIPPING_PROVIDER.KEY}all`,
            `${miscConstants.CACHE.SHIPPING_PROVIDER.KEY}active`
          ];
      
          for (const cacheKey of allCacheKeys) {
            const isCacheDeleted = await cache.delete(cacheKey);
      
            if (isCacheDeleted) {
              console.log(`Cache cleared successfully for key: ${cacheKey}`);
            } else {
              console.warn(`Cache key not found or already deleted: ${cacheKey}`);
            }
          }
        } catch (error) {
          logger.logError('Error clearing providers cache:', error);
        }
      }


    /**
     * 
     * @param {*} clientId 
     * @param {*} zoneId 
     * @param {*} weightSlabId 
     * @param {*} mode 
     * @param {*} orderType 
     * @param {*} priority1ProviderId 
     * @param {*} priority2ProviderId 
     * @param {*} priority3ProviderId 
     * @param {*} userId 
     * @returns 
     */
    static async addPriorities ( 
        clientId,
        zoneId,
        weightSlabId,
        mode,
        orderType,
        priority1ProviderId,
        priority2ProviderId,
        priority3ProviderId, 
        userId) {
        try {
            const sql = `${hcldb}.AddOrUpdateShippingProviderPriorities`;
            const result = await dbpool.executeProcedure(sql, [
                clientId,
                zoneId,
                weightSlabId,
                mode,
                orderType,
                priority1ProviderId,
                priority2ProviderId,
                priority3ProviderId,
                userId
            ]);

            return result;
        } catch (error) {
            throw  error;
        }
    }

    /**
     * 
     * @param {*} param0 
     * @param {*} userId 
     * @returns 
     */

    static async addBulkPriorities ( {clientId, weightSlabId, mode, orderType, priorities}, userId) {
        try {
            const sql = `${hcldb}.AddOrUpdateBulkShippingPriorities`;
            const prioritiesJson = JSON.stringify(priorities);
            const inputs = [ clientId, weightSlabId, mode, orderType, prioritiesJson, userId ];
            const result = await dbpool.executeProcedure(sql, [inputs]);

            // Cache Key and Data
            const cacheData = await this.getPriorities(clientId, orderType, mode, 0, 1); // Fetch fresh data and add in cache
            return result;
        } catch (error) {
            throw  error;
        }
    }

    /**
     * Fetch priorities from the database (And set the cache and update).
     * @param {number} clientId - Client ID.
     * @param {string} orderType - Order Type (B2C, B2B).
     * @param {string} mode - Mode of transport (air, surface).
     * @param {string} refresh - 0, 1 if 1 refresh the cahed data
     * @returns {Array} - List of priorities.
     */
    static async getPriorities (clientId, orderType, mode, flatData =0, refresh = 0) {
        try {
            const cacheKey = `${miscConstants.CACHE.SHIPPING_PRIORITIES.KEY}_client_${clientId}_priorities_${orderType.toLowerCase()}_${mode.toLowerCase()}`;
            const groupCacheKey = `${miscConstants.CACHE.SHIPPING_PRIORITIES.GROUPKEY}_client_${clientId}_priorities_${orderType.toLowerCase()}_${mode.toLowerCase()}`;
            const cacheTTL = miscConstants.CACHE.SHIPPING_PRIORITIES.TTL;


             //console.log(refresh);
            // Check if data is already in cache
            if( refresh === 0) {
                // console.log(refresh);
                 if(flatData){
                 //    console.log(flatData);
                     const cachedData = cache.get(cacheKey);
                     if (cachedData) {
                        return cachedData;
                     }
                     
                 } else {
                     const groupedCachedData = cache.get(groupCacheKey);
                     if (groupedCachedData) {
                        return groupedCachedData;
                     }
                 }    
             }
         
            // Fetch from database
            const sql = `${hcldb}.GetShippingPrioritiesByClient`;
            const [rows] = await dbpool.executeProcedure(sql, [clientId, orderType, mode]);
            // Group the data by zone and weight slab
            const groupedData = rows.reduce((result, row) => {
                const { weightSlabId, zoneCode, ...priorities } = row;

                if (!result[weightSlabId]) {
                    result[weightSlabId] = {};
                }
                if (!result[weightSlabId][zoneCode]) {
                    result[weightSlabId][zoneCode] = [];
                }

                result[weightSlabId][zoneCode].push(priorities);

                return result;
            }, {});

            // Cache the grouped data
            cache.set(groupCacheKey, groupedData, cacheTTL);
            cache.set(cacheKey, rows, cacheTTL);
            if(flatData) {
                return rows;
            } else {
                return groupedData;
            }
        
        } catch (error) {
            throw  error;
        }
    }

    /**
     * 
     * @param {*} clientId 
     * @param {*} weightSlabId 
     * @param {*} zoneId 
     * @param {*} mode 
     * @param {*} orderType 
     * @returns 
     */
    static async getShippingProviderByPriority(clientId, weightSlabId, zoneId, mode = 'surface', orderType = 'B2C') {
        try {

            const prioritiesData = await this.getPriorities(clientId, orderType, mode, 1);
            // Validate that the cache is an array
            if (!Array.isArray(prioritiesData)) {
                throw new Error('Invalid priorities. Expected an array.');
            }
        
            // return weightSlabs;
            const prioritiesProviders = await prioritiesData.find( async (priorities) => 
                priorities.zoneId == zoneId &&
                priorities.weightSlabId == weightSlabId 
            );
        
            // If no matching weight slab is found, throw an error
            if (!prioritiesProviders) {
                throw new Error(`No prioritis added for the weight slab, ID ${weightSlabId} and zone ID ${zoneId}.`);
            } 

            const priorities = [
                prioritiesProviders.priority1ProviderId,
                prioritiesProviders.priority2ProviderId,
                prioritiesProviders.priority3ProviderId
            ].filter(value => value !== null);
            
            //console.log(priorities);

             // If no matching shipping is found, throw an error
             if (priorities.length === 0) {
                throw new Error(`No shipping provider added for the weight slab, ID ${weightSlabId} and zone ID ${zoneId}.`);
            } 

            return priorities;

        } catch(error) {
            throw error;
        }    
    }
}

(async () => {
    try {
        await ShippingProvideService.initializeShippingProvidersConfig();
        console.log('Shipping providers config initialized successfully.');
    } catch (error) {
        console.error('Failed to initialize shipping providers config:', error);
        process.exit(1); // Exit the application if initialization fails
    }
})();

module.exports = ShippingProvideService;      