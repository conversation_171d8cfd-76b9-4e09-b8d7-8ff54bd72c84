DROP PROCEDURE IF EXISTS channelconnector.updateOrderDetailStatus;

DELIMITER $$
CREATE PROCEDURE channelconnector.updateOrderDetailStatus(
	`in_fby_id` VARCHAR(128), 
	`in_order_no` VARCHAR(256), 
	`in_crn_name` VARCHAR(60), 
	`in_crnid` VARCHAR(100), 
	`in_time` DATETIME
)
BEGIN
	   SET SQL_SAFE_UPDATES = 0;
       
		UPDATE order_details 
		SET 
			`status` = 1,
			count = 0,
			fby_error_flag = 0,
			cron_name = in_crn_name,
			updated_at = NOW(),
			cron_id = in_crnid
		WHERE
			fby_user_id = in_fby_id
			AND order_no = in_order_no
			AND is_trackable = 1;
    
		SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;
