const helpers = require('../../../misc/helpers');
const miscConstants = require("../../../misc/constants");
const CodPayoutService = require('../../../services/hcl/codPayoutService');
const { throwError } = require('rxjs');

exports.createCodPayoutEntry = async (req, res, next) => {
    try {
        const { orderId, awb, invoiceNum, codAmount, deliveredAT } = req.body;

        if (!orderId || !awb || !invoiceNum || !codAmount) {
            helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                 miscConstants.ERRORCODES.BAD_REQUEST, 
                "All fields are required", req.body);
        }

        const clientId = req.user.clientId;
        const organizationId = req.user.organizationId;

        const result = await CodPayoutService.createCodPayoutEntry(clientId, orderId, awb, invoiceNum, codAmount, deliveredAT, organizationId);
        return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, result); 
    } catch (error) {
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, req.body);
    }
};

exports.listCodPayouts = async (req, res, next) => {
    try {
        const { clientId = null, filter = null, page = 1, pageSize = 10 } = req.query;

        if (clientId && isNaN(clientId)) {
            helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                miscConstants.ERRORCODES.BAD_REQUEST, 
               "Client ID is shopuld be numeric", req.body);
        }

        const { payouts, totalRecords } = await CodPayoutService.listCodPayouts(clientId, filter, parseInt(page), parseInt(pageSize));
        return helpers.sendPaginationResponse(res, miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, payouts, 
            totalRecords, page, pageSize);

    } catch (error) {
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, req.query);
    }
};

// Log COD Payout
exports.logCodPayout = async (req, res, next) => {
    try {
        const { clientId, orderIds, totalPayoutAmount, payoutDate } = req.body;

        if (!clientId || !orderIds || !totalPayoutAmount || !payoutDate) {
            helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                miscConstants.ERRORCODES.BAD_REQUEST, 
               "All fields are required", req.body);
        }

        const result = await CodPayoutService.logCodPayout(clientId, orderIds, totalPayoutAmount, payoutDate);
        return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, miscConstants.SUCESSSMESSAGES.INSERT, result, req.body);
    } catch (error) {
       return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, req.body);
    }
};

// List COD Payout History
exports.listCodPayoutHistory = async (req, res, next) => {
    try {
        const { clientId, page = 1, pageSize = 10 } = req.query;

        if (!clientId) {
            helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                miscConstants.ERRORCODES.BAD_REQUEST, 
               "Client ID is required", req.query);
        }

        const { payoutsHistory, totalRecords } = await CodPayoutService.listCodPayoutHistory(clientId, parseInt(page), parseInt(pageSize));

        return helpers.sendPaginationResponse(res, miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, payoutsHistory, 
            totalRecords, page, pageSize);

    } catch (error) {
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, req.query);
    }
};

 exports.getCodPayoutSums = async(req, res) => {
    try {
        const { clientId } = req.query;
        
        if (!clientId) {
            return helpers.sendError(
                res, 
                miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
                miscConstants.ERRORCODES.BAD_REQUEST, 
               "Client ID is required", req.query
            );
        }

        const result = await CodPayoutService.getCodPayoutSums(clientId);
        
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.INSERT, 
            result, req.query
        );
    } catch (error) {
        console.error('Error in getCodPayoutSums:', error);
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            `Failed to retrieve COD Payout Sums: ${error.message}`, 
            req.query
        );
    }
};
