const { upsertWeightSlabSchema, getWeightSlabsSchema } = require('./validators/weightSlabValidator');
const WeightSlabService = require('../../../services/hcl/weightSlabService');
const Constants = require("../../../misc/constants.js");
const helpers = require('../../../misc/helpers');

// Insert/Update Weight Slab
exports.upsertWeightSlab = async (req, res) => {
    try {
        // Validate request body
        const { error, value } = upsertWeightSlabSchema.validate(req.body, { abortEarly: false });
        if (error) {
            helpers.sendError(res, 400, 'Validation Error', error.details.map((err) => err.message), req.body);
        }
        // Proceed with the service call
        const result = await WeightSlabService.upsertWeightSlab(value, req.user.id);
        helpers.sendSuccess(res, 200, result.message, result, req.body);
    } catch (error) {
        if (error.sqlState === '45000') {
            helpers.sendError(res, 404, 'Duplicate', error.message, req.body);
        } else {
            helpers.sendError(res, 500, 'Server Error', error.message, req.body);
        }
    }
};

// Get Weight Slabs by Client ID
exports.getWeightSlabs = async (req, res) => {
    try {
        const { error, value } = getWeightSlabsSchema.validate(req.query, { abortEarly: false });
        if (error) {
            helpers.sendError(res, 400, 'Validation Error', error.details.map((err) => err.message), req.query);
        }
        const { clientId = null, shippingProviderId = null, mode = null } = req.query;
        const result = await WeightSlabService.getWeightSlabs(clientId, shippingProviderId, mode);
        return helpers.sendSuccess(res, Constants.HTTPSTATUSCODES.OK, Constants.SUCESSSMESSAGES.GET, result, req.query);
    } catch (error) {
        return helpers.sendError(res, Constants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            Constants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.query);
    }
};