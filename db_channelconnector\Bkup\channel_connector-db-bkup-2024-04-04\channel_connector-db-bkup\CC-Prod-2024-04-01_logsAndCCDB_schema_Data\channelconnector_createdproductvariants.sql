-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `createdproductvariants`
--

DROP TABLE IF EXISTS `createdproductvariants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `createdproductvariants` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `channel` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `domain` varchar(64) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `owner_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `sku` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `barcode` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `item_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `title` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `item_product_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `inventory_item_id` varchar(127) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `location_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '0',
  `previous_inventory_quantity` int DEFAULT NULL,
  `inventory_quantity` int DEFAULT NULL,
  `image` text CHARACTER SET utf8 COLLATE utf8_bin,
  `price` decimal(10,2) DEFAULT NULL,
  `count` int unsigned NOT NULL DEFAULT '0',
  `fby_error_flag` int unsigned NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `cron_name` varchar(60) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `isChanged` tinyint DEFAULT '0',
  `description` text,
  `specialPrice` decimal(10,2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `createdproductvariants_fby_user_id` (`fby_user_id`,`sku`,`item_product_id`,`title`,`inventory_item_id`,`item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `createdproductvariants`
--

LOCK TABLES `createdproductvariants` WRITE;
/*!40000 ALTER TABLE `createdproductvariants` DISABLE KEYS */;
INSERT INTO `createdproductvariants` VALUES (1,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_NPN_MFH001_E8480_BJ999','9957149590000','44191111217426','Portafoglio Tascabile dal design Slim - Sirolo','8045171835154','46239245500690','74186719506',-1,0,'40052068745490',26.00,0,0,0,'create_ProductVariant_Shopify','0c83f18d-00ec-413e-b62e-c890664482fe','2022-12-08 18:45:11',NULL,0,'',17.90),(2,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_NPN_MFH002_J3704_BJ999','9957149590024','44191191793938','Portafoglio Tascabile Verticale dal design Slim - Urbino','8045204832530','46239325978898','74186719506',-1,0,'40052170817810',27.00,0,0,0,'create_ProductVariant_Shopify','2ba2796c-ecdc-4923-afa2-7201527abc3f','2022-12-08 19:01:04',NULL,0,'',18.90),(3,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_BTA_MCD471_J3704_T8','9951310205342','44192929874194','Chelsea BATA in pelle per Uomo','8045845119250','46241063600402','74186719506',-1,0,'40054486008082',95.00,0,0,0,'create_ProductVariant_Shopify','0b7d0140-fe7a-497b-b872-2b90b060bf6b','2022-12-09 00:30:07',NULL,0,'',95.00),(4,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_NPN_MFH003_A2682_BJ999','9957149590031','44196497228050','Portafoglio Tascabile Verticale dal design Slim - Ancona','8047555576082','46244634591506','74186719506',-1,0,'',23.90,0,0,0,'create_ProductVariant_Shopify','a1942724-f042-47b8-983b-a316ac369654','2022-12-09 12:19:09',NULL,0,'',NULL),(8,'41','Shopify IT','https://demo-reseller.myshopify.com/','RDM','RDM_HGB_MEA001_I18499_F9','9927886007387','44207562064146','Occhiali Da Sole Uomo','8050452955410','46255730000146','74186719506',-1,0,'',205.00,0,0,0,'create_ProductVariant_Shopify','5b938b50-a330-4b13-b566-b295dc27820d','2022-12-12 08:45:03',NULL,0,'',205.00);
/*!40000 ALTER TABLE `createdproductvariants` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:26:50
