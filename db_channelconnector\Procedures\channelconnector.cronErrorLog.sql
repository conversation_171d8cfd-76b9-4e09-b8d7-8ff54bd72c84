DROP PROCEDURE IF EXISTS channelconnector.cronErrorLog;

DE<PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE channelconnector.`cronErrorLog`(
	IN `crn_name` VARCHAR(60), 
	IN `crnid` VARCHAR(100), 
	IN `err_type` VARCHAR(100), 
	IN `err_msg` TEXT, 
	IN `fby_id` VARCHAR(128)
)
BEGIN

	IF(CHAR_LENGTH(err_msg) > 2048) THEN
		set err_msg = SUBSTRING(err_msg,2048);
    END IF;
    
	INSERT INTO cron_error_log(fby_user_id,cron_name,cron_id,type_error,error_message) VALUES(fby_id,crn_name,crnid,err_type,err_msg);
    
END$$
DELIMITER ;
