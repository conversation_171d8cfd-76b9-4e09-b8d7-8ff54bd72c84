// /shippingPartners/dtdc/dtdcService.js
const BaseService = require('../baseService');
const {CourierDataTransformer, formatDateTime} = require('../transformer');
const OrderService = require('../../hcl/orderService.js');
const { getDtdcConfig } = require('../../../misc/shippingConfig');
const { PAYMENT_MODES, ORDER_TYPES } = require('../../../misc/enums/orderStatusEnum');
const logger = require('../../../misc/logger.js');
const cache = require('../../../misc/cache.js');
const helpers = require('../../../misc/helpers.js');
const miscConstants = require('../../../misc/constants.js');
const axios = require('axios');
const xml2js = require('xml2js');
const { result } = require('lodash');

/**
 * DTDC services
 */
class DTDCService extends BaseService {

    constructor(providerDetails, type ) {

        const config = getDtdcConfig();
        super(config, type, providerDetails);

        if (!config.customerCode || !config.apiKey || !config.accessToken || !config.serviceType) {
            logger.logError(`DTDC: Missing required API configuration.`);
            throw new Error("Missing required API configuration.");
        }

        this.shippingProviderId = providerDetails?.id || config.shippingProviderId; // Example value
        this.shippingProviderName = providerDetails?.name || config.shippingProviderName;
        this.customerCode = config.customerCode;
        this.apiKey = config.apiKey;
        this.username = config.trackUserName;
        this.password = config.trackPassword;
        this.verNo = config.verNo;
        this.serviceType = config.serviceType;
        this.movementType = config.movementType;
        this.getUrls = config.getUrls(this.environment);
        this.accessToken = config.accessToken;
        this.cookie = config.cookie;
        this.authCookie = config.authCookie;
        this.cacheKey = 'DTDC_JWT_TOKEN';
        this.cacheExpiry = 3600; // 1 hour in seconds

    }


    getProductDescription(serviceType) {
        const serviceMap = {
            [dtdcConfig.serviceType.b2c.priority]: 'B2C Air Priority',
            [dtdcConfig.serviceType.b2c.express]: 'B2C Surface Express',
            [dtdcConfig.serviceType.b2c.premium]: 'B2C Premium Delivery',
            [dtdcConfig.serviceType.b2c.groundEconomy]: 'B2C Ground Economy',
            [dtdcConfig.serviceType.b2c.groundExpress]: 'B2C Ground Express',
            [dtdcConfig.serviceType.b2c.gec]: 'B2C Heavy Movement',
            [dtdcConfig.serviceType.b2c.stdExpA]: 'B2C Lite Movement',
            [dtdcConfig.serviceType.document.priority]: 'Document Air Priority',
            [dtdcConfig.serviceType.document.premium]: 'Document Premium',
            [dtdcConfig.serviceType.document.stdExpA]: 'Document Lite Movement',
            [dtdcConfig.serviceType.b2b.priority]: 'B2B Air Priority',
            [dtdcConfig.serviceType.b2b.premium]: 'B2B Premium',
            [dtdcConfig.serviceType.b2b.stdExpA]: 'B2B Lite Movement'
        };

        return serviceMap[serviceType] || 'DTDC Service';
    }

    getServiceType(order) {

        const isDocument = order?.isDocument || false;
        const shipmentType = isDocument ? 'DOCUMENT' : order?.orderType?.toUpperCase() === ORDER_TYPES.B2B
                                                    ? ORDER_TYPES.B2B : ORDER_TYPES.B2C;

        const expressMode = order?.shipmentDetails?.express?.toUpperCase() || miscConstants.SHIPPING.MODE.SURFACE.toUpperCase();
        const serviceLevel = expressMode === miscConstants.SHIPPING.MODE.AIR.toUpperCase() 
                                    ? 'express'
                                    : 'priority';                      
       
       if (!['DOCUMENT', 'B2C', 'B2B'].includes(shipmentType)) {
            throw new Error('Invalid shipment type. Use "document", "b2c", or "b2b"');
        }
         
        const serviceMap = {
            DOCUMENT: this.serviceType.DOCUMENT,
            B2C: this.serviceType.B2C,
            B2B: this.serviceType.B2B
        };

        const typeMap = {
            'priority': 'priority',
            'express': 'express',
            'premium': 'premium',
            'ground': 'groundExpress',
            'economy': 'groundEconomy',
            'gec': 'gec',
            'std': 'stdExpA',
            'lite': 'stdExpA'
        };
        
        const serviceKey = typeMap[serviceLevel] || serviceLevel;
        if (!serviceMap[shipmentType][serviceKey]) {
            throw new Error(`Service level '${serviceLevel}' not available for ${shipmentType} shipments`);
        }

        return serviceMap[shipmentType][serviceKey];
  
    }

     // Private helper methods
     _getAddressDetails(pickUpAddress, deliveryAddress, returnAddress, isReverse) {
        const origin = isReverse ? deliveryAddress : pickUpAddress;
        const destination = isReverse ? pickUpAddress : deliveryAddress;

        return {
            origin_details: this._formatAddress('origin', origin),
            destination_details: this._formatAddress('destination', destination),
            return_details: this._formatAddress('return', returnAddress)
        };
    }

    _formatAddress(type, addressData) {
        const base = {
            name: `${addressData.consigneeFirstName} ${addressData.consigneeLastName}`,
            phone: this.formatContactNumber(addressData.contact.consigneeContact),
           // alternate_phone: this.formatContactNumber(addressData.contact.alternateContact),
            alternate_phone: this.formatContactNumber(addressData.contact.consigneeContact),
            address_line_1: this.cleanAddress(addressData.address.addressLine1),
            address_line_2: this.cleanAddress(addressData.address.addressLine2),
            pincode: addressData.address.zip,
            city: addressData.address.city,
            state: addressData.address.state
        };

        if (type === 'return') {
            return {
                ...base,
                city_name: addressData.address.city,
                state_name: addressData.address.state,
                email: addressData.contact.email
            };
        }
        return base;
    }

    _getPaymentDetails(order) {
        const isCod = helpers.isPaymentModeCOD(order.paymentMode);
        return {
            cod_collection_mode: isCod ? 'CASH' : '',
            cod_amount: isCod ? parseFloat(order.shipmentDetails.order.collectibleCod) : 0
        };
    }

    _createSinglePackagePayload(base, pkg, order) {
        return {
            consignments: [{
                ...base,
                description: "Single Package shipment 1 Package (General Goods)",
                dimension_unit: pkg.dimensionUnit,
                length: parseFloat(pkg.length),
                width: parseFloat(pkg.width),
                height: parseFloat(pkg.height),
                weight_unit: pkg.weightUnit,
                weight: parseFloat(pkg.weight),
                declared_value: parseFloat(order.shipmentDetails.order.totalAmount),
                num_pieces: "1"
            }]
        };
    }

    _createMultiPackagePayload(base, packages, order) {
        const totalWeight = packages.reduce((sum, pkg) => sum + parseFloat(pkg.weight), 0);
        const totalLength = packages.reduce((sum, pkg) => sum + parseFloat(pkg.length), 0);
        const totalWidth = packages.reduce((sum, pkg) => sum + parseFloat(pkg.width), 0);
        const totaHeight = packages.reduce((sum, pkg) => sum + parseFloat(pkg.height), 0);
        
        return {
            consignments: [{
                ...base,
                description: `Multi-package shipment (${packages.length} packages) (General Goods)`,
                dimension_unit: packages[0]?.dimensionUnit,
                length: totalLength,
                width: totalWidth,
                height: totaHeight,
                weight: totalWeight,
                weight_unit: packages[0]?.weightUnit,
                declared_value: parseFloat(order.shipmentDetails.order.totalAmount),
                num_pieces: packages.length.toString(),
                pieces_detail: packages.map(pkg => ({
                    package_reference: `${order.shipmentDetails.invoiceNo}-PKG${pkg.packageId}`,
                    length: parseFloat(pkg.length),
                    width: parseFloat(pkg.width),
                    height: parseFloat(pkg.height),
                    weight: parseFloat(pkg.weight),
                    declared_value: parseFloat(pkg.value || (order.shipmentDetails.order.totalAmount / packages.length))
                }))
            }]
        };
    }

    /**
     * generate JWT function
     * @returns 
     */
    async generateJWTToken() {
            let jwtToken = '';
    
            try {
                const cachedToken = cache.get(this.cacheKey);
                // if (cachedToken) {
                //     return cachedToken;
                // }
                // Set up request params
                const params = {
                    'username': this.username,
                    'password': this.password
                };
    
                const headers = {
                    'Content-Type': 'application/json',
                    'Cookie': this.authCookie,
                };

                // Get the authentication URL based on environment
                const authUrl = `${this.getUrls.authentication}?${new URLSearchParams(params).toString()}`;
               
                // Make the request
                const responseBody = await this.makeRequest(authUrl, headers, 'GET');
                if (responseBody) {
                    jwtToken = responseBody;
                    
                    if (jwtToken) {
                        // Store token in cache
                        cache.set(this.cacheKey, jwtToken, this.cacheExpiry);
                        logger.logInfo('DTDC: Token generated successfully!');
                    } else {
                        logger.logError('DTDC: Token not found in response');
                    }
                } else {
                    logger.logError('DTDC: Token not generated!');
                }
    
            } catch (error) {
                logger.logError(`DTDC: JWT Token Generation Error: ${error.message}`);
                throw new Error(`JWT Token Generation Error: ${error.message}`);
            }
    
            return jwtToken;
        }


       

     /**
     * Generate shipment object for both forward and reverse shipping
     * @param {*} order 
     * @param {*} isReverse 
     * @returns 
     */
     async generateShipmentObject(order, isReverse = false) {
        try {
            const packages = await OrderService.getPackageDetails(order);
            const { pickUpAddress, deliveryAddress, returnAddress } = await OrderService.getAddressFromOrder(order);

            //const isDocument = shipmentData.isDocument || false;

             // Determine service type based on shipment characteristics
            const serviceType = this.getServiceType(order);
            
            const baseConsignment = {
                customer_code: this.customerCode,
                service_type_id: serviceType,
                load_type: packages.length > 1 ? "NON-DOCUMENT" : "NON-DOCUMENT",
                commodity_id: "7",
                ...this._getAddressDetails(pickUpAddress, deliveryAddress, returnAddress, isReverse),
                ...this._getPaymentDetails(order),
                customer_reference_number: order.shipmentDetails.invoiceNo,
                invoice_number:  order.shipmentDetails.invoiceNo,
                reference_number: ""
            };

            return packages.length === 1 
                ? this._createSinglePackagePayload(baseConsignment, packages[0], order)
                : this._createMultiPackagePayload(baseConsignment, packages, order);
        } catch(error) {
            logger.logError(`Error in creating DTDC shipment object: ${error.message}`);
            throw new Error(`Error in creating shipment object: ${error.message}`);
        }
    }

    /**
     * Generate forward shipment payload
     */
    async generateForwardShipmentObject(order) {
        return this.generateShipmentObject(order, false);
    }

     /**
     * Generate return shipment payload
     */
    async generateReturnShipmentObject(order, returnReason) {
        const payload = await this.generateShipmentObject(order, true);
        payload.consignments[0].return_reason = returnReason;
        payload.consignments[0].is_return = true;
        return payload;
    }

     /**
     * Create return shipment
     */
     async createReturnShipment(order, returnReason) {
        try {
            logger.logInfo('DTDC: Start Return Pickup Request.', order);
            const shipmentObject = await this.generateReturnShipmentObject(order, returnReason);

            const params = {
                body: JSON.stringify(shipmentObject).replace(/\\\//g, '/'),
                headers: {
                    'api-key': this.apiKey,
                    'Content-Type': 'application/json',
                },
            };

            logger.logInfo('DTDC: Return shipment Payload', params.body);
            console.log(params.body.consignments);
            console.log(`hello ${params.body.consignments[0]?.origin_details}`);
            console.log(params.body.consignments[0]?.destination_details);
            console.log(params.body.consignments[0]?.return_details);
            console.log(params.headers);

            const url = `${this.baseUrl}${this.getUrls.generateWayBill}`;
            const responseBody = await this.makeRequest(url, params.headers, 'POST', params.body);
            
            logger.logInfo('DTDC: retutn shipment API Response - ', responseBody);
            
            if(responseBody.status === 'OK') {
                const result = responseBody.data?.[0];
                console.log(result);
                if ( result.success === false ) {
                    success = result.success;
                    referenceCode = result.reason;
                    message = result.message || 'An Unknow error occurred';
                    logger.logError(`DTDC: Response - AWB: ${message}`);
                } else {
                    success = result.success;
                    awbNumber = result.reference_number;
                    referenceCode = responseBody.status;
                    message = 'AWB generated successfully';
                }
            }    

            logger.logInfo(`DTDC: Response - AWB: ${awbNumber}, Invoice Number; ${referenceCode}, Message: ${message}`);
            return { success, awbNumber, tokenNumber, referenceCode, message };;
            
        } catch (error) {
            logger.logError(`DTDC: Return shipment error: ${error.message}`);
            throw new Error(`Return shipment creation failed: ${error.message}`);
        }
    }

    /**
     * 
     * @param {*} order 
     * @returns 
     */
    async createShipment(order) {
       
        let success = '';
        let awbNumber = '';
        let tokenNumber = '';
        let referenceCode = '';
        let message = '';
        let waybillInfo = null;

        try {
            
            logger.logInfo('DTDC: Start Forward Pickup Request.', order);

            const shipmentObject = await this.generateForwardShipmentObject(order);

            const params = {
                body: JSON.stringify(shipmentObject).replace(/\\\//g, '/'),
                headers: {
                    'api-key': this.apiKey,
                    'Content-Type': 'application/json',
                },
            };

            logger.logInfo(`DTDC: shipment Payload. Order Id: ${order.orderId}`, params.body);
            console.log(JSON.parse(params.body));
            //console.log(params.headers);
           
            // create url based on environment and make api request
            const url = `${this.baseUrl}${this.getUrls.generateWayBill}`;
            const responseBody = await this.makeRequest(url, params.headers, 'POST', params.body);
            logger.logInfo(`DTDC: shipment API Response. Order Id: ${order.orderId} - `, JSON.stringify(responseBody));
            console.log(responseBody);

            if(responseBody.status === 'OK') {
                const result = responseBody.data?.[0];
                waybillInfo = result;
                console.log(result);
                if ( result.success === false ) {
                    success = result?.success || false;
                    referenceCode = result.reason;
                    message = result.message || 'An Unknow error occurred';
                    logger.logError(`DTDC: Response - AWB: ${message}`);
                } else {
                    success = result.success;
                    awbNumber = result.reference_number;
                    referenceCode = responseBody.status;
                    message = 'AWB generated successfully';
                }
            }    

            logger.logInfo(`DTDC: Response - AWB: ${awbNumber}, Invoice Number; ${referenceCode}, Message: ${message}`, waybillInfo);
            return { success, awbNumber, tokenNumber, referenceCode, message, waybillInfo };;
        
        } catch (error) {
            logger.logError(`DTDC: Response - AWB: ${error.message}`);
            throw new Error(`Shipment Error: ${error.message}`);
        }       
    }

    /**
     * 
     * @param {*} awbs 
     * @returns 
     */
    async cancelAwb(awbs) {
        try {
            let result;
            // Validate input
            if (!awbs || !Array.isArray(awbs) || awbs.length === 0) {
                throw new Error('Invalid AWB data provided');
            }

            const payload = {
                'AWBNo': awbs, 
                'customerCode': this.customerCode  
            };

            const headers = {
                'api-key': this.apiKey,
                'Content-Type': 'application/json',
            };

            const url = `${this.baseUrl}${this.getUrls.cancelWaybill}`;
            logger.logInfo(`DTDC: Cancel awbs request url: ${url}   payload :`, payload);
            // Make API request using make request through axios
            const responseBody = await this.makeRequest(url, headers, 'POST', payload);
            //console.log(responseBody);
            logger.logInfo(`DTDC: Cancel awbs response:`, responseBody);
            if(responseBody.success === true) {
                result = {
                    success: responseBody.success,
                    awbs: awbs,
                    message: 'AWBs cancelled successfully'
                };
            } else {                
                result = {
                    success: responseBody.success,
                    awbs: awbs,
                    message: responseBody.failures?.[0].message ? responseBody.failures?.[0].message : 'An error occured cacelling AWBS',
                    reason: responseBody.failures?.[0].reason ? responseBody.failures?.[0].reason : 'INVALID_CONSIGNMENT_STATUS',
                    status: responseBody.failures?.[0].current_status > 0 ? responseBody.failures?.[0].current_status : 'INVALID_STATUS',
                    referenceCode: responseBody.failures?.[0].code > 0 ? responseBody.failures?.[0].code : '404',
                };
            }
            logger.logInfo(`DTDC: Cancel awbs returns:`, result);
            return result;
        } catch (error) {
           // console.error('Error in cancelAwb:', error);
            logger.logError(`DTDC: Error in cancelAwb: ${error.message}`, error);
            throw new Error(`Failed to cancel AWB: ${error.message}`);
        }
    }
    
    /**
     * DTDC track shipment function
     * @param {*} trackingNumber 
     * @param {*} format 
     * @returns 
     */
    
    async trackShipment(trackingNumber, format = 'json') {
        try {
            
            let finalResponse;

            logger.logInfo(`DTDC: Start Shipment Tracking. format: ${format}, trackingNumber: `, trackingNumber);
            
            // get tokem
            //const token = await this.generateJWTToken();
 
            // create headers
            const headers = {
                'X-Access-Token': this.accessToken,
                'Content-Type': 'application/json',
                'Cookie': this.cookie,
            };
            // create payload
            const payload = {
                strcnno: trackingNumber,
                addtnlDtl: 'Y',
                trkType: "cnno",
            };
            logger.logInfo(`DTDC: Shipment Tracking payload :`, payload);

            //get track shipment URL based on environment
            const url = this.getUrls.trackingJson;
            // make API call
            const responseBody = await this.makeRequest(url, headers, 'POST', payload);
            console.log(responseBody);
            logger.logInfo('DTDC: Track Shipment Response.: ', responseBody);
         
            if(responseBody.statusFlag === true && responseBody.status === 'SUCCESS') {
                finalResponse = {
                    success: responseBody.statusFlag,
                    data: {
                        trackHeader: {
                            awb: responseBody?.trackHeader?.strShipmentNo,
                            orderType: responseBody?.trackHeader?.strCNType,
                            serviceType: responseBody?.trackHeader?.strCNProduct,
                            status: responseBody?.trackHeader?.strStatus,
                            clientName: responseBody?.trackHeader?.strCNTypeName,
                            paymentMode: '',
                            orderAmount: '',
                            orderDate: formatDateTime(responseBody?.trackHeader?.strBookedDate),
                            orderId: responseBody?.trackHeader?.strRefNo,
                            shippingProviderName: this.shippingProviderName,
                            origin: responseBody?.trackHeader?.strOrigin,
                            destination: responseBody?.trackHeader?.strDestination,
                            customer: '',
                            timestamp: formatDateTime(responseBody?.trackHeader?.strStatusTransOn, responseBody?.trackHeader?.strStatusTransTime),
                            expectedDeliveryDate: formatDateTime(responseBody?.trackHeader?.strExpectedDeliveryDate)
                        }, 
                        trackingDetails: CourierDataTransformer.transform(this.shippingProviderName, responseBody.trackDetails)
                    }
                };
            } else {
                const errorDetails = responseBody.errorDetails;
                finalResponse= {
                    success: false,
                    trackingNumber: errorDetails?.[0]?.name == 'strShipmentNo' ? errorDetails?.[0]?.value : trackingNumber,
                    message: errorDetails?.[1]?.name == 'strError' ? errorDetails?.[1]?.value : 'An unknown error occurred.'
                };
            }
                
            logger.logInfo('DTDC: End track shipment Request. Return :', finalResponse);
            
            return finalResponse;

        } catch (error) {
            logger.logError(`DTDC: Error in track shipment request: ${error.message}`, error);
            console.error('DTDC: Error in track Shipment: ', error);
            throw new Error(`Error track shipment: ${error.message}`);
        }
    }

    // async calculateRate(orderData) {
    //     try {
    //         const payload = {
    //             custId: this.customerId,
    //             password: this.password,
    //             originPin: orderData.originPincode,
    //             destPin: orderData.address.pincode,
    //             weight: orderData.weight,
    //             productCode: orderData.productCode
    //         };

    //         const responseBody = await this.makeRequest('/rating/calculate', payload);
    //         return responseBody;
    //     } catch (error) {
    //         throw new Error(`DTDC Rate Calculation Error: ${error.message}`);
    //     }
    // }

    // async checkServiceability(pincode) {
    //     // DTDC-specific API call to check serviceability
    // }

}

module.exports = DTDCService;
