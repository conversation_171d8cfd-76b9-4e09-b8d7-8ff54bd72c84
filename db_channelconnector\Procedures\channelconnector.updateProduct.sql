DROP PROCEDURE IF EXISTS channelconnector.updateProduct;

DELIMITER $$
CREATE PROCEDURE channelconnector.`updateProduct`
(
	`in_crn_name` VARCHAR(60) ,
    `in_crnid` VARCHAR(100) ,
    `in_time` DATETIME,
	`in_fby_user_id` VARCHAR(60) 
)
BEGIN
	/*
		call channelconnector.`updateProduct`(
        'send_Products_Fby',
        '22dd4051-1ff6-44d5-b281-fe1ba55e10e7',
        now(),
        1002
        );
    */
	SET SQL_SAFE_UPDATES = 0;
	UPDATE products AS P,temp_master_inventory AS TI 
	SET 
	
		P.previous_inventory_quantity = P.inventory_quantity,
		P.inventory_quantity = TI.quantity,
		#P.barcode = TI.barcode,
		P.cron_name = in_crn_name,
		P.cron_id = in_crnid,
		P.updated_at = in_time 
	WHERE 
		P.sku = TI.skucode 
        AND 1 = (
        case when P.barcode>0 AND TI.barcode > 0 
			then case when P.barcode = TI.barcode 
				then 1 
                else 0 
			end 
		else 1 end
        
        )
        AND P.fby_user_id = in_fby_user_id
        AND TI.fby_user_id = in_fby_user_id
        AND P.fby_user_id = TI.fby_user_id
		AND (CASE WHEN P.inventory_quantity IS  NULL THEN 0 ELSE P.inventory_quantity  END) <> TI.quantity; 
	SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;