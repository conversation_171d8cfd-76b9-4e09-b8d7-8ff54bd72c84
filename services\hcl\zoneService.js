const dbpool = require('../../startup/db');
const hcldb = process.env.INITIAL_CATALOG || "hcl";
const miscConstants = require("../../misc/constants");
const logger = require('../../misc/logger.js');
const helpers = require('../../misc/helpers');
const cache = require('../../misc/cache.js');
const { chain } = require('lodash');

class ZoneService {
    static async upsertZone (id, 
        shippingProviderId, zoneName, zoneCode, zoneTat,
        region, isActive, createdBy, updatedBy) {
        try{
            const query = `${hcldb}.UpsertZone`;
            const result = await dbpool.executeProcedure(query, 
                [
                    id ,
                    shippingProviderId,
                    zoneName,
                    zoneCode,
                    zoneTat.toUpperCase(),
                    region,
                    isActive,
                    createdBy,
                    updatedBy
                ]
            );
            const cacheKey = `${miscConstants.CACHE.ZONES.KEY}${shippingProviderId}`;
            await cache.delete(cacheKey);
            return result[0];
        } catch (error) {
            console.error('Error in zoneService.upsertZone:', error.message);
            throw error;
        }  
    }

    static async getZones ( shippingProviderId ) {
        try{
            let zones;
            const cacheKey = `${miscConstants.CACHE.ZONES.KEY}${shippingProviderId}`;
            const cacheTTL = miscConstants.CACHE.ZONES.TTL;
            // Check if data is already in cache
            zones = await cache.get(cacheKey);
            if (zones) {
                return zones;
            }
    
            const query = `${hcldb}.GetZones`;
            [zones] = await dbpool.executeProcedure(query, [ shippingProviderId ]);
            cache.set(cacheKey, zones, cacheTTL);
            return zones;
            
        } catch (error) {
            throw error;
        }        
    }

    static async getZoneByCode(shippingProviderId, zoneCode, refresh = false) {
        try {
            
            const zones = await this.getZones(shippingProviderId, refresh);

            const zone = zones.find(z => z.zoneCode === zoneCode);

            if (!zone) {
                throw new Error(`Zone with code ${zoneCode} not found`);
            }

            return zone; // Return the specific zone
        } catch (error) {
            throw error;
        }
    }
    
    static async getZoneById (id) {
        try{
            const query = `${hcldb}.GetZoneById`;
            const [zone] = await dbpool.executeProcedure(query, [id]);
            return zone[0];
        } catch (error) {
            throw error;
        }        
    }

    /**
     * 
     * @param {*} data 
     * @returns 
     */
    static async upsertDeliveryDays(data) {
        const { 
          sourceZoneId, 
          destinationZoneId, 
          orderType, 
          providerId,
          surface,
          air,
          rail,
          dp,
          oda
        } = data;
        
        try {
            const query = `${hcldb}.UpsertZoneDeliveryDays`;
            const result = await dbpool.executeProcedure(query, 
                [
                    sourceZoneId,
                    destinationZoneId,
                    orderType.toUpperCase(),
                    providerId,
                    surface || null,
                    air || null,
                    rail || null,
                    dp || null,
                    oda || null
                ]
          );
          
          // Clear cache for this combination
          const cachePrefix = miscConstants.CACHE.ZONES_DELIVERY_DAYS.KEY;
          const cacheKey = `${cachePrefix}${sourceZoneId}:${destinationZoneId}:${orderType}:${providerId}`;
          await cache.delete(cacheKey);
          
          // Clear all-inclusive caches that might contain this data
          await cache.delete(`${cachePrefix}${sourceZoneId}:${destinationZoneId}:${orderType}:all`);
          await cache.delete(`${cachePrefix}${sourceZoneId}:${destinationZoneId}:all:all`);
          await cache.delete(`${cachePrefix}all:all:all:${providerId}`);
          await cache.delete(`${cachePrefix}all:all:${orderType}:${providerId}`);
          await cache.delete(`${cachePrefix}${sourceZoneId}:all:all:all`);
          await cache.delete(`${cachePrefix}all:all:all:all`);
          
          return result[0][0];
        } catch (error) {
            throw error;
        }    
    }

    /**
     * 
     * @param {*} filters 
     * @param {*} useCache 
     * @returns 
     */
    static async getDeliveryDays(filters, useCache = true) {
       // console.log(filters);
        const { sourceZoneId, destinationZoneId, orderType, providerId } = filters;
        const cachePrefix = miscConstants.CACHE.ZONES_DELIVERY_DAYS.KEY;
        const cacheTTL = miscConstants.CACHE.ZONES_DELIVERY_DAYS.TTL;
        // Check Redis cache first if caching is enabled
        if (useCache) {

            const cacheKey = `${cachePrefix}${sourceZoneId || 'all'}:${destinationZoneId || 'all'}:${orderType || 'all'}:${providerId || 'all'}`;
            const cachedData = await cache.get(cacheKey);
            
            if (cachedData) {
                logger.logInfo(`Using cached data for ${cacheKey}`);
                return cachedData;
            }
        }
        
        try {
            const query = `${hcldb}.GetZoneDeliveryDays`;
            const [results] = await dbpool.executeProcedure(query, 
                [
                    sourceZoneId ?? null,  // Ensures null if undefined
                    destinationZoneId ?? null,
                    orderType?.toUpperCase() ?? null,  // Prevents error if orderType is undefined
                    providerId ?? null
                ]
            );   

            // console.log(results[0]);
            const data = results;
            // Store in Redis if caching is enabled
            if (useCache) {
                const cacheKey = `${cachePrefix}${sourceZoneId || 'all'}:${destinationZoneId || 'all'}:${orderType || 'all'}:${providerId || 'all'}`;
                await cache.set(cacheKey, data, cacheTTL); // 1 hour expiry
            }
            
            return data;
        } catch (error) {
            logger.logError(`Error fetching delivery days: ${error.message}`);
            throw error;
        } 
    }
    
    /**
     * 
     * @param {*} sourceZoneId 
     * @param {*} destinationZoneId 
     * @param {*} orderType 
     * @returns 
     */
    static async getOptimalDelivery(sourceZoneId, destinationZoneId, orderType) {
        // Try to get from cache first
        const cacheKey = `optimal_delivery:${sourceZoneId}:${destinationZoneId}:${orderType}`;
        const cachedData = await cache.get(cacheKey);
    
        if (cachedData) {
            logger.logInfo(`Using cached optimal delivery data for ${cacheKey}`);
            return cachedData;
        }
    
        try {
            const query = `${hcldb}.GetOptimalDelivery`;
            const results = await dbpool.executeProcedure(query, 
                [
                    sourceZoneId,
                    destinationZoneId,
                    orderType?.toUpperCase() ?? null
                ]
            );
            
            console.log(results);
             // Format the result
            const response = {
                sourceZone: {
                    id: results[1][0]?.source_zone_id || sourceZoneId,
                    name: results[1][0]?.source_zone_name || 'Unknown',
                    code: results[1][0]?.source_zone_code || 'Unknown',
                },
                destinationZone: {
                    id: results[1][0]?.destination_zone_id || destinationZoneId,
                    name: results[1][0]?.destination_zone_name || 'Unknown',
                    code: results[1][0]?.destination_zone_code
                },
                order_type: orderType.toUpperCase(),
                deliveryOptions: results[0],
                fastestOption: results[0].length > 0 ? results[0][0] : null
            };
            // Cache the result
            await cache.set(cacheKey, response, 1800); // 30 min expiry
            
            return response;
        } catch (error) {
            logger.logError(`Error fetching optimal delivery: ${error.message}`);
            throw error;
        } 
    }
      
    /**
    * 
    * @param {*} pattern 
    */
    static async clearCache(pattern = 'delivery_days:*') {
        const keys = await cache.keys(pattern);
        if (keys.length > 0) {
            await cache.delete(keys);
            logger.info(`Cleared ${keys.length} cache entries with pattern: ${pattern}`);
        }
    }
 
}



module.exports = ZoneService;   