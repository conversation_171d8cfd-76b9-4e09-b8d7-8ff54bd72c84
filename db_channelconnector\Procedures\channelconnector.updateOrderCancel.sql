DROP PROCEDURE IF EXISTS channelconnector.updateOrderCancel;

DELIMITER $$
CREATE PROCEDURE channelconnector.updateOrderCancel( 
	`in_fby_user_id` VARCHAR(128),
	`in_ordr_number` VARCHAR(256),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
	`in_time` DATETIME
)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE order_details AS od 
	SET 
		od.is_canceled_fby = 1,
		od.count = 0,
		od.fby_error_flag = 0,
		od.cron_name = in_crn_name,
		od.updated_at = in_time,
		od.cron_id = in_crnid
	WHERE
		od.fby_user_id = in_fby_user_id
		AND od.order_no = in_ordr_number;
    
	UPDATE order_masters AS om 
	SET 
		om.is_canceled = 1,
		om.fby_send_status = 1,
		om.cron_name = in_crn_name,
		om.updated_at = in_time,
		om.cron_id = in_crnid
	WHERE
		om.fby_user_id = in_fby_user_id
		AND om.order_no = in_ordr_number;
    
    SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;
