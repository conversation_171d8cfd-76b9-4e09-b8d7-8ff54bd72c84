DROP PROCEDURE IF EXISTS channelconnector.fbyOrderTrackErrorManage;

DELIMITER $$
CREATE PROCEDURE channelconnector.fbyOrderTrackErrorManage(
	`in_fby_id` VARCHAR(128),
	`in_ordr_number` VARCHAR(256),
	`in_exist` TINYINT(4) UNSIGNED,
	`in_crn_name` VARCHAR(60),
	`in_crn_id` VARCHAR(100),
	`in_error_type` VARCHAR(60),
	`in_error_msg` TEXT,
	`in_time` DATETIME
)
BEGIN
	/*
		
   CALL fbyOrderErrorManage('8','4667998896386',1,'send_Orders_Fby','809d8c84-1ea4-43bc-9a80-72221a89adc5','catch error','{\"4667998896386\":{\"ownerCode\":\"lowner con codice \\\"FDM\\\" non esiste.\"}}','2022-03-03 12:22:02');

	*/
    SET SQL_SAFE_UPDATES = 0;
    
    IF in_exist = 1 
    THEN
   
		UPDATE order_details AS od 
		SET 
			od.count = od.count + 1,
			od.updated_at = NOW()
		WHERE
			od.cron_id = in_crn_id
				AND od.order_no = in_ordr_number
				AND od.is_trackable = 1;
     
		UPDATE cron_error_log AS cl 
		SET 
			cl.type_error = in_error_type,
			cl.error_message = in_error_msg
		WHERE
			cl.cron_id = in_crn_id;
		
	ELSE
    
		UPDATE order_details AS od 
		SET 
			od.fby_error_flag = 1,
			od.count = 1,
			od.cron_name = in_crn_name,
			od.cron_id = in_crn_id,
			od.updated_at = NOW()
		WHERE
		od.fby_user_id = in_fby_id
			AND od.order_no = in_ordr_number
			AND od.is_trackable = 1;
	END IF;
    
   
END$$
DELIMITER ;
