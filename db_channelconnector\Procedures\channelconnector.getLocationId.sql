DROP PROCEDURE IF EXISTS channelconnector.getLocationId;

DE<PERSON><PERSON><PERSON>ER $$
CREATE  PROCEDURE channelconnector.getLocationId(
	IN `fby_id` VARCHAR(128), 
	IN `skuid` VARCHAR(256),
    IN `in_order_item_id` VARCHAR(256)
)
BEGIN
/*
		call channelconnector.getLocationId(9,'OSM21A008','41090369585309');
    */
	SELECT 
		*
	FROM
		products
	WHERE
		sku = skuid 
		AND fby_user_id = fby_id
		and item_id = in_order_item_id;
END$$
DELIMITER ;
