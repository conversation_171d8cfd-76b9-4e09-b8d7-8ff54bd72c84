const dbpool = require('../../startup/db');

const { GroupStatusesByState, GroupStatusesByStatuses, GroupDataMasterByTypes } = require('../../misc/enums/orderStatusEnum');
const hcldb = process.env.INITIAL_CATALOG || "hcl";
const miscConstants = require("../../misc/constants");
const cache = require('../../misc/cache.js');
const logger = require('../../misc/logger.js');

class StatusService {

    static dataMasterConfig = {
        modeArray: [ 'air', 'surface', 'rail', 'dp' ],
        orderTypeArray: [ 'B2B', 'B2C' ],
        mode: { AIR: 'air', SURFACE: 'surface', RAIL: 'rail', DP: 'dp' },
        orderType: { B2B: 'B2B', B2C: 'B2C' }
    };

    static async getAllStatuses(type = 'all', groupBy = 0, callback) {
        try {
          
            const groupKey = `${miscConstants.CACHE.ORDER_STATUSES.GROUPKEY}_${type}`;
            const cacheKey = `${miscConstants.CACHE.ORDER_STATUSES.KEY}_${type}`;
            const cacheTTL = miscConstants.CACHE.ORDER_STATUSES.TTL;
            if(groupBy) {
                const cachedStatusesByState= cache.get(groupKey);
                if (cachedStatusesByState) {
                    return cachedStatusesByState;
                }
            } else {
                const cachedOrdersStatuses = cache.get(cacheKey);
                if (cachedOrdersStatuses) {
                    return cachedOrdersStatuses;
                }
            }  

            const sql = `${hcldb}.GetAllStatuses`;
            const inputs = [type];
            let results = await dbpool.executeProcedure(sql, inputs);
            if(groupBy) {
              results =  GroupStatusesByState(results[0]);
              cache.set(groupKey, results);
            }else {
                results = results[0];
                cache.set(cacheKey, results, cacheTTL);
            }    
            return results;

        } catch (error) {
                throw error;
        }
    }

    static async statusesMaster(type = 'all') {
        try {
            const cacheKey = `${miscConstants.CACHE.STATUSES_MASTER.KEY}_${type}`;
            const cacheTTL = miscConstants.CACHE.STATUSES_MASTER.TTL;
        
            const cachedOrdersStatuses = cache.get(cacheKey);
            if (cachedOrdersStatuses) {
                return cachedOrdersStatuses;
            }
            
            const sql = `${hcldb}.GetAllStatuses`;
            const inputs = [type];
            let results = await dbpool.executeProcedure(sql, inputs);
            results =  GroupStatusesByStatuses(results[0]);
            cache.set(cacheKey, results, cacheTTL);
            
            return results;

        } catch (error) {
                throw error;
        }
    }

    static async getDataMasters (type = 'all') {
        try {
            const cacheKey = `${miscConstants.CACHE.ORDER_TYPE_MASTER.KEY}_${type}`;
            const cacheTTL = miscConstants.CACHE.ORDER_TYPE_MASTER.TTL;
        
            const cachedOrderTypeMaster = cache.get(cacheKey);
            if (cachedOrderTypeMaster) {
                return cachedOrderTypeMaster;
            }
            
            const sql = `${hcldb}.GetDataMasters`;
            const inputs = [type];
            let results = await dbpool.executeProcedure(sql, inputs);
            // Ensure data is in expected format before caching
            if (results && results.length > 0) {
                results = GroupDataMasterByTypes(results[0]); 
                cache.set(cacheKey, results, cacheTTL);
                console.log(results);
                return results;
            }
            return {}; // Return empty object if no data found

        } catch (error) {
                throw error;
        }
    }

    static async initializeDataMasterConfig() {
        try {
      
            const masters = await this.getDataMasters();
            
            this.dataMasterConfig = {
                modeArray: masters.mode || [],      // Store mode as an array
                orderTypeArray: masters.order_type || [], // Store order types as an array
                mode: {},       // Object to store lookup by uppercase key (for fast lookup)
                orderType: {}   // Object to store lookup by uppercase key (for fast lookup)
            };
    
            // Populate the 'mode' and 'orderType' objects for fast lookup
            masters.mode.forEach(modeItem => {
                this.dataMasterConfig.mode[modeItem.toUpperCase()] = modeItem;  
            });
    
            masters.order_type.forEach(orderTypeItem => {
                this.dataMasterConfig.orderType[orderTypeItem.toUpperCase()] = orderTypeItem;  
            });
    
            // Log the initialized configuration
            logger.logInfo('Data masters config initialized: ', this.dataMasterConfig);
            console.log('Data masters config initialized: ', this.dataMasterConfig);
        
        } catch (error) {
            // Log the error if any
            console.error('Error initializing Data masters config:', error);
            logger.logError('Error initializing Data masters config:', error);
            throw error;
        }
    }
    
    static async getConfigByType(type, name) {
        if (['mode', 'orderType'].includes(type)) {
            const config = this.dataMasterConfig[type];
            return config[name] || null;  
        }
        throw new Error('Invalid type, expected "mode" or "orderType"');
    }
}

// Initialize the config (only once)
(async () => {
    try {
        await StatusService.initializeDataMasterConfig();
        console.log('Data masters config initialized successfully.');
    } catch (error) {
        console.error('Failed to initialize Data masters config:', error);
        process.exit(1); // Exit the application if initialization fails
    }
})();
module.exports = StatusService;       