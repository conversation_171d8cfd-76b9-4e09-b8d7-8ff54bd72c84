const request = require('supertest');
const chai = require('chai');
const expect = chai.expect;

// Replace with your app's base URL if necessary
const app = require('../app'); // Assuming the Express app is exported from 'app.js'

describe('POST /api/orders/:orderId/trackings', () => {
    it('should add multiple tracking events successfully', async () => {
        // Sample input for trackingDetails
        const trackingDetails = [
            {
                "eventType": "Arrived",
                "warehouseId": 5,
                "hubName": "Pick Up Address - Warehouse A",
                "timestamp": "2025-01-22 10:00:00",
                "statusMessage": "Order has arrived at the pickup warehouse.",
                "location": "New Delhi",
                "mode": "Car",
                "hubsTrackingType": "StartingHub"
            },
            {
                "eventType": "Departed",
                "warehouseId": 5,
                "hubName": "Pick Up Address - Warehouse A",
                "timestamp": "2025-01-22 12:00:00",
                "statusMessage": "Order has left the pickup warehouse and is on its way to next hub.",
                "location": "New Delhi",
                "mode": "Train",
                "hubsTrackingType": "IntermediateHub"
            },
            {
                "eventType": "Arrived",
                "warehouseId": 6,
                "hubName": "Return Address - Warehouse B",
                "timestamp": "2025-01-23 09:00:00",
                "statusMessage": "Order has arrived at the return warehouse.",
                "location": "Gurgaon",
                "mode": "Air",
                "hubsTrackingType": "IntermediateHub"
            },
            {
                "eventType": "Departed",
                "warehouseId": 6,
                "hubName": "Return Address - Warehouse B",
                "timestamp": "2025-01-23 14:00:00",
                "statusMessage": "Order has left the return warehouse and is out for reattempted delivery.",
                "location": "Gurgaon",
                "mode": "Car",
                "hubsTrackingType": "IntermediateHub"
            },
            {
                "eventType": "Delivery Attempted",
                "warehouseId": 7,
                "hubName": "Delivery Address - Final Destination",
                "timestamp": "2025-01-24 10:00:00",
                "statusMessage": "Delivery attempt failed, customer unavailable.",
                "location": "Noida",
                "mode": "Car",
                "deliveryAgent": {
                    "agentName": "Ravi Kumar",
                    "agentContact": "+919876543210",
                    "agentEmail": "<EMAIL>",
                    "agentId": "DEL12345"
                },
                "hubsTrackingType": "FinalHub"
            },
            {
                "eventType": "Returned",
                "warehouseId": 7,
                "hubName": "Delivery Address - Final Destination",
                "timestamp": "2025-01-24 10:30:00",
                "statusMessage": "Order has been returned to warehouse due to delivery failure.",
                "location": "Noida",
                "mode": "Car",
                "returnReason": "Customer unavailable at delivery address",
                "hubsTrackingType": "FinalHub"
            },
            {
                "eventType": "Departed",
                "warehouseId": 6,
                "hubName": "Return Address - Warehouse B",
                "timestamp": "2025-01-25 12:00:00",
                "statusMessage": "Order has left the return warehouse for second delivery attempt.",
                "location": "Gurgaon",
                "mode": "Car",
                "hubsTrackingType": "IntermediateHub"
            },
            {
                "eventType": "Delivered",
                "warehouseId": 7,
                "hubName": "Delivery Address - Final Destination",
                "timestamp": "2025-01-25 15:00:00",
                "statusMessage": "Order successfully delivered to the customer by a different delivery agent.",
                "location": "Noida",
                "mode": "Motorbike",
                "deliveryAgent": {
                    "agentName": "Amit Kumar",
                    "agentContact": "+919876543211",
                    "agentEmail": "<EMAIL>",
                    "agentId": "DEL67890"
                },
                "hubsTrackingType": "FinalHub"
            }
        ];

        const orderId = 161;  // Use a valid order ID for testing
        
        const response = await request(app)
            .post(`/api/orders/${orderId}/trackings`)  // Adjust the endpoint as needed
            .send({ trackingDetails })  // Send the tracking details in the request body
            .set('Accept', 'application/json');

        // Assert the response
        expect(response.status).to.equal(200);
        expect(response.body).to.have.property('status', 'success');
        expect(response.body.data).to.have.property('orderId', orderId);
        expect(response.body.data).to.have.property('trackingDetails').with.lengthOf(trackingDetails.length);
    });
});
