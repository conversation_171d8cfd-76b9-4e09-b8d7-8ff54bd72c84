const dbpool = require('../../startup/db');
const helpers = require('../../misc/helpers.js');
const cache = require('../../misc/cache.js');
const miscConstants = require("../../misc/constants.js");
const hcldb = process.env.INITIAL_CATALOG || "hcl";

class DashbiardService {
    async getDashboardData (userData) {
        try {

            let clientId = userData.clientId;
            let organizationId = userData.organizationId;
            if(clientId) {
                organizationId = null;
            } else {
                clientId = null;
            }

            const cacheKeyDashboard = `${miscConstants.CACHE.DASHBOARD.KEY}${organizationId}:${clientId}`;
            const cacheKeyProviderDistribution = `${miscConstants.CACHE.DASHBOARD_PROVIDER_DISTRIBUTION.KEY}${organizationId}:${clientId}`;
            const cacheKeyStatusDistribution = `${miscConstants.CACHE.DASHBOARD_STATUS_DISTRIBUTION.KEY}${organizationId}:${clientId}`;
            let dashboardData = cache.get(cacheKeyDashboard);
            let providerData = cache.get(cacheKeyProviderDistribution);
            let statusData = cache.get(cacheKeyStatusDistribution);

            if (!dashboardData || !providerData || !statusData) {

                const sql = `${hcldb}.GetDashboardData`;
                const [dashboardResult, statusResult] = await dbpool.executeProcedure(sql,[organizationId, clientId]);
               // console.log(dashboardResult);
                dashboardData = dashboardResult[0];
                statusData = statusResult;

                const providerSql = `${hcldb}.GetProviderDistribution`;
                const [providerResult] = await dbpool.executeProcedure(providerSql,[organizationId, clientId]);
                providerData = providerResult;
                console.log(providerData);

                cache.set(cacheKeyDashboard, dashboardData, miscConstants.CACHE.DASHBOARD.TTL);
                cache.set(cacheKeyProviderDistribution, providerData, miscConstants.CACHE.DASHBOARD_PROVIDER_DISTRIBUTION.TTL);
                cache.set(cacheKeyStatusDistribution, statusData, miscConstants.CACHE.DASHBOARD_STATUS_DISTRIBUTION.TTL);
            }

            return {
                totalOrders: dashboardData.totalOrders,
                delivered: dashboardData.delivered,
                in_transit: dashboardData.in_transit,
                rto: dashboardData.rto,
                prepaid: dashboardData.prepaid,
                cod: dashboardData.cod,
                providerDistribution: providerData,
                statusDistribution: statusData
            };
        } catch (error) {
            throw error;
        }
    }    
}

module.exports = new DashbiardService();