const Joi = require('joi');

const createWarehouseAddressSchema = Joi.object({
  addressDetailType: Joi.string().allow(''),
  warehouseId: Joi.number().optional(),
  consigneeFirstName: Joi.string().required(),
  consigneeLastName: Joi.string().required(),
  businessName: Joi.string().required(),
  contact: Joi.object({
    consigneeContact: Joi.string()
        .pattern(/^[0-9]{10}$/) // Must be exactly 10 digits
        .required()
        .messages({
            'string.pattern.base': 'Phone Number must be a 10-digit number',
            'any.required': 'Phone Number is required',
        }),
    
    alternateContact: Joi.string()
        .pattern(/^[0-9]{10}$/) // Must be exactly 10 digits
        .required()
        .messages({
            'string.pattern.base': 'Alternate Phone Number must be a 10-digit number',
            'any.required': 'Alternate Phone Number is required',
        }),
    email: Joi.string().email().required()
  }).required(),
  address: Joi.object({
    addressType: Joi.string().required(),
    addressLine1: Joi.string().required(),
    addressLine2: Joi.string().allow(''),
    city: Joi.string().required(),
    state: Joi.string().required(),
    zip: Joi.string().required(),
    country: Joi.string().required()
  }).required(),
  created_by_name: Joi.string().optional(),  // This will be set by the backend from the authenticated user
  created_by_id: Joi.string().optional()     // This will be set by the backend from the authenticated user
});


const updateWarehouseAddressSchema = Joi.object({
  addressDetailType: Joi.string().allow(''),
  warehouseId: Joi.number().required(),
  consigneeFirstName: Joi.string().required(),
  consigneeLastName: Joi.string().required(),
  businessName: Joi.string().required(),
  contact: Joi.object({
    consigneeContact: Joi.string()
        .pattern(/^[0-9]{10}$/) // Must be exactly 10 digits
        .required()
        .messages({
          'string.pattern.base': 'Phone Number must be a 10-digit number',
          'any.required': 'Phone Number is required',
        }),
    
    alternateContact: Joi.string()
        .pattern(/^[0-9]{10}$/) // Must be exactly 10 digits
        .required()
        .messages({
           'string.pattern.base': 'Alternate Phone Number must be a 10-digit number',
            'any.required': 'Alternate Phone Number is required',
        }),
    email: Joi.string().email().required()
  }).required(),
  address: Joi.object({
    addressType: Joi.string().required(),
    addressLine1: Joi.string().required(),
    addressLine2: Joi.string().allow(''),
    city: Joi.string().required(),
    state: Joi.string().required(),
    zip: Joi.string().required(),
    country: Joi.string().required()
  }).required()
});

module.exports = { createWarehouseAddressSchema, updateWarehouseAddressSchema };
