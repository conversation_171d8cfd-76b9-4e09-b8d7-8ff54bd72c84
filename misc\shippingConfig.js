const env = require("../startup/env");

let shipxboxConfig = {
    shippingProviderId: 1, // Default value
    shippingProviderName: 'SHIPXBOX',
}    

let bluedartConfig = {
    shippingProviderId: 4, // Default value
    shippingProviderName: 'BLUEDART',
    customerCode: process.env.BLUEDART_CUSTOMER_CODE,
    loginId: process.env.BLUEDART_LOGIN_ID,
    licKey: process.env.BLUEDART_LIC_KEY,
   // trackLicKey: process.env.BLUEDART_TRACKING_LIC_KEY,
    clientID: process.env.BLUEDART_CLIENT_ID,
    clientSecret: process.env.BLUEDART_CLIENT_SECRET,
    originArea: process.env.BLUEDART_ORIGIN_AREA,
    sender: process.env.BLUEDART_SENDER,
    apiType: process.env.BLUEDART_API_TYPE,
    verNo: process.env.BLUEDART_API_VERSION,
    productTypes: {    
        B2C:{
            SURFACE: {              // B2C Services
                code: 'E',
                name: 'eTailCODGround',
                productType: 1,
                subProductCode: 'P',
                packType:'',
                category: 'B2C'
            },
            AIR: {
                code: 'A',
                name: 'eTailCODAir',
                productType: 1,
                subProductCode: 'P',
                packType:'',
                category: 'B2C'
            },
            DP: {
                code: 'D',
                name: 'DartPlus',
                productType: 1,
                subProductCode: 'T',
                packType:'L',  
                category: 'B2C'
            },
        },   
        B2B: {                      // B2B Services
            SURFACE: {    
                code: 'E',
                name: 'Ground- by Road - Surface line',
                productType: 1,
                subProductCode: '',
                packType:'',
                category: 'B2B'
            },
            AIR: {
                code: 'A',
                name: 'Apex- Air Cargo',
                productType: 2,
                subProductCode: '',
                packType:'',
                category: 'B2B'
            },
            DP: {
                code: 'D',
                name: 'Domestic Priority',
                subProductCode: 'T',
                productType: 2,
                packType:'',
                category: 'B2B'
            },
        },    
        RVP: {
            SURFACE: { // Reverse Pickups (Both B2C/B2B)
                code: 'A',
                name: 'Surface RVP',
                productType: 1,
                subProductCode: 'R',
                packType:'',
                category: 'RVP'
            },
            AIR: {
                code: 'P',
                name: 'Air RVP',
                productType: 1,
                subProductCode: 'R',
                packType:'',
                category: 'RVP'
            }
        }    
    },    
    devUrl: 'https://apigateway-sandbox.bluedart.com/in',
    prodUrl : 'https://apigateway.bluedart.com/in',
    production: {
        urls: {
            authentication: '/transportation/token/v1/login',
            generateWayBill: '/transportation/waybill/v1/GenerateWayBill',
            cancelWaybill: '/transportation/waybill/v1/CancelWaybill',
            tracking: '/transportation/tracking/v1/shipment',
            downloadPinCodeMaster: '/transportation/masterdownload/v1/DownloadPinCodeMaster'
        }
    },
    development: {
        urls: {
            authentication: '/transportation/token/v1/login',
            generateWayBill: '/transportation/waybill/v1/GenerateWayBill',
            cancelWaybill: '/transportation/waybill/v1/CancelWaybill',
            tracking: '/transportation/tracking/v1/shipment',
            downloadPinCodeMaster: '/transportation/masterdownload/v1/DownloadPinCodeMaster'
        }
    },
    getUrls: function(environment) {
        if (!['production', 'development'].includes(environment)) {
            throw new Error('Invalid environment specified. Use "production" or "development"');
        }
        return this[environment].urls;
    },
  
};

let dtdcConfig = {
    shippingProviderId: 3, // Default value
    shippingProviderName: 'DTDC',
    customerCode: process.env.DTDC_CUSTOMER_CODE,
    apiKey: process.env.DTDC_API_KEY,
    accessToken: process.env.DTDC_ACCESS_TOKEN,
    cookie: process.env.DTDC_COOKIE,
    authCookie: process.env.DTDC_AUTH_COOKIE,
    trackUserName: process.env.DTDC_TRACKING_USERNAME,
    trackPassword: process.env.DTDC_TRACKING_PASSWORD,   
    serviceType: {
        // Non-Document Services (B2C)
        B2C: {
            priority: 'B2C PRIORITY',          // Air
            express: 'B2C SMART EXPRESS',       // Surface
            premium: 'B2C PREMIUM',             // On Time Faster Delivery
            groundEconomy: 'B2C GROUND ECONOMY',// Heavy Movement
            groundExpress: 'GROUND EXPRESS',    // Surface
            gec: 'GEC',                        // Heavy Movement
            stdExpA: 'STD EXP-A'                // Lite Movement
        },
        // Document Services
        DOCUMENT: {
            priority: 'PRIORITY',               // Air
            premium: 'PREMIUM',                 // On Time Faster Delivery
            stdExpA: 'STD EXP-A'                // Lite Movement
        },
        // B2B Services
        B2B: {
            priority: 'PRIORITY',               // Air
            premium: 'PREMIUM',                // On Time Faster Delivery
            stdExpA: 'STD EXP-A'                // Lite Movement
        }
    },

    // Movement Types Mapping
    movementType: {
        AIR: 'Air',
        SURFACE: 'Surface',
        ON_TIME: 'On Time Faster Delivery',
        HEAVY: 'Heavy Movement',
        LITE: 'Lite Movement'
    },

    devUrl: 'https://demodashboardapi.shipsy.in/api',
    prodUrl : 'https://dtdcapi.shipsy.io/api',

    production: {
        urls: {
            authentication: 'https://blktracksvc.dtdc.com/dtdc-api/api/dtdc/authenticate',
            generateWayBill: '/customer/integration/consignment/softdata',
            cancelWaybill: '/customer/integration/consignment/cancel',
            trackingXml: 'https://blktracksvc.dtdc.com/dtdc-api/rest/XMLCnTrk/getDetails',
            trackingJson: 'https://blktracksvc.dtdc.com/dtdc-api/rest/JSONCnTrk/getTrackDetails',
            downloadPinCodeMaster: 'http://smarttrack.ctbsplus.dtdc.com/ratecalapi/PincodeApiCall'
        }
    },
    development: {
        urls: {
            authentication: 'https://dtdcstagingapi.dtdc.com/dtdc-tracking-api/dtdc-api/api/dtdc/authenticate',
            generateWayBill: '/customer/integration/consignment/softdata',
            cancelWaybill: '/customer/integration/consignment/cancel',
            trackingXml: 'https://dtdcstagingapi.dtdc.com/dtdc-tracking-api/dtdc-api/rest/XMLCnTrk/getDetails',
            trackingJson: 'https://dtdcstagingapi.dtdc.com/dtdc-tracking-api/dtdc-api/rest/JSONCnTrk/getTrackDetails',
            downloadPinCodeMaster: 'http://smarttrack.ctbsplus.dtdc.com/ratecalapi/PincodeApiCall'
        }
    },
    getUrls: function(environment) {
        if (!['production', 'development'].includes(environment)) {
            throw new Error('Invalid environment specified. Use "production" or "development"');
        }
        return this[environment].urls;
    },
};

module.exports = {
    getBluedartConfig: () => bluedartConfig,
    setBluedartConfig: (newConfig) => {
        bluedartConfig = { ...bluedartConfig, ...newConfig };
    },

    getDtdcConfig: () => dtdcConfig,
    setDtdcConfig: (newConfig) => {
        dtdcConfig = { ...dtdcConfig, ...newConfig };
    },

    getShipxboxConfig: () => shipxboxConfig,
    setShipxboxConfig: (newConfig) => {
        shipxboxConfig = { ...shipxboxConfig, ...newConfig };
    }
};
