const { validateClientRegistration, createBillingInfoValidation } = require('./validators/clientValidator');

const { registerClient, 
        getAllClients, 
        editClient, 
        getClientById, 
        deleteClientById, 
        upsertBillingInfo, 
        getBillingInfo} = require('../../../services/hcl/clientService');

const helpers = require('../../../misc/helpers');
const miscConstants = require("../../../misc/constants");
const { throwError } = require('rxjs');

exports.registerClient = async (req, res) => {
  try {
      const { error, value } = validateClientRegistration(req.body);
      const userData = req.user;
      
      if (error) {
          return helpers.sendError(
              res, 
              miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
              miscConstants.ERRORCODES.VALIDATION_ERROR, 
              error.details[0].message, 
              req.body
          );
      }

      const result = await registerClient(value, userData.id, userData.organizationId);

      // Handle duplicate registration cases
      if (result.success === false) {
          return helpers.sendError(
              res,
              miscConstants.HTTPSTATUSCODES.CONFLICT, 
              miscConstants.ERRORCODES.DUPLICATE_RESOURCE,
              result.message, 
              req.body
          );
      }

      // Handle successful registration
      return helpers.sendSuccess(
          res, 
          miscConstants.HTTPSTATUSCODES.OK, 
          result.message || 'Client registered successfully', 
          {
              clientId: result.clientId,
              ...(result.user ? { user: result.user } : {})
          },
          req.body
      );

  } catch (error) {
      console.error('Client registration error:', error);
      return helpers.sendError(
          res,
          miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
          miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
          error.message || 'Failed to register client',
          req.body
      );
  }
};

exports.getAllClients = async (req, res) => {
    try {
      // Extract pagination parameters
      const { page = 1, pageSize = 0, useCache = 1} = req.query;
      const userData = req.user;

      let organizationId = req.userRoles.isRoleSuperAdmin ? null : userData.organizationId;

      // Parse pagination values to integers
      const pageNum = parseInt(page, 10) || 1;
      const pageSizeNum = pageSize || 0 ;
  
      // Call service to get all clients
      const result = await getAllClients(pageNum, pageSizeNum, organizationId, useCache);
  
      // Send the result to the client
      return helpers.sendPaginationResponse(res, miscConstants.HTTPSTATUSCODES.OK, 
        'Clients fetched successfully', result.clients, result.totalRecords, pageNum, pageSizeNum);

    } catch (error) {
      return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
        miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.query);
    }
  };

  exports.editClient = async (req, res) => {

    try {
      const { id } = req.params; // Client ID from URL
      const {
        firstName,
        lastName,
        email,
        mobile,
        company,
        monthlyOrders,
        businessType,
        isActive,
        isWallet,
        isB2BAccess,
        isTat = 0
      } = req.body;
  
      const userData = req.user;

      const client = await getClientById(id);

      if ( helpers.isEmpty(client) ) {
          return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.NOT_FOUND, miscConstants.ERRORCODES.NOT_FOUND, 
            'Client does not found', req.body);
      }

      // Call service to edit client
      const result = await editClient(
        id,
        firstName,
        lastName,
        email,
        mobile,
        company,
        monthlyOrders,
        businessType,
        isActive,
        isWallet,
        isB2BAccess,
        isTat,
        userData.id,
        userData.organizationId,
      );
      
      return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, 'Client updated successfully', result, req.body);
    } catch (error) {
      return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
        miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
    }
  };

exports.getClientById = async (req, res) => {
  try {
    const { id } = req.params

    if (!id || isNaN(id)) {
      return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
        miscConstants.ERRORCODES.VALIDATION_ERROR, 'Invalid Client ID', req.params);
    }

    const client = await getClientById(id);
    if ( helpers.isEmpty(client) ) {
      return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.NOT_FOUND, 
        miscConstants.ERRORCODES.NOT_FOUND, 'Client does not found', req.params);
    }

    return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, 'Client details retrieved successfully', client, req.params);
  } catch (error) {
  //  console.error('Error in getClientById controller:', error.message);
    return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR,  error.message, req.params);
  }
};  


// function to handle client deletion
exports.deleteClientById = async (req, res) => {

  try {
      const { clientId } = req.params;
      const userData = req.user;

      if (!clientId || isNaN(clientId)) {
       return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                      miscConstants.ERRORCODES.VALIDATION_ERROR, 
                      'Invalid client ID', req.params);
      }
      const client = await getClientById(clientId);
      if ( helpers.isEmpty(client) ) {
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.NOT_FOUND,
                          miscConstants.ERRORCODES.NOT_FOUND, 'Client does not found', req.params);
      }
      // Call the service to delete the client
      const result = await deleteClientById(clientId, userData);
      return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, result.message, {clientId: result.clientId}, req.params);
  } catch (error) {
      return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
        miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.params);
  }
};


// Add or Update Billing Info
exports.upsertBillingInfo = async (req, res) => {
  try {       
      const { error } = createBillingInfoValidation(req.body);          
      if (error) {
          helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
            miscConstants.ERRORCODES.VALIDATION_ERROR, error.details[0].message, req.body);
      }   
      const{clientId} = req.body;
      const client = await getClientById(clientId);
      if (!client || client.length === 0 ) {
        helpers.sendError(res, miscConstants.HTTPSTATUSCODES.NOT_FOUND, miscConstants.ERRORCODES.NOT_FOUND, 'Client does not found', req.body);
      } 
      const result = await upsertBillingInfo(req.body, req.user);
      return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, 
        miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, {billingId: result.billingInfoId}, req.body);

  } catch (error) {
    return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
      miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
  }
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
exports.getBillingInfo = async (req, res) => {
  const { clientId } = req.params;

  if (!clientId || isNaN(clientId)) {
    helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
      miscConstants.ERRORCODES.VALIDATION_ERROR, 'Invalid client ID.', req.params);
  }
  try {
      const billingInfo = await getBillingInfo(clientId);
      if (helpers.isEmpty(billingInfo)) {
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.NOT_FOUND, miscConstants.ERRORCODES.NOT_FOUND, 
          "No billing info found for the given client ID.", req.params);
      }
     return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, 
                  miscConstants.SUCESSSMESSAGES.GET, billingInfo, req.params); 

  } catch (error) {
    return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
                  miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, {clientId: clientId});
  }
};