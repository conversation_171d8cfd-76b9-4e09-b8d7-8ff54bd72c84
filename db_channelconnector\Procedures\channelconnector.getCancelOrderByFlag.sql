DROP PROCEDURE IF EXISTS channelconnector.getCancelOrderByFlag;

DELIMITER $$
CREATE PROCEDURE channelconnector.getCancelOrderByFlag(
	IN `in_casename` VARCHAR(60)
)
BEGIN
	/*
    call channelconnector.getCancelOrderByFlag('send_Orders_Fby');
    */
    
	 /*SELECT * FROM order_details;# WHERE fby_error_flag=1 AND count=1 AND lower(cron_name) = lower(casename);*/
     SELECT * FROM order_details as OD 
     WHERE OD.fby_error_flag = 1 
     AND OD.count = 1
     AND lower(OD.cron_name) = lower(in_casename);
END$$
DELIMITER ;