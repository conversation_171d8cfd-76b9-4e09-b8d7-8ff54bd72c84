-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tracking_carrier_master_mapping`
--

DROP TABLE IF EXISTS `tracking_carrier_master_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tracking_carrier_master_mapping` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` int DEFAULT NULL,
  `mirakl_carrier_code` varchar(256) DEFAULT NULL,
  `fby_mapped_tracking_carrier_code` varchar(256) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tracking_carrier_master_mapping`
--

LOCK TABLES `tracking_carrier_master_mapping` WRITE;
/*!40000 ALTER TABLE `tracking_carrier_master_mapping` DISABLE KEYS */;
INSERT INTO `tracking_carrier_master_mapping` VALUES (1,39,'UPS','UPS','2023-06-08 05:14:51','2023-06-08 05:14:51'),(2,39,'DHL','DHL','2023-06-08 05:14:52','2023-06-08 05:14:52'),(3,39,'TNT IT','TNT','2023-06-08 05:14:52','2023-06-08 05:14:52'),(4,39,'GLS','GLS','2023-06-08 05:14:53','2023-06-08 05:14:53'),(5,39,'FEDEX','FDX','2023-06-08 05:14:53','2023-06-08 05:14:53'),(6,39,'SDA','SDA','2023-06-08 05:14:53','2023-06-08 05:14:53'),(7,39,'BRT','BRT','2023-06-08 05:14:54','2023-06-08 05:14:54'),(8,39,'RHENUS','','2023-06-08 05:14:54','2023-06-08 05:14:54'),(9,39,'CMO','CLS','2023-06-08 05:14:55','2023-06-08 05:14:55'),(10,39,'CRO','CHR','2023-06-08 05:14:55','2023-06-08 05:14:55'),(11,39,'DPD','DPD','2023-06-08 05:14:56','2023-06-08 05:14:56'),(12,50,'swisspost','SWP','2023-06-08 05:14:56','2023-06-08 05:14:56'),(13,50,'dpd','DPD','2023-06-08 05:14:56','2023-06-08 05:14:56'),(14,50,'dhl','DHL','2023-06-08 05:14:57','2023-06-08 05:14:57'),(15,50,'ups','UPS','2023-06-08 05:14:57','2023-06-08 05:14:57'),(16,50,'tnt','TNT','2023-06-08 05:14:58','2023-06-08 05:14:58'),(17,50,'fiege','','2023-06-08 05:14:58','2023-06-08 05:14:58'),(18,50,'LaPost_Colissimo','LPS','2023-06-08 05:14:59','2023-06-08 05:14:59'),(19,50,'asendia','ASN','2023-06-08 05:14:59','2023-06-08 05:14:59'),(20,50,'chronopost','CHR','2023-06-08 05:14:59','2023-06-08 05:14:59'),(21,50,'Planzer','','2023-06-08 05:15:00','2023-06-08 05:15:00'),(22,50,'quickpac','','2023-06-08 05:15:00','2023-06-08 05:15:00'),(23,50,'fedex','FDX','2023-06-08 05:15:01','2023-06-08 05:15:01'),(24,50,'postnord',NULL,'2023-06-08 05:15:01','2023-06-08 05:15:01');
/*!40000 ALTER TABLE `tracking_carrier_master_mapping` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:53:12
