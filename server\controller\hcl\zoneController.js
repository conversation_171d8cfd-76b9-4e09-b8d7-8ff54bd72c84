const helpers = require('../../../misc/helpers');
const logger = require('../../../misc/logger.js');
const { validateDeliveryDays } = require('./validators/zoneValidator.js');
const miscConstants = require("../../../misc/constants.js");
const ZoneService = require('../../../services/hcl/zoneService.js');

// Add or Update Zone
exports.upsertZone = async (req, res) => {
    const { id, shippingProviderId, zoneName, zoneCode, zoneTat = "2-3 Days", region, isActive } = req.body;

    try {
        const result = await ZoneService.upsertZone(
            id, shippingProviderId, zoneName, zoneCode, 
            zoneTat, region, isActive, req.user.id, req.user.id
        );
       return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, result, req.body);
    } catch (error) {
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
    }
};

// Get All Zones
exports.getZones = async (req, res) => {
    try {
        const { shippingProviderId } = req.query;
        const zones = await ZoneService.getZones(shippingProviderId);
        helpers.sendSuccess(res, 200, "Zone fetched successfully!", zones, req.query);
    } catch (error) {
        helpers.sendError(res, 500, 'getZones', error.message, req.query);
    }
};

// Get Zone By ID
exports.getZoneById = async (req, res) => {
    const { id } = req.params;
    try {
        if (!id || isNaN(id)) {
            helpers.sendError(res, 400, 'getZoneById', 'Invalid zone iD', req.params)
        }
        const result = await ZoneService.getZoneById(id);
        helpers.sendSuccess(res, 200, "Zoen fetched successfully!", result, req.params);
    } catch (error) {
        if (error.sqlState === '45000') {
            helpers.sendError(res, 404, 'getZoneById', error.message, req.params);
        } else {
            helpers.sendError(res, 500, 'getZoneById', error.message, req.params);
        }
    }
};

// // Delete Zone
// exports.deleteZone = async (req, res) => {
//     const { id } = req.params;
//     const { updatedBy } = req.user.id;
//     try {
//         const result = await zonesService.deleteZone(id, updatedBy);
//         helpers.sendSuccess(res, 200, result.message, result, req.params);
//     } catch (error) {
//         helpers.sendError(res, 500, 'deleteZone', error.message, req.params);
//     }
// };


exports.upsertDeliveryDays = async (req, res) => {
    try {
      const { error, value } = validateDeliveryDays(req.body);
      
      if (error) {
        return helpers.sendError(
            res,
            miscConstants.HTTPSTATUSCODES.BAD_REQUEST,
            miscConstants.ERRORCODES.VALIDATION_ERROR,   
            error.details.map(detail => detail.message),
            req.body
        );
      }
      
      const result = await ZoneService.upsertDeliveryDays(value);
      return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.CREATED, 
            miscConstants.SUCESSSMESSAGES.INSERT_UPDATE, 
            result, 
            req.body
        );
   
    } catch (error) {
      logger.logError(`Error in upsertDeliveryDays: ${error.message}`);
      return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );
    }
};

/**
 * 
 * @param {*} req 
 * @param {*} res 
 */
exports.getDeliveryDays = async (req, res) => {
    try {
        const filters = {
            sourceZoneId: req.query.sourceZoneId ? parseInt(req.query.sourceZoneId) : null,
            destinationZoneId: req.query.destinationZoneId ? parseInt(req.query.destinationZoneId) : null,
            orderType: req.query.orderType ? req.query.orderType : "B2C",
            providerId: req.query.providerId ? parseInt(req.query.providerId) : null
        };
      
        // Default to using cache, unless explicitly disabled
        const useCache = req.query.useCache !== 'false';
      
        const data = await ZoneService.getDeliveryDays(filters, useCache);
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            data, 
            req.body
        );
      
    } catch (error) {
        logger.logError(`Error in getDeliveryDays: ${error.message}`);
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );
    }
  };
  
  /**
   * 
   * @param {*} req 
   * @param {*} res 
   * @returns 
   */
  exports.getOptimalDelivery = async (req, res) => {
    try {
        const { sourceZoneId, destinationZoneId, orderType } = req.query;
        
        // Validate required parameters
        if (!sourceZoneId || !destinationZoneId || !orderType) {
            return res.status(400).json({
                success: false,
                message: 'Source zone, destination zone, and order type are required'
            });
        }
        
        const data = await ZoneService.getOptimalDelivery(
            parseInt(sourceZoneId),
            parseInt(destinationZoneId),
            orderType
        );

        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            miscConstants.SUCESSSMESSAGES.GET, 
            data, 
            req.body
        );
      
    } catch (error) {
        logger.logError(`Error in getOptimalDelivery: ${error.message}`);
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );
    }
  };
  
  exports.clearCache = async (req, res) => {
    try {
        await ZoneService.clearCache();
        return helpers.sendSuccess(
            res, 
            miscConstants.HTTPSTATUSCODES.OK, 
            'Cache cleared successfully', 
            '', 
            req.body
        );
    } catch (error) {
        logger.logError(`Error in clearCache: ${error.message}`);
        return helpers.sendError(
            res, 
            miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );
    }
  };
