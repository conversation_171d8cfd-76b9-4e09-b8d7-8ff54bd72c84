const helpers = require("../misc/helpers");
const jwt = require("jsonwebtoken");
const dbpool = require('../startup/db');
const env = require('../startup/env');
const CONSTANTS = require("../misc/constants");
const CacheService = require('../misc/cache.js');
const ccDB = process.env.DB_DATABASE || "channelconnector";

exports.checkMultiAuthorization = async (req, res, next) => {

  if (!req.headers.authorization) {

    return helpers.sendError(
      res,
      CONSTANTS.HTTPSTATUSCODES.BAD_REQUEST,
      "Invalid credentials",
      "Authorization token is not provided. Please try again.",
      req.body
    );

  }

  const [type, token] = req.headers.authorization.split(" ");

  if (token != env.JWT_KEY) {

    return helpers.sendError(
      res,
      CONSTANTS.HTTPSTATUSCODES.UNAUTHORIZED,
      "Authorization failed",
      "Authorization token is invalid. Please provide valid token.",
      {
        authorization: req.headers.authorization
      }
    );

  }
  next();

};

exports.authenticateToken = (req, res, next) => {
  const token = req.headers['authorization'];
  if (!token) return res.status(401).json({ message: 'Access Denied. No token provided.' });

  try {
    const decoded = jwt.verify(token.split(' ')[1], process.env.JWT_KEY);
    req.user = decoded;  // Attach user info to request object
    next();
  } catch (error) {
    res.status(400).json({ message: 'Invalid token' });
  }
};

// Middleware to authorize based on role
module.exports.authorizeRoles = async (...allowedRoles) => {
  return (req, res, next) => {
    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Access Denied. You do not have permission to perform this action.' });
    }
    next();
  };
}

// Middleware to check for specific permissions for normal users
// module.exports.authorizePermissions = async (...requiredPermissions) => {

//   return async (req, res, next) => {
//     const user = await User.findById(req.user.id); // Get the full user from DB to check permissions
//     if (user.role !== 'admin' && !requiredPermissions.every(perm => user.permissions.includes(perm))) {
//       return res.status(403).json({ message: 'Access Denied. Insufficient permissions.' });
//     }
//     next();
//   };
// }


/**
 * Middleware to check for specific permissions for normal users
 * @param {*} requiredPermission 
 * @returns 
 */
module.exports.checkPermission = () => {
  return async (req, res, next) => {
      try {
          const user = req.user;

          // Determine user roles
          const isRoleSuperAdmin = user.roles.some(role => role.toLowerCase().trim() === 'super admin');
          const isRoleOrganization = user.roles.some(role => 
              ['organization admin', 'organization user'].includes(role.toLowerCase().trim())
          );
          const isRoleUser = !isRoleSuperAdmin && !isRoleOrganization;

          // Attach roles to the request object
          req.userRoles = {
              isRoleSuperAdmin,
              isRoleOrganization,
              isRoleUser
          };

          // If the user is a Super Admin, bypass permission check
          if (isRoleSuperAdmin || isRoleOrganization || isRoleUser) {
              return next();
          }

          // Check permissions for non-Super Admin users
          const cacheKey = CacheService.generateKey(Constants.CACHE.PERMISSION.KEY, user.id);
          let permissions = await CacheService.get(cacheKey);

          if (!permissions) {
              const sql = `${ccDB}.GetUserPermissions`;
              const [result] = await dbpool.executeProcedure(sql, [user.id], ccDB);
              permissions = result[0];
              await CacheService.set(cacheKey, permissions, Constants.CACHE.PERMISSION.TTL);
          }

          const hasPermission = permissions.some(
              p => p.name === requiredPermission
          );

          if (!hasPermission) {
              return helpers.sendError(res, Constants.HTTPSTATUSCODES.FORBIDDEN, 
                  Constants.ERRORCODES.FORBIDDEN, 'Permission denied', req.params);
          }

          next();
      } catch (error) {
          return helpers.sendError(res, Constants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
              Constants.ERRORCODES.INTERNAL_SERVER_ERROR, 'Error checking permissions', req.params);
      }
  };
};

