const fs = require("fs");
const csv = require("fast-csv");
const mysql = require("mysql2");
require("dotenv/config");

const connection = mysql.createConnection({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_DATABASE,
});

// Connect to the database
connection.connect((err) => {
    if (err) {
      console.error("Database connection failed:", err.message);
      process.exit(1);
    }
    console.log("Connected to MySQL database.");
  });

// File path of the CSV to read
const filePath = "./uploads/pincode.csv";

// Function to read CSV and upload data
const uploadCSV = (file) => {

    let rows = [];
  // Read and parse CSV
    fs.createReadStream(file)
        .pipe(csv.parse({ headers: true })) // Parse CSV with headers
        .on("data", (row) => {
        rows.push([row.Pincode, row.City, row.State]); // Map row fields to match database columns
        })
        .on("end", () => {
        console.log(`Read ${rows.length} rows from CSV file.`);

        // Bulk insert into MySQL
        // conn = db.pool;
        // connection = 
        const query = "INSERT INTO `hcl`.`pincode_master` (pincode, city, state) VALUES ?";
        connection.query(query, [rows], (err, result) => {
            if (err) {
            console.error("Error inserting data:", err.message);
            } else {
            console.log(`Successfully imported ${result.affectedRows} rows into the database.`);
            }

            // Close the database connection
            if (connection) connection.end();
        });
        })
        .on("error", (err) => {
        console.error("Error reading CSV file:", err.message);
        });
};

// Start the upload process
uploadCSV(filePath);
