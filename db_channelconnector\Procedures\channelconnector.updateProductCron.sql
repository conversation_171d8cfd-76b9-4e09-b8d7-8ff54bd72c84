DROP PROCEDURE IF EXISTS channelconnector.updateProductCron;

DELIMITER $$
CREATE PROCEDURE channelconnector.updateProductCron (
	`in_sku` VARCHAR(128),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
	`in_time` DATETIME
)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
    
	UPDATE products 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = in_time
	WHERE
		products.sku = in_sku;
            
	SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;