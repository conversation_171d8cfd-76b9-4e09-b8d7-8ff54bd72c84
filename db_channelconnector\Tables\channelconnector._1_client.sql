CREATE TABLE channelconnector._1_client (
    `id` INT(11) NOT NULL AUTO_INCREMENT,
    `clientId` VARCHAR(512) NOT NULL,
    `name` VARCHAR(1024) NOT NULL,
    `ownerCode` VARCHAR(1024) NOT NULL,
    `isActive` TINYINT(4),
    `createdOn` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    `modifiedOn` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP (),
    PRIMARY KEY (`id`)
)  ENGINE=INNODB  DEFAULT CHARSET=UTF8MB4;

/*

Truncate channelconnector._1_client;

*/