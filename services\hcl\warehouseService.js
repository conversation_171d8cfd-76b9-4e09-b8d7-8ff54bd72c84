// services/warehouseService.js
const pool = require('../../startup/db');
const hcldb = process.env.INITIAL_CATALOG || "hcl";
const warehouseService = {
    // Service to create a new warehouse address
    async createWarehouseAddress(warehouseData, user) {
        try {
            let sql = `${hcldb}.CreateWarehouseAddress`;
            
            let organizationId = null;
            let clientId = user.clientId;

            if(warehouseData.address.addressType === "hub") {
                organizationId = user.organizationId;
                clientId = null;
            } 

            let inputs = [
                warehouseData.addressDetailType,
                warehouseData.consigneeFirstName,
                warehouseData.consigneeLastName,
                warehouseData.businessName,
                warehouseData.contact.consigneeContact,
                warehouseData.contact.alternateContact,
                warehouseData.contact.email,
                warehouseData.address.addressType,
                warehouseData.address.addressLine1,
                warehouseData.address.addressLine2,
                warehouseData.address.city,
                warehouseData.address.state,
                warehouseData.address.zip,
                warehouseData.address.country,
                user.name,  // created_by_name
                user.email,  // created_by_id
                user.id,
                clientId,
                organizationId,
            ];

            let result = await pool.executeProcedure(sql, inputs);

            return result;
        } catch (error) {
            throw error;
        }
    },

    async getWarehouseAddressById(id) {
        try {
            let sql = `${hcldb}.GetWarehouseAddressById`;
            console.log(sql);

            let inputs = [id];

            let [result] = await pool.executeProcedure(sql, inputs);

            return result;
        } catch (error) {
            throw error;
        }
    },

    async getAllWarehouseAddressesWithPaging(params) {
        try {
            let sql = `${hcldb}.GetAllWarehouseAddressesWithPaging`;
             
            // Destructure parameters
            const {
                limit,
                offset,
                addressDetailType,
                consigneeFirstName,
                consigneeLastName,
                city,
                state,
                zip,
                country,
                warehouseId,
                consigneeContact,
                addressType,
                userData
            } = params;

            let organizationId = null;
            let clientId = userData.clientId;

            if(addressType === "hub") {
                organizationId = userData.organizationId;
                clientId = null;
            } 

            let inputs = [
                limit,
                offset,
                addressDetailType || null,
                consigneeFirstName || null,
                consigneeLastName || null,
                city || null,
                state || null,
                zip || null,
                country || null,
                warehouseId || null,
                consigneeContact || null,
                clientId,
                organizationId
            ];

            let [addresses, totalRecords] = await pool.executeProcedure(sql, inputs);

            return {
                addresses,
                totalRecords: totalRecords[0].total_records
            };
        } catch (error) {
            throw error;
        }
    },

    // Update warehouse address by ID
    async updateWarehouseAddress(id, warehouseData, user) {
        try {
            let sql = `${hcldb}.UpdateWarehouseAddress`;
            console.log(sql);

            let inputs = [
                id,
                warehouseData.addressDetailType,
                warehouseData.consigneeFirstName,
                warehouseData.consigneeLastName,
                warehouseData.businessName,
                warehouseData.contact.consigneeContact,
                warehouseData.contact.alternateContact,
                warehouseData.contact.email,
                warehouseData.address.addressType,
                warehouseData.address.addressLine1,
                warehouseData.address.addressLine2,
                warehouseData.address.city,
                warehouseData.address.state,
                warehouseData.address.zip,
                warehouseData.address.country,
                user.name,   // updated_by_name
                user.email,   // updated_by_id
                user.id
            ];

            let result = await pool.executeProcedure(sql, inputs);

            return result;
        } catch (error) {
            throw error;
        }
    }
};

module.exports = warehouseService;
