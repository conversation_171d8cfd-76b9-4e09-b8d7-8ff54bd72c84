const WalletService = require('../../../services/hcl/walletService');
const { validateRechargeWallet, 
        validateWalletTransaction, 
        validateGetWalletTransaction,
        validateGetWalletBalances } = require('./validators/walletValidator');
const helpers = require('../../../misc/helpers');
const OrderStatus = require('../../../misc/enums/orderStatusEnum');
const { throwError } = require('rxjs');

exports.rechargeWallet = async (req, res) => {
    try {
        const { error, value } = validateRechargeWallet(req.body)
        if (error) {
            return res.status(400).json({ success: false, message: error.details[0].message })
        }
        const { clientId, amount, transactionType, transactionReference, operation } = req.body
        const result = await WalletService.rechargeWallet(clientId, amount, transactionType, 
                                                          transactionReference, operation, req.user.id)
        helpers.sendSuccess(res, 200, 'Wallet recharged successfully!', result, req.body)
    } catch (error) {
        helpers.sendError(res, 500, 'rechargeWallet', error.message, req.body)
    }
};

exports.walletTransaction = async (req, res) => {
    try {
       
        const { error, value } = validateWalletTransaction(req.body);
        if (error) {
            helpers.sendError(res, 400, error.details[0].message, req.body)
        }

        let result;
        const { clientId, amount, transactionType, awbNumber, transactionReference, operation, createdBy = null} = req.body;
      
        if (operation === 'ORDER_CHARGE') {
            result = await WalletService.deductFromWallet(clientId, amount, transactionType, awbNumber, transactionReference, createdBy);
            helpers.sendSuccess(res, 200, 'Amount Charged.', result, req.body)
        } else if (operation === 'ORDER_REFUND') {
            result = await WalletService.refundToWallet(clientId, amount, transactionType, awbNumber, transactionReference, createdBy);
            helpers.sendSuccess(res, 200, 'Amount refunded.', result, req.body)
        } else {
            helpers.sendError(res, 400, "Invalid operation. Use 'ORDER_CHARGE' or 'ORDER_REFUND'.", req.body)
        }     

    } catch (error) {
        helpers.sendError(res, 500, 'walletTransaction', error.message, req.body)
    }
};

/**
 * get wallet balance by id
 * @param {*} req 
 * @param {*} res 
 */

exports.getWalletBalancebyClientId = async (req, res) => {
    try {
        const { clientId } = req.params
        if (!clientId || isNaN(clientId)) {
          helpers.sendError(res, 400, 'getWalletBalancebyClientId', 'Invalid client ID', req.params)
        }
        // Call the service to get balance of the client
        const result = await WalletService.getWalletBalancebyClientId(clientId)
        helpers.sendSuccess(res, 200, result.message, result, req.params)
    } catch (error) {
        helpers.sendError(res, 400, 'getWalletBalancebyClientId', "Wallet not found", req.params)
    }
};

/**
 * get all transaction of the clients
 * @param {*} req , client, page, limit
 * @param {*} res 
 */
exports.getWalletTransactions = async (req, res) => {
    try {
        const { error, value } = validateGetWalletTransaction(req.query);
        if (error) {
            helpers.sendError(res, 400, 'Validation Error', error.details[0].message, req.query)
        }

        const result = await WalletService.getWalletTransactions(req.query);
        helpers.sendPaginationResponse(res, 200, 'Users fetched successfully', result.transactions, result.totalRecords, result.pageNum, result.pageSize)
    } catch (error) {
        helpers.sendError(res, 500, 'Server Error', error.message, req.query)
    }
};


exports.getWalletBalances = async (req, res) => {
    try {
        const { error, value } = validateGetWalletBalances(req.query);
        if (error) {
            helpers.sendError(res, 400, 'Validation Error', error.details[0].message, req.query)
        }
        const { pageNum = 1, pageSize = 10} = req.query;

        let organizationId = req.userRoles.isRoleSuperAdmin ? null : req.user.organizationId;

        const { balances, totalRecords } = await WalletService.getWalletBalances(organizationId, parseInt(pageNum), parseInt(pageSize));
        helpers.sendPaginationResponse(res, 200, 'Client balances fetched successfully', balances, totalRecords, pageNum, pageSize)
    } catch (error) {
        helpers.sendError(res, 500, 'Server Error', error.message, req.query)
    }
};