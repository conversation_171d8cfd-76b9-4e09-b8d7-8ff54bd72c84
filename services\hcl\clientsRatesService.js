const dbpool = require('../../startup/db');
const miscConstants = require("../../misc/constants");
const cache = require('../../misc/cache.js');
const helpers = require('../../misc/helpers');
const ShippingProvideRatesService = require('./shippingProviderRatesService.js');
const WeightSlabService =  require('./weightSlabService.js');

const logger = require('../../misc/logger.js');

const hcldb = process.env.INITIAL_CATALOG || "hcl";

class ClientsRatesService {
    /**
     * Fetch priorities from the database (And set the cache and update).
     * @param {number} clientId - Client ID.
     * @param {string} orderType - Order Type (B2C, B2B).
     * @param {string} mode - Mode of transport (air, surface).
     * @param {string} refresh - 0, 1 if 1 refresh the cahed data
     * @returns {Array} - List of priorities.
    */
     static async getClientZonesRates (clientId, shippingProviderId, orderType, mode, flatData = 0, refresh = 0) {
        try {
            const groupCacheKey = `${miscConstants.CACHE.CLIENT_ZONES_RATES.GROUPKEY}_${clientId}_${shippingProviderId}_${orderType.toLowerCase()}_${mode.toLowerCase()}`;
            const cacheKey = `${miscConstants.CACHE.CLIENT_ZONES_RATES.KEY}_${clientId}_${shippingProviderId}_${orderType.toLowerCase()}_${mode.toLowerCase()}`;
            const cacheTTL = miscConstants.CACHE.CLIENT_ZONES_RATES.TTL;

            //console.log(refresh);
            // Check if data is already in cache
            // if( refresh === 0 ) {
            //    // console.log(refresh);
            //     if(flatData){
            //     //    console.log(flatData);
            //         const cachedData = cache.get(cacheKey);
            //         if (cachedData) {
            //             return cachedData;
            //         }
                    
            //     } else {
            //         const groupedCachedData = cache.get(groupCacheKey);
            //         if (groupedCachedData) {
            //             return groupedCachedData;
            //         }
            //     }    
            // }
            // Fetch from database
            const sql = `${hcldb}.GetClientZoneRates`;
            const [rows] = await dbpool.executeProcedure(sql, [clientId, shippingProviderId, orderType, mode]);
            // Group the data by zone and weight slab
            const groupedData = rows.reduce((result, row) => {
                const { weightSlabId, zoneCode, ...priorities } = row;

                if (!result[weightSlabId]) {
                    result[weightSlabId] = {};
                }
                if (!result[weightSlabId][zoneCode]) {
                    result[weightSlabId][zoneCode] = [];
                }

                result[weightSlabId][zoneCode].push(priorities);

                return result;
            }, {});

            // Cache the grouped data
            cache.set(groupCacheKey, groupedData, cacheTTL);
            cache.set(cacheKey, rows, cacheTTL);
            if(flatData) {
                return rows;
            } else {
                return groupedData;
            }
        
        } catch (error) {
            throw error;
        }
    }

  
    /**
     * 
     * @param {*} param0 
     * @param {*} createdBy 
     * @returns 
     */

    static async upsertClientZonesRates ({clientId, shippingProviderId, mode, orderType, weightSlabId, rates}, createdBy) {
        try {
            // Convert data array into a JSON string
            const jsonData = JSON.stringify(rates);
            const inputs = [ clientId, shippingProviderId, mode, orderType, weightSlabId, jsonData, createdBy ];
            const query= `${hcldb}.UpsertClientZonesRates`;
            // Execute the stored procedure
            const results = await dbpool.executeProcedure(query, [inputs]);
            // Cache Key and Data
            const cacheData = await this.getClientZonesRates(clientId, shippingProviderId, orderType, mode, 0, 1); // Fetch fresh data and add in cache
            return results;
        } catch (error) {
            console.error('error upserting client zone rates', error);
            throw error;
        }
    }

    /**
     * 
     * @param {*} clientId 
     * @param {*} refresh 
     * @returns 
     */
    static async getCodRateByClient (clientId, shippingProviderId, refresh = 0) {
        try {
            const cacheKey = `${miscConstants.CACHE.CLIENT_COD_RATES.KEY}_${clientId}_${shippingProviderId}`;
            const cacheTTL = miscConstants.CACHE.CLIENT_COD_RATES.TTL;
            let cachedData = '';
            // Check if data is already in cache
            if(refresh === 0) {
                cachedData = cache.get(cacheKey);    
            }
            console.log(cachedData);

            if (!cachedData) {
                const query = `${hcldb}.GetClientCODRates`;
                const [results] =  await dbpool.executeProcedure(query, [clientId, shippingProviderId]);
                cachedData = results[0];
                cache.set(cacheKey, cachedData, cacheTTL);
            }
            return cachedData;

        } catch (error) {
            throw error;
        }    
    }
    
    /**
     * 
     * @param {*} clientId 
     * @param {*} codAmount 
     * @param {*} codPercentage 
     * @param {*} flueSurcharge 
     * @param {*} docketCharges 
     * @param {*} createdBy 
     * @returns 
     */
    static async upsertClientCODRates(clientId, shippingProviderId, codAmount, codPercentage, flueSurcharge, docketCharges, createdBy) {
        try{
            const query = `${hcldb}.UpsertClientCODRates`;
            const result = await dbpool.executeProcedure(
                query, 
                [clientId, shippingProviderId, codAmount, codPercentage, flueSurcharge, docketCharges, createdBy]
            );
            const updateCachedData = this.getCodRateByClient(clientId, shippingProviderId, 1);
            return result;
        } catch (error) {
            throw error;
        }    
    }

    /**
     * 
     * @param {*} clientId 
     * @param {*} weightSlabId 
     * @param {*} zoneId 
     * @param {*} mode 
     * @param {*} orderType 
     * @param {*} isCod 
     * @returns 
     */

    static async getRatesBasedOnWeightZone (clientId, shippingProviderId, effectiveWeight, zoneId, mode = 'surface', orderType = 'B2C',  isCod = false) {
        try {
            const clientZoneRates = await this.getClientZonesRates (clientId, shippingProviderId, orderType, mode, 1);
            //console.log(clientZoneRates);
            // Validate that the cache is an array
            if (!Array.isArray(clientZoneRates)) {
                throw new Error('Invalid client zone rate. Expected an array.');
            }
        
            // // return weightSlabs;
            const filteredSlabs = await clientZoneRates.filter( async (slab) => 
                slab.zoneId == zoneId
            );
            // // Step 2: Sort by base_weight in descending order
            const sortedSlabs = filteredSlabs.sort((a, b) => b.weightSlab - a.weightSlab);
        
            // // Step 3: Find the first slab where base_weight <= effective_weight
            let matchingRate = await sortedSlabs.find(async(slab) => slab.weightSlab <= effectiveWeight);
        
            // If no matching rate is found, throw an error
            if (!matchingRate) {
                throw new Error(`No rate found for weight slab ID ${effectiveWeight} and zone ID ${zoneId}.`);
            }
            return matchingRate; // Return the matching rate
        } catch (error) {
            throw error;
        }
    }

     /**
     * 
     * @param {*} rates 
     * @param {*} userId 
     * @returns 
     */
    static async upsertClientAdditionalRates (rateData, userId) {
    
        try {
            const { clientId, shippingProviderId, orderType, rates } = rateData;
            // Convert rates array to JSON string for stored procedure
            const ratesJson = JSON.stringify(rates);

            const query= `${hcldb}.UpsertClientAdditionalRates`;
            const results = await dbpool.executeProcedure(query, [clientId, shippingProviderId, orderType, ratesJson, userId]);
            const newRates = results[0];
            // Invalidate cache for this provider
            const cacheKey = `${miscConstants.CACHE.CLIENT_ADDITIONAL_RATES.KEY}${clientId}:${shippingProviderId}:${orderType}`;
            cache.delete(cacheKey);   
            return newRates;
        } catch (error) {
            console.log('error upserting client addition rate', error);
            throw error;
        }
    }

    /**
     * 
     * @param {*} providerId 
     * @returns 
     */
    static async getClientAdditionalRates (clientId, shippingProviderId, orderType) {
        // Try to get from cache first
        const cacheKey = `${miscConstants.CACHE.CLIENT_ADDITIONAL_RATES.KEY}${clientId}:${shippingProviderId}:${orderType}`;
        const CACHE_TTL = miscConstants.CACHE.CLIENT_ADDITIONAL_RATES.TTL;
        const cachedRates = await cache.get(cacheKey);
        
        // if (cachedRates) {
        //     return cachedRates;
        // }

        try {
            const query= `${hcldb}.GetClientAdditionalRates`;
            const results = await dbpool.executeProcedure(query, [clientId, shippingProviderId, orderType]);

            const rates = results[0];
            // Store in cache
            cache.set(cacheKey, rates, CACHE_TTL);
            
            return rates;
        } catch (error) {
            throw error;
        }
    }

    
     /**
     * 
     * @param {*} clientId 
     * @returns 
     */
     static async getEnabledProviders(clientId) {
        
        try {

            const cacheTTL = miscConstants.CACHE.CLIENT_PROVIDERS.TTL;
            const cacheKey = `${miscConstants.CACHE.CLIENT_PROVIDERS.KEY}:${clientId}`;
            
            const cachedData = await cache.get(cacheKey);

            // if (cachedData) {
            //     return cachedData;
            // }

            const sql = `${hcldb}.GetClientEnabledProviders`;
            const [result] = await dbpool.executeProcedure(sql, [clientId]);

            const providersWithRates = result;

            await cache.set(
                cacheKey, 
                providersWithRates, 
                cacheTTL
            );

            return providersWithRates;
        } catch (error) {
            throw new Error(`Error fetching enabled providers: ${error.message}`);
        }
    }

     /**
     * 
     * @param {*} clientId 
     * @returns 
     */
     static async getClientProviders(clientId, providerId) {
        
        try {
            const clientProvidersRates = await this.getEnabledProviders(clientId);
            // console.log(clientProvidersRates);

            if (!Array.isArray(clientProvidersRates)) {
                throw new Error('Invalid client rate. Expected an array.');
            }

            const providerRates = clientProvidersRates.filter( (rate) => 
                rate.providerId == providerId
            );
    
            return providerRates;

        } catch (error) {
            throw new Error(`Error fetching enabled providers: ${error.message}`);
        }
    }

    
    /**
     * 
     * @param {*} providerData 
     * @param {*} userId 
     * @returns 
     */
     static async upsertClientProvider(providerData, userId) {
        const connection = await dbpool.getConnection();
        await connection.query('SET SESSION TRANSACTION ISOLATION LEVEL READ UNCOMMITTED');
        try {
          
            await connection.beginTransaction(); // Start transaction

            const {
                clientId,
                providerId,
                orderType,
                isProviderRates,
                priceIncrement,
                isEnabled = true,
                effectiveDate = new Date()
            } = providerData;

            const jsonData = JSON.stringify(priceIncrement);
            const inputs = [ clientId, providerId, orderType, isProviderRates, jsonData, isEnabled, effectiveDate, userId];
            const procedureName = `${hcldb}.UpsertClientProvider`;  
            const sql = `CALL ${procedureName}(${inputs.map(() => '?').join(', ')})`;
            const [result] = await connection.query(sql, inputs);
            const updatedProvider = result[0];
            // Invalidate cache
            
            const cacheKey = `${miscConstants.CACHE.CLIENT_PROVIDERS.KEY}:${clientId}`;
            await cache.delete(cacheKey);
            
            // If `isProviderRates` is false no need to update the price
            if (!isProviderRates) {
                await connection.commit();
                return { success: true, message: 'Custom rates has been added.',  providerData};
            }

             // Delete old weight slabs, zone rates, and additional rates**
            await this.deleteClientRates(clientId, providerId, userId, connection);

            // Insert weight slabs and map IDs**
            const slabMapping = await this.insertClientWeightSlabs(clientId, providerId, orderType, userId, connection);

            if (Object.keys(slabMapping).length === 0) {
                await connection.commit();
                return { success: true, message: 'No weight slabs found for the provider' };
            }

            //Insert zone rates with price increment**
            const insertedZoneRates = await this.insertClientZoneRates(clientId, providerId, orderType, priceIncrement, slabMapping, isEnabled, effectiveDate, userId, connection);

            // Insert additional rates**
            const insertedAdditionalRates = await this.insertClientAdditionalRates(clientId, providerId, orderType, priceIncrement, isEnabled, effectiveDate, userId, connection);

            await connection.commit();
            
            await this.deleteClientCache(clientId, providerId);

            return {
                success: true,
                message: `${insertedZoneRates} zone rates and ${insertedAdditionalRates} additional rates added successfully`,
                providerData
            };

        } catch (error) {
            await connection.rollback();

            logger.logError('Error in upsertClientProvider:', error);
            console.error('Error in upsertClientProvider:', error);
            throw new Error(`Error upserting client provider: ${error.message}`);

        } finally {
            connection.release();
        }    
    }
    
    static async deleteClientRates(clientId, providerId, userId, connection) {
        try {
            await connection.execute(`DELETE FROM weight_slab_management WHERE client_id = ? AND shipping_provider_id = ?`, [clientId, providerId]);
            await connection.execute(`DELETE FROM clients_zones_rates WHERE client_id = ? AND shipping_provider_id = ?`, [clientId, providerId]);
            await connection.execute(`DELETE FROM clients_additional_rates WHERE client_id = ? AND shipping_provider_id = ?`, [clientId, providerId]);
        } catch (error) {
            console.error('Error deleting old values.', error)
            throw error;
        }
    }
    
    // Insert Client Weight Slabs 
    static async insertClientWeightSlabs(clientId, providerId, orderType, userId, connection) {
        try {
            const [providerWeightSlabs] = await connection.execute(`
                SELECT id, mode, base_weight, additional_weight, additional_weight_extra, is_active, created_by
                FROM shipping_providers_weight_slabs
                WHERE is_active = 1 AND shipping_provider_id = ? AND order_type = ?
            `, [providerId, orderType]);
        
            const slabMapping = {}; // Map provider slab ID -> client slab ID
        
            for (let slab of providerWeightSlabs) {
                const [insertResult] = await connection.execute(`
                    INSERT INTO weight_slab_management 
                    (client_id, shipping_provider_id, mode, base_weight, additional_weight, additional_weight_extra, is_active, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                `, [clientId, providerId, slab.mode, slab.base_weight, slab.additional_weight, slab.additional_weight_extra, slab.is_active, userId]);
        
                slabMapping[slab.id] = insertResult.insertId;
            }
        
            return slabMapping;
        } catch (error) { 
            console.error('Error deleting old values.', error);
            throw error;
        }      
    }

    // insert Client Zone Rates
    static async insertClientZoneRates(
        clientId, 
        providerId, 
        orderType, 
        priceIncrement, 
        slabMapping, 
        isEnabled, 
        effectiveDate, 
        userId, 
        connection
    ) {
        try {
            // Validate required parameters
            if (!clientId || !providerId || !orderType || !effectiveDate || !userId || !connection) {
                throw new Error('Missing required parameters');
            }
    
            // 1. Fetch provider zone rates
            const [providerZoneRates] = await connection.execute(`
                SELECT 
                    shipping_provider_id, 
                    mode, 
                    order_type, 
                    zone_id, 
                    providers_weight_slabs_id, 
                    rate, 
                    additional_rate, 
                    additional_rate_extra, 
                    effective_from, 
                    effective_to, 
                    is_active, 
                    created_by
                FROM shipping_providers_zones_rates
                WHERE is_active = 1 
                  AND shipping_provider_id = ? 
                  AND order_type = ? 
            `, [providerId, orderType]);
    
            if (providerZoneRates.length === 0) {
                console.log('No active provider zone rates found');
                return 0;
            }
    
            // 2. Prepare insert query
            const insertQuery = `
                INSERT INTO clients_zones_rates (
                    shipping_provider_id, 
                    client_id, 
                    mode, 
                    order_type, 
                    zone_id, 
                    weight_slab_id, 
                    rate, 
                    additional_rate, 
                    additional_rate_extra, 
                    effective_from, 
                    effective_to, 
                    is_active, 
                    created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
    
            // 3. Process each rate with transaction
            let insertedCount = 0;
            await connection.beginTransaction();
    
            try {
                for (const row of providerZoneRates) {
                    const weightSlabId = slabMapping[row.providers_weight_slabs_id];
                    if (!weightSlabId) {
                        console.warn(`No slab mapping found for provider slab ID: ${row.providers_weight_slabs_id}`);
                        continue;
                    }
    
                    const modeLower = row.mode.toLowerCase();
                    const incrementPercent = Number(priceIncrement[modeLower]) || 0;
    
                    // Calculate new rates with proper precision handling
                    const calculateNewRate = (baseRate) => {
                        if (baseRate === null || baseRate === undefined) return null;
                        const result = Number(baseRate) * (1 + incrementPercent / 100);
                        return Math.round((result + Number.EPSILON) * 100) / 100; // Proper rounding to 2 decimal places
                    };
    
                    const newRate = calculateNewRate(row.rate);
                    const newAdditionalRate = calculateNewRate(row.additional_rate);
                    const newAdditionalRateExtra = calculateNewRate(row.additional_rate_extra);
    
                    // Execute insert
                    await connection.execute(insertQuery, [
                        row.shipping_provider_id, 
                        clientId, 
                        row.mode, 
                        row.order_type, 
                        row.zone_id, 
                        weightSlabId, 
                        newRate, 
                        newAdditionalRate, 
                        newAdditionalRateExtra, 
                        effectiveDate, 
                        row.effective_to, 
                        isEnabled ? 1 : 0, 
                        userId
                    ]);
    
                    insertedCount++;
                }
    
                await connection.commit();
                console.log(`Successfully inserted ${insertedCount} client zone rates`);
                return insertedCount;
                
            } catch (batchError) {
                await connection.rollback();
                console.error('Batch insert failed, rolling back transaction', batchError);
                throw batchError;
            }
    
        } catch (error) {
            console.error('Error in insertClientZoneRates:', error);
            throw error;
        }
    }


    //Insert Additional Rates (Fixed & Percentage)    
    static async insertClientAdditionalRates(clientId, providerId, orderType, priceIncrement, isEnabled, effectiveDate, userId, connection) {
        try{
            const [providerAdditionalRates] = await connection.execute(`
                SELECT shipping_provider_id, order_type, rate_name, rate_value, rate_type, is_active, created_by
                FROM shipping_providers_additional_rates
                WHERE is_active = 1 AND shipping_provider_id = ? AND order_type = ?
            `, [providerId, orderType]);
        
            if (providerAdditionalRates.length === 0) return 0;
        
            let insertedCount = 0;
            const insertQuery = `
                INSERT INTO clients_additional_rates 
                (client_id, shipping_provider_id, order_type, rate_name, rate_value, rate_type, is_active, effective_from, created_by)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `;
        
            for (let row of providerAdditionalRates) {
                const newRateValue = row.rate_value;
        
                await connection.execute(insertQuery, [
                    clientId, providerId, row.order_type, row.rate_name, newRateValue, row.rate_type, 
                    isEnabled ? 1 : 0, effectiveDate, userId
                ]);
                insertedCount++;
            }
        
            return insertedCount;
        } catch (error) { 
            console.error('Error deleting old values.');
            throw error;
        }  
    }


    static async deleteClientCache(clientId, providerId) {
        try {
            if (!clientId || !providerId) {
                throw new Error('Both clientId and providerId are required');
            }
    
            await WeightSlabService.initializeWeightCache(clientId);

            const allKeys = await cache.keys();
            if (!allKeys || allKeys.length === 0) {
                console.log('Cache is empty, nothing to delete');
                return { deletedCount: 0 };
            }
    
            const patterns = [
                `${miscConstants.CACHE.CLIENT_ZONES_RATES.GROUPKEY}_${clientId}_*`,
                `${miscConstants.CACHE.CLIENT_ZONES_RATES.KEY}_${clientId}_*`,
                `${miscConstants.CACHE.CLIENT_PROVIDERS.KEY}:${clientId}`,
                `${miscConstants.CACHE.CLIENT_ADDITIONAL_RATES.KEY}${clientId}:*`,
            ];
    
            // Improved pattern matching that handles wildcards properly
            const keysToDelete = allKeys.filter(key => 
                patterns.some(pattern => {
                    const regexPattern = pattern
                        .replace(/\*/g, '.*') // Convert wildcards to regex
                        .replace(/\?/g, '.');  // Convert single char wildcards
                    const regex = new RegExp(`^${regexPattern}$`);
                    return regex.test(key);
                })
            );
    
            if (keysToDelete.length === 0) {
                console.log('No matching cache keys found for deletion');
                return { deletedCount: 0 };
            }
    
            await cache.delete(keysToDelete);
            console.log(`Successfully deleted ${keysToDelete.length} cache keys`);
            
            return { 
                success: true,
                deletedCount: keysToDelete.length,
                deletedKeys: keysToDelete 
            };
    
        } catch (error) {
            console.error(`Error in deleteClientCache: ${error.message}`, {
                clientId,
                providerId,
                errorStack: error.stack
            });
            throw new Error(`Failed to delete client cache: ${error.message}`);
        }
    }


}

module.exports = ClientsRatesService;      