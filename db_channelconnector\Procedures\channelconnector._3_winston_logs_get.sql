DROP PROCEDURE IF EXISTS channelconnector._3_winston_logs_get;

DELIMITER $$
CREATE PROCEDURE channelconnector.`_3_winston_logs_get`(
	`in_partition_key` date ,
	`in_operation_id` varchar(255)
 )
BEGIN
	/*
		Call channelconnector._3_winston_logs_get('2022-01-27','');
        
        Call channelconnector._3_winston_logs_get('2022-01-27','');
        
    */

	SELECT  
		`partition_key`,
		`operation_id`,
		`level`,
		`message`,
		`meta`,
        CAST(`timestamp` as char) as createdOn
	FROM 
		`channelconnector`.`winston_logs`
	WHERE 
		`partition_key` = `in_partition_key`
        AND 1 = (case when `in_operation_id` = '' then 1 else case when `operation_id` = `in_operation_id` then 1 else 0 end end);
		


END$$
DELIMITER ;