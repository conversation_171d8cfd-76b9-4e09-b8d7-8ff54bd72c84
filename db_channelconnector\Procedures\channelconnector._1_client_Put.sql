DROP PROCEDURE IF EXISTS channelconnector._1_client_Put;

DELIMITER $$
CREATE PROCEDURE channelconnector.`_1_client_Put`(
  `in_clientId` varchar(1024),
  `in_name` varchar(1024),
  `in_ownerCode` varchar(1024)
  )
BEGIN
	/*
		call channelconnector.`_1_client_Put`(
			'1234',
			'Test Name1',
            'Test Owner Code2'
        );
        
        call channelconnector.`_1_client_Get`(
			''
        );
    */
    DECLARE isExists TINYINT; 
    SET in_clientId = LOWER(`in_clientId`);
   
	SET isExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`clientId`) = `in_clientId` 
			AND T.isActive = 1 
		LIMIT 1 
    );
    
    IF (isExists = 0 OR isExists IS NULL)
    THEN
		call channelconnector.`_1_client_Post`(			`in_clientId`,			`in_name`,            `in_ownerCode`        );
	ELSE
		SET SQL_SAFE_UPDATES = 0;
        
        UPDATE `channelconnector`.`_1_client`
		SET
			`name` = in_name,
			`ownerCode` = in_ownerCode,
            `modifiedOn` = NOW()
		WHERE 	 
			LOWER(clientId) = in_clientId 
			AND isActive = 1;
            
		SET SQL_SAFE_UPDATES = 1;
        
		call channelconnector.`_1_client_Get`(`in_clientId`);

    END IF;
    
END$$
DELIMITER ;
