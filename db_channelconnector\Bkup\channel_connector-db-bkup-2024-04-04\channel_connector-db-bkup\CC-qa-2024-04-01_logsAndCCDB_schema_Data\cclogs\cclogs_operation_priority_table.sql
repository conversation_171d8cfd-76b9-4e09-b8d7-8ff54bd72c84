-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: cclogs
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `operation_priority_table`
--

DROP TABLE IF EXISTS `operation_priority_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `operation_priority_table` (
  `id` int NOT NULL,
  `operation` varchar(45) DEFAULT NULL,
  `base1` int DEFAULT NULL,
  `base2` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `operation_priority_table`
--

LOCK TABLES `operation_priority_table` WRITE;
/*!40000 ALTER TABLE `operation_priority_table` DISABLE KEYS */;
INSERT INTO `operation_priority_table` VALUES (1,'GET_PRODUCT_FROM_CHANNEL',3,33),(2,'GET_PRODUCT_LOCATION_FROM_CHANNEL',4,34),(3,'GET_ORDER_FROM_CHANNEL',5,35),(4,'GET_PRODUCT_REPORT_FROM_CHANNEL',2,32),(5,'GET_PRODUCT_FROM_FBY',6,36),(6,'GET_STOCK_FROM_FBY',8,38),(7,'GET_TRACKING_FROM_FBY',7,37),(8,'GET_PRICES_FROM_FBY',9,39),(9,'PUSH_STOCK_TO_CHANNEL',10,40),(10,'PUSH_PRODUCTS_TO_CHANNE',11,41),(11,'PUSH_TRACKING_TO_CHANNE',12,42),(12,'PUSH_PRODUCTS_TO_FBY',13,43),(13,'PUSH_ORDER_TO_FBY',14,44),(14,'PUSH_ORDER_TO_FBY_MISSING_DATA',15,45),(15,'PUSH_CANCELLED_ORDER_TO_FBY',16,46),(16,'PUSH_TRAKING_NOTIFICATION_TO_FBY',17,47),(17,'FBY_ALERT_INSERT',18,48),(18,'DELETE_VARIANTS_WITH_BLANK_SKU_CHANNEL',NULL,NULL),(19,'GET_AUTH_TOKEN_FROM_FBY',NULL,NULL),(20,'GET_MASTER_DATA_CARRIER_MIRAKL',19,49),(21,'GET_ACCEPT_ORDER_MIRAKL',NULL,NULL),(22,'GET_VALIDATE_SHIPMENT_MIRAKL',NULL,NULL);
/*!40000 ALTER TABLE `operation_priority_table` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:40:13
