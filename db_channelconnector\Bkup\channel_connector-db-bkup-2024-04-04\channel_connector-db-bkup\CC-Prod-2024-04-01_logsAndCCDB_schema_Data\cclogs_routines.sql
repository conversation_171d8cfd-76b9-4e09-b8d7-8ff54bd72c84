-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: cclogs
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Dumping events for database 'cclogs'
--
/*!50106 SET @save_time_zone= @@TIME_ZONE */ ;
/*!50106 DROP EVENT IF EXISTS `event_logsTableBackup` */;
DELIMITER ;;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;;
/*!50003 SET character_set_client  = utf8mb4 */ ;;
/*!50003 SET character_set_results = utf8mb4 */ ;;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;;
/*!50003 SET @saved_time_zone      = @@time_zone */ ;;
/*!50003 SET time_zone             = 'UTC' */ ;;
/*!50106 CREATE*/ /*!50117 DEFINER=`yocabedbadmin`@`%`*/ /*!50106 EVENT `event_logsTableBackup` ON SCHEDULE EVERY 1 DAY STARTS '2023-05-31 00:00:00' ON COMPLETION NOT PRESERVE ENABLE DO call cclogs._5_logsTableBackup () */ ;;
/*!50003 SET time_zone             = @saved_time_zone */ ;;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;;
/*!50003 SET character_set_client  = @saved_cs_client */ ;;
/*!50003 SET character_set_results = @saved_cs_results */ ;;
/*!50003 SET collation_connection  = @saved_col_connection */ ;;
DELIMITER ;
/*!50106 SET TIME_ZONE= @save_time_zone */ ;

--
-- Dumping routines for database 'cclogs'
--
/*!50003 DROP PROCEDURE IF EXISTS `_1_logs_Post` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`yocabedbadmin`@`%` PROCEDURE `_1_logs_Post`(
`in_fby_user_id` INT,
`in_order_no` varchar(128) ,
`in_sku` varchar(128) ,
`in_message` varchar(1024),
`in_data` text,
`in_level` varchar(128) ,
`in_codes` varchar(128) ,
`in_operation` varchar(256) ,
`in_operation_id` varchar(128) 

)
BEGIN

/*

	call cclogs._1_logs_Post(
		1002,
		'abc-01' ,
		'abc-02-sku' ,
		'test' ,
		'test' ,
		'ERROR'  ,
		'STOCK' ,
		'GET_STOCK_FROM_FBY'  ,
		'123-123-123' 

	);
    
    call cclogs._1_logs_Post(
		1002,
		'abc-01' ,
		'abc-02-sku' ,
		'test' ,
		'test' ,
		'ERROR'  ,
		'ORDER' ,
		'GET_ORDER_FROM_CHANNEL'  ,
		'123-123-123' 

	);
    
    
    call cclogs._1_logs_Post(
		1002,
		'abc-01' ,
		'abc-02-sku' ,
		'test' ,
		'test' ,
		'ERROR'  ,
		'PRODUCT' ,
		'GET_PRODUCT_FROM_CHANNEL'  ,
		'123-123-123' 

	);

	call cclogs._2_logs_error_Get('1002');

 	SELECT t2.*       
	FROM 
	cclogs._logs_error as t2;
    
    
SELECT * FROM cclogs._logs_error
Where sku = 'AD728' 
order by fby_user_id,id desc
;
         
*/


DECLARE varExistsId INT;
DECLARE varLastInsertId INT;
DECLARE varLastNDays DATETIME;
DECLARE varchecksum varchar(128);
DECLARE errno INT;
/*
DECLARE EXIT HANDLER FOR SQLEXCEPTION
BEGIN
	GET CURRENT DIAGNOSTICS CONDITION 1 errno = MYSQL_ERRNO;
	SELECT errno AS MYSQL_ERROR;
	ROLLBACK;
END;
*/
SET TRANSACTION ISOLATION LEVEL READ UNCOMMITTED;

SET varLastNDays = DATE_ADD(CAST(NOW() as DATE), INTERVAL -10 DAY);

SET in_order_no 	= if(`in_order_no` 	is null, '',`in_order_no`		);
SET in_sku 			= if(`in_sku` 		is null, '',`in_sku` 			);
SET in_message 		= if(`in_message` 	is null, '',TRIM(`in_message`)	);
SET in_level 		= if(`in_level` 	is null, '',`in_level` 			);
SET in_codes 		= if(`in_codes` 	is null, '',`in_codes` 			);
SET in_operation 	= if(`in_operation` is null, '',`in_operation`  	);	
SET in_data 		= if(`in_data` is null, '',TRIM(`in_data`)  		);	
SET in_codes 		= if(`in_codes` = 'TRACKS', 'SHIPMENT',`in_codes`   );

SET varchecksum = MD5(CONCAT(in_fby_user_id , in_order_no , in_sku , in_level , in_codes , in_operation , in_message ));
SET varExistsId = 
(
	SELECT  id 
    FROM cclogs._logs_error
    WHERE fby_user_id 	=	in_fby_user_id
		AND order_no  	=	in_order_no
		AND	sku 		=	in_sku
        AND	`level` 	=	in_level
        AND	fby_alert_code 	=	in_codes
        AND	cc_operation 	=	in_operation
        AND message	 =  in_message
        -- and `checksum` = varchecksum
	-- Order by id desc
    limit 1	
    
);

IF(varExistsId is null OR varExistsId = 0 or in_level = 'INFO')
THEN
		INSERT INTO cclogs._logs_error
		(
			`fby_user_id`,
			`order_no`,
			`sku`,
			`message`,
			`data`,
			`level`,
			`fby_alert_code`,
			`cc_operation`,
			`operation_id`,
			`checksum`
		)
		VALUES
		(
			`in_fby_user_id`,
			`in_order_no`,
			`in_sku`,
			`in_message`,
			`in_data`,
			`in_level`,
			`in_codes`,
			`in_operation`,
			`in_operation_id`,
			varchecksum
		);
        
        SET varLastInsertId = LAST_INSERT_ID();
        SET varExistsId = varLastInsertId;
        -- Select 1 as 'Inserted', varLastInsertId as id;
        
ELSE

	IF EXISTS (
    
		SELECT  id 
		FROM cclogs._logs_error
		WHERE fby_user_id 	=	in_fby_user_id
			AND order_no  	=	in_order_no
			AND	sku 		=	in_sku
			AND	`level` 	=	in_level
			AND	fby_alert_code 	=	in_codes
			AND	cc_operation 	=	in_operation
			AND message	 =  in_message
			-- and `checksum` = varchecksum
		Order by id desc
		limit 1	
		FOR UPDATE
    
	)
	THEN
		SET SQL_SAFE_UPDATES = 0;
		UPDATE cclogs._logs_error
		SET
			counter = (case when counter is null then 0 else counter end)   + 1,
			`order_no` = `in_order_no`,
			`sku`= `in_sku`,
			`message` = `in_message`,
			`data` = `in_data`,
			`level`= `in_level`,
			`fby_alert_code` = `in_codes`,
			`cc_operation`= `in_operation`,
			`operation_id` = `in_operation_id`,
			 updatedOn = NOW(),
			 `checksum` = varchecksum
		WHERE `id` = varExistsId;
		SET varLastInsertId = varExistsId;
	END IF;
END IF;

/*
Select l.id,l.order_no,l.sku, l.fby_user_id,l.cc_operation,l.level,CAST(l.message as CHAR) as message,l.counter,CAST(l.createdOn as CHAR) as createdOn, CAST(l.updatedOn as CHAR) as updatedOn
from cclogs._logs_error as l
WHERE `id` = varLastInsertId;
*/
Select varExistsId,varLastInsertId;

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_2_logs_Get` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`yocabedbadmin`@`%` PROCEDURE `_2_logs_Get`(
`in_fby_user_id` INT
)
BEGIN
/*
	call cclogs._2_logs_Get(1002);
  
 	SELECT t2.*       
	FROM cclogs._logs as t2;
*/

    SET transaction isolation level READ uncommitted;    
    
Select DISTINCT
	MAX(cl.id) as id,
	cl.fby_user_id,
	if(cl.order_no =0, '',cl.order_no) as order_no,
	cl.sku,
	replace(replace((if(clm.fby_error_message is not null and clm.fby_error_message <> '',clm.fby_error_message, cl.message)),"##orderid##", if(cl.order_no =0, '',cl.order_no)),"##sku##", if(cl.sku <>'', cl.sku, '')) as message,
    '' as cc_messgae,
	'' as `data`,
	cl.counter,
	cl.`level`,
	cl.fby_alert_code,
	cl.cc_operation as cc_operation,
	cl.operation_id,
	cl.partition_key,
	CAST(cl.createdOn as CHAR) createdOn,
    CAST(cl.updatedOn as CHAR) updatedOn,
    CAST(cl.alertSentOn as CHAR) alertSentOn,
	cl.isAlertSentToFBY,
    cl.fbyAlertRetryCounter,
    cl.fbyAlertInsertError
FROM cclogs._logs_error_mapping as clm
	
    inner join cclogs._logs_error as cl
    on clm.cc_operation = cl.cc_operation
    and clm.fby_alert_code = cl.fby_alert_code
    and clm.is_active = 1
    
    left join channelconnector.order_masters as om
	on om.fby_user_id	= cl.fby_user_id
    and om.fby_user_id	= in_fby_user_id
    And om.order_no	= cl.order_no
    -- and om.fby_send_status <> 1
 Where   
	cl.fby_user_id = in_fby_user_id
	AND (cl.isAlertSentToFBY = 0 or cl.isAlertSentToFBY is null)
    AND (cl.fbyAlertRetryCounter < 3 or cl.fbyAlertRetryCounter is null) 
    AND cl.level = 'ERROR'
    AND 1 = if(om.fby_send_status is null or om.fby_send_status = 0 , 1,0)
group by cl.fby_user_id,
	cl.order_no,
	cl.sku,
	cl.`level`,
	cl.fby_alert_code,
	cl.cc_operation,
	cl.operation_id,
	cl.partition_key
	
    
ORDER BY cl.id,
	cl.fby_user_id

limit 5    
;

		

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_3_logs_Update_Sucess` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`yocabedbadmin`@`%` PROCEDURE `_3_logs_Update_Sucess`(
	in_fby_user_id INT,
	logIds text
)
BEGIN
	/*
		call cclogs._3_logs_Update_Sucess(39,'1,2,3');
    */
	SET SQL_SAFE_UPDATES = 0;
    
    UPDATE cclogs._logs_error
	SET
		isAlertSentToFBY = 1,
		alertSentOn = NOW(),
        updatedOn = NOW()
	WHERE 
		fby_user_id = in_fby_user_id
		AND FIND_IN_SET(id, logIds);

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_4_logs_Update_Fail` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`yocabedbadmin`@`%` PROCEDURE `_4_logs_Update_Fail`(
	in_fby_user_id INT,
	logIds text,
    in_fbyAlertInsertError text
)
BEGIN
	/*
		call cclogs._4_logs_error_Update_Fail(1002,3,'');
    */
	SET SQL_SAFE_UPDATES = 0;
    
    UPDATE cclogs._logs_error
	SET
		isAlertSentToFBY = 0,
        fbyAlertRetryCounter = (case when fbyAlertRetryCounter is null then 0 else fbyAlertRetryCounter end)   + 1,
		alertSentOn = NULL,
        fbyAlertInsertError = in_fbyAlertInsertError,
        updatedOn = NOW()
	WHERE 
		fby_user_id = in_fby_user_id
		AND FIND_IN_SET(id, logIds);

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_5_logsTableBackup` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`yocabedbadmin`@`%` PROCEDURE `_5_logsTableBackup`()
BEGIN
	DECLARE new_table_name varchar(128);
    DECLARE sql_text text;
     DECLARE createTableSql text;
	SET new_table_name = CONCAT('`_logs','_',LEFT(CAST(NOW() AS CHAR),10),'`');
    Set new_table_name = replace(new_table_name,'-','');

	Select new_table_name;
	 
	IF NOT EXISTS (
		Select * from information_schema.tables
		Where table_name =   new_table_name  
        and TABLE_SCHEMA = 'cclogs'
	)
	THEN
    -- RENAME TABLE cclogs._logs TO cclogs.`_logs_2023-03-07`;

	SET sql_text = 	   CONCAT('RENAME TABLE cclogs._logs TO cclogs.',new_table_name,'; ');
    
    SET @sql_text = sql_text;
    PREPARE stmt FROM @sql_text;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
	 
    END IF;


IF NOT EXISTS (
		Select * from information_schema.tables
		Where table_name =   '_logs'  
        and TABLE_SCHEMA = 'cclogs'
	)
	THEN
    
    CREATE TABLE cclogs._logs (
	  `id` int unsigned NOT NULL AUTO_INCREMENT,
	  `fby_user_id` int NOT NULL,
	  `order_no` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
	  `sku` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
	  `message` varchar(1024) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
	  `data` text CHARACTER SET utf8 COLLATE utf8_bin,
	  `counter` int DEFAULT '0',
	  `level` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT 'INFO',
	  `fby_alert_code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	  `cc_operation` varchar(256) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
	  `operation_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
	  `createdOn` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
	  `updatedOn` datetime DEFAULT NULL,
	  `isAlertSentToFBY` tinyint DEFAULT '0',
	  `alertSentOn` datetime DEFAULT NULL,
	  `fbyAlertRetryCounter` int DEFAULT '0',
	  `fbyAlertInsertError` text,
	  `partition_key` date NOT NULL DEFAULT (curdate()),
	  `checksum` varchar(128) DEFAULT '',
	  PRIMARY KEY (`id`,`fby_user_id`,`partition_key`)
	) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb3;
        
	ALTER TABLE `cclogs`.`_logs` 
	PARTITION BY KEY(fby_user_id,partition_key);
    
    END IF;
END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
/*!50003 DROP PROCEDURE IF EXISTS `_6_logs_Get_by_alert_id` */;
/*!50003 SET @saved_cs_client      = @@character_set_client */ ;
/*!50003 SET @saved_cs_results     = @@character_set_results */ ;
/*!50003 SET @saved_col_connection = @@collation_connection */ ;
/*!50003 SET character_set_client  = utf8mb4 */ ;
/*!50003 SET character_set_results = utf8mb4 */ ;
/*!50003 SET collation_connection  = utf8mb4_0900_ai_ci */ ;
/*!50003 SET @saved_sql_mode       = @@sql_mode */ ;
/*!50003 SET sql_mode              = 'NO_ENGINE_SUBSTITUTION' */ ;
DELIMITER ;;
CREATE DEFINER=`yocabedbadmin`@`%` PROCEDURE `_6_logs_Get_by_alert_id`(
`in_alert_id` varchar(128)
)
BEGIN
/*
	call cclogs._2_logs_Get(39);
  
 	SELECT t2.*       
	FROM cclogs._logs as t2;
*/
	SET transaction isolation level READ uncommitted;    
    
    SET in_alert_id = replace(lower(in_alert_id),'alert-','');
    
	Select DISTINCT
	MAX(cl.id) as id,
    CAST(cl.createdOn as CHAR) createdOn,
    
    CAST(cl.alertSentOn as CHAR) alertSentOn,
	cl.fby_user_id,
	if(cl.order_no =0, '',cl.order_no) as order_no,
	cl.sku,
	replace(replace((if(clm.fby_error_message is not null and clm.fby_error_message <> '',clm.fby_error_message, cl.message)),"##orderid##", if(cl.order_no =0, '',cl.order_no)),"##sku##", if(cl.sku <>'', cl.sku, '')) as message,
    cl.message as cc_messgae,
	`data`,
	cl.counter,
	cl.`level`,
	cl.fby_alert_code,
	cl.cc_operation as cc_operation,
	cl.operation_id,
	cl.partition_key,
	
	cl.isAlertSentToFBY,
    cl.fbyAlertRetryCounter,
    cl.fbyAlertInsertError,
    CAST(cl.updatedOn as CHAR) updatedOn
FROM cclogs._logs_error_mapping as clm
	
    inner join cclogs._logs_error as cl
    on clm.cc_operation = cl.cc_operation
    and clm.fby_alert_code = cl.fby_alert_code
    and clm.is_active = 1
    
    left join channelconnector.order_masters as om
	on om.fby_user_id	= cl.fby_user_id
    -- and om.fby_user_id	= in_fby_user_id
    And om.order_no	= cl.order_no
    -- and om.fby_send_status <> 1
 Where   
	cl.id = in_alert_id
	-- AND (cl.isAlertSentToFBY = 0 or cl.isAlertSentToFBY is null)
    -- AND (cl.fbyAlertRetryCounter < 3 or cl.fbyAlertRetryCounter is null) 
    AND cl.level = 'ERROR'
    -- AND 1 = if(om.fby_send_status is null or om.fby_send_status = 0 , 1,0)
group by cl.fby_user_id,
	cl.order_no,
	cl.sku,
	cl.`level`,
	cl.fby_alert_code,
	cl.cc_operation,
	cl.operation_id,
	cl.partition_key
	
    
ORDER BY cl.id,
	cl.fby_user_id

-- limit 5    
;

	-- select in_alert_id;	

END ;;
DELIMITER ;
/*!50003 SET sql_mode              = @saved_sql_mode */ ;
/*!50003 SET character_set_client  = @saved_cs_client */ ;
/*!50003 SET character_set_results = @saved_cs_results */ ;
/*!50003 SET collation_connection  = @saved_col_connection */ ;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:25:39
