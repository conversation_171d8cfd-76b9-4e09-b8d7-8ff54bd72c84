const CONSTANTS = require("../../misc/constants");
const helpers = require('../../misc/helpers');
const logger = require('../../misc/logger.js');
const RoleService = require('../../services/users/roleService.js');

exports.getRoles = async (req, res) => {
    try {
        const isSuperAdmin = req.userRoles?.isRoleSuperAdmin || false;
        // console.log(isSuperAdmin);
        let roles = await RoleService.getRoles();
         // Filter out Super Admin if user is not Super Admin
         if (!isSuperAdmin) {
            const superAdminRole = roles.find(r => r.name.toLowerCase() === "super admin");
            if (superAdminRole) {
                roles = roles.filter(role => role.id !== superAdminRole.id);
            }
        }

        return helpers.sendSuccess(
            res, 
            CONSTANTS.HTTPSTATUSCODES.OK, 
            CONSTANTS.SUCESSSMESSAGES.GET, 
            roles, 
            req.body
        );
        
    } catch (error) {
        return helpers.sendError(
            res, 
            CONSTANTS.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
            CONSTANTS.ERRORCODES.INTERNAL_SERVER_ERROR, 
            error.message, 
            req.body
        );
    }
};
  
