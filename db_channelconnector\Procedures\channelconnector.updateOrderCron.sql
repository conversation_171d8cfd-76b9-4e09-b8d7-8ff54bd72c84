DROP PROCEDURE IF EXISTS channelconnector.updateOrder<PERSON>ron;

DEL<PERSON>ITER $$
CREATE PROCEDURE channelconnector.updateOrderCron(
	`in_ordr_no` VARCHAR(256),
	`in_crn_name` VARCHAR(60),
	`in_crnid` VARCHAR(100),
	`in_time` DATETIME
)
BEGIN
	SET SQL_SAFE_UPDATES = 0;
	UPDATE order_masters 
	SET 
		cron_name = in_crn_name,
		cron_id = in_crnid,
		updated_at = in_time
	WHERE
		order_no = in_ordr_no;
	SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;
