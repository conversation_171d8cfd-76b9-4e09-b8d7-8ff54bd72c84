const dbpool = require('../../startup/db.js');
const hcldb = process.env.INITIAL_CATALOG || "hcl";
const miscConstants = require("../../misc/constants.js");
const logger = require('../../misc/logger.js');
const cache = require('../../misc/cache.js');

class ManifestService {
    constructor() {
        this.cacheKeyPrefix = miscConstants.CACHE.MANIFEST.KEY;
        this.cacheTTL = miscConstants.CACHE.MANIFEST.TTL; // 1 hour
    }

    async getManifestDetails(manifestId) {
        try {
            // Check cache first
            const cacheKey = `${this.cacheKeyPrefix}${manifestId}`;
            const cachedData = await cache.get(cacheKey);
        
            if (cachedData) {
                return cachedData;
            }
       
            const query = `${hcldb}.GetManifestDetails`;
            const results = await dbpool.executeProcedure(query, [manifestId]);

            console.log(results);
            const manifestData = {
                manifest: results[0][0],
                orders: results[1]
            };

            // Cache the result
            cache.set(cacheKey, manifestData, this.cacheTTL);
            return manifestData;
        } catch (error) {
            logger.logError('Error getting Manifest Details', error);
            throw error;
        }
    }

    async createManifest(shippingProviderId, orderIds, userId, clientId, organizationId) {
        const pool = await dbpool.initializePool(hcldb);
        const connection = await pool.getConnection();
        try {
            await connection.beginTransaction();

            // Create manifest
            const [manifestResult] = await connection.query(
                `CALL ${hcldb}.CreateManifest(?, ?, ?, ?, @manifest_id)`,
                [shippingProviderId, userId, clientId, organizationId]
            );

            const [[{p_manifest_id: manifestId}]] = await connection.query('SELECT @manifest_id as p_manifest_id');

            // Add orders
            await connection.query(
                `CALL ${hcldb}.AddOrdersToManifest(?, ?)`,
                [manifestId, JSON.stringify(orderIds)]
            );

            await connection.commit();

            // Invalidate relevant caches
            await this.invalidateManifestCaches(manifestId);

            return manifestId;
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }

    async getManifests(fromDate, toDate, clientId, organizationId) {
       
        try {
            const query = `${hcldb}.GetAllManifests`;
            const [rows] = await dbpool.executeProcedure(query, [fromDate, toDate, clientId, organizationId]);
            if (rows && rows.length > 0) {
                const manifests = rows;
                return manifests;
            }
            return {}; // Return empty object if no data found    
        } catch (error) {
            logger.logError("Error fetching manifests:", error);
            throw error;
        }
    }


    async updateManifestStatus(manifestId, status, userId) {
        const pool = await dbpool.initializePool(hcldb);
        const connection = await pool.getConnection();
        try {
            await connection.beginTransaction();

            // Update manifest status
            await connection.query(
                `UPDATE ${hcldb}.manifests 
                 SET status = ?,
                     ${status === 'submitted' ? 'submitted_at = NOW()' : 
                       status === 'completed' ? 'completed_at = NOW()' : ''}
                 WHERE manifest_id = ?`,
                [status, manifestId]
            );

            // Add status history
            await connection.query(
                `INSERT INTO ${hcldb}.manifest_status_history 
                 (manifest_id, status, changed_by)
                 VALUES (?, ?, ?)`,
                [manifestId, status, userId]
            );

            await connection.commit();

            // Invalidate cache
            await this.invalidateManifestCaches(manifestId);

        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    }

    async invalidateManifestCaches(manifestId) {
        const cacheKey = `${this.cacheKeyPrefix}${manifestId}`;
        await cache.delete(cacheKey);
    }
}

module.exports = new ManifestService();