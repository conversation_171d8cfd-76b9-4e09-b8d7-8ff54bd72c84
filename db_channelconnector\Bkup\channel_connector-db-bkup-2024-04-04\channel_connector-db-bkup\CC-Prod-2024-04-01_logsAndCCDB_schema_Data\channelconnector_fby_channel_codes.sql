-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: yocabe-rdsmysql-prd.c6oqnu6x80jp.eu-central-1.rds.amazonaws.com    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `fby_channel_codes`
--

DROP TABLE IF EXISTS `fby_channel_codes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fby_channel_codes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `code` varchar(128) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=25735 DEFAULT CHARSET=utf8mb3;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fby_channel_codes`
--

LOCK TABLES `fby_channel_codes` WRITE;
/*!40000 ALTER TABLE `fby_channel_codes` DISABLE KEYS */;
INSERT INTO `fby_channel_codes` VALUES (1,'Pricy IT','PRICY','2022-01-28 17:26:43'),(2,'Amazon DE','AMDE','2022-01-28 17:26:43'),(3,'Amazon IT','AMIT','2022-01-28 17:26:43'),(4,'Amazon ES','AMES','2022-01-28 17:26:43'),(5,'Amazon FR','AMFR','2022-01-28 17:26:44'),(6,'Amazon UK','AMUK','2022-01-28 17:26:44'),(7,'Amazon US','AMUS','2022-01-28 17:26:44'),(8,'Ebay IT','EBIT','2022-01-28 17:26:44'),(9,'Ebay FR','EBFR','2022-01-28 17:26:44'),(10,'Ebay ES','EBES','2022-01-28 17:26:44'),(11,'Ebay DE','EBDE','2022-01-28 17:26:44'),(12,'Ebay UK','EBUK','2022-01-28 17:26:44'),(13,'Fnac FR','FNFR','2022-01-28 17:26:44'),(14,'La Redoute FR','LRFR','2022-01-28 17:26:44'),(15,'Galleries Lafayette FR','GLFR','2022-01-28 17:26:44'),(16,'Zalando IT','ZLIT','2022-01-28 17:26:44'),(17,'Cdiscount FR','CDFR','2022-01-28 17:26:44'),(18,'Privalia IT','PRIT','2022-01-28 17:26:44'),(19,'Spartoo IT','STIT','2022-01-28 17:26:44'),(20,'Spartoo FR','STFR','2022-01-28 17:26:44'),(21,'BrandAlley FR','BAFR','2022-01-28 17:26:44'),(22,'Ebay US','EBUS','2022-01-28 17:26:44'),(23,'Amazon CA','AMCA','2022-01-28 17:26:44'),(24,'Ebay CA','EBCA','2022-01-28 17:26:44'),(25,'ApiDrop','ADIT','2022-01-28 17:26:44'),(26,'Zalando DE','ZLDE','2022-01-28 17:26:45'),(27,'Amazon NL','AMNL','2022-01-28 17:26:45'),(28,'Vendita diretta','DIR','2022-01-28 17:26:45'),(29,'Amazon SE','AMSE','2022-01-28 17:26:45'),(30,'Veepee IT','VPIT','2022-01-28 17:26:45'),(31,'Veepee FR','VPFR','2022-01-28 17:26:45'),(32,'Veepee ES','VPES','2022-01-28 17:26:45'),(33,'Zalando FR','ZLFR','2022-01-28 17:26:45'),(34,'Zalando CH DE','ZLCHDE','2022-01-28 17:26:45'),(35,'Zalando CH FR','ZLCHFR','2022-01-28 17:26:45'),(36,'Zalando NL','ZLNL','2022-01-28 17:26:45'),(37,'Zalando PL','ZLPL','2022-01-28 17:26:45'),(38,'Zalando AT','ZLAT','2022-01-28 17:26:45'),(39,'Zalando BE FR','ZLBEFR','2022-01-28 17:26:45'),(40,'Zalando BE NL','ZLBENL','2022-01-28 17:26:45'),(41,'Refrigiwear DE','RWDE','2022-01-28 17:26:45'),(42,'Refrigiwear ES','RWES','2022-01-28 17:26:45'),(43,'Refrigiwear FR','RWFR','2022-01-28 17:26:45'),(44,'Refrigiwear IT','RWIT','2022-01-28 17:26:45'),(45,'Refrigiwear UK','RWUK','2022-01-28 17:26:45'),(46,'Shopify IT','SHIT','2022-01-28 17:26:45'),(47,'Shopify DE','SHDE','2022-01-28 17:26:45'),(62,'Galeries Lafayette FR','GLFR','2022-03-02 00:05:01'),(75,'Vendita diretta IT','VDIT','2022-03-02 00:05:01'),(93,'Zalando ES','ZLES','2022-03-02 00:05:01'),(96,'Zalando DK','ZLDK','2022-03-02 00:05:01'),(97,'Zalando SE','ZLSE','2022-03-02 00:05:01'),(98,'Yoox IT','YOIT','2022-03-02 00:05:01'),(99,'Yoox BE','YOBE','2022-03-02 00:05:01'),(100,'Yoox DE','YODE','2022-03-02 00:05:01'),(101,'Yoox ES','YOES','2022-03-02 00:05:01'),(102,'Yoox FR','YOFR','2022-03-02 00:05:01'),(103,'Yoox NL','YONL','2022-03-02 00:05:01'),(104,'Storeden IT','SRIT','2022-03-02 00:05:01'),(105,'Prestashop IT','PSIT','2022-03-02 00:05:01'),(106,'Prestashop DE','PSDE','2022-03-02 00:05:01'),(107,'Prestashop ES','PSES','2022-03-02 00:05:01'),(108,'Prestashop FR','PSFR','2022-03-02 00:05:01'),(109,'Prestashop UK','PSUK','2022-03-02 00:05:01'),(12944,'Rimozioni','RMIT','2022-03-16 20:00:04'),(25734,'Woocommerce IT','WCIT','2022-03-30 10:00:06');
/*!40000 ALTER TABLE `fby_channel_codes` ENABLE KEYS */;
UNLOCK TABLES;
SET @@SESSION.SQL_LOG_BIN = @MYSQLDUMP_TEMP_LOG_BIN;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:27:32
