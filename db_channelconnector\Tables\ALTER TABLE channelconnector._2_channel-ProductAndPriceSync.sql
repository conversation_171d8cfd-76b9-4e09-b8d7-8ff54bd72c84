ALTER TABLE `channelconnector`.`_2_channel` 
ADD COLUMN `stockUpdate` TINYINT(4) NULL AFTER `isEnabled`,
ADD COLUMN `priceUpdate` TINYINT(4) NULL AFTER `stockUpdate`,
ADD COLUMN `orderSync` TINYINT(4) NULL AFTER `priceUpdate`,
ADD COLUMN `productPublish` TINYINT(4) NULL AFTER `orderSync`;

SET SQL_SAFE_UPDATES = 0;
Update `channelconnector`.`_2_channel` 
SET stockUpdate = true,
orderSync = true,
priceUpdate = false,
productPublish = false;
