DROP PROCEDURE IF EXISTS channelconnector.getOrderByStatus;

DE<PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE channelconnector.getOrderByStatus
(
	IN `in_fby_user_id` VARCHAR(128)
)
BEGIN
	/*
		call channelconnector.getOrderByStatus(27);
    */
	SELECT DISTINCTROW om.* , ch.orderSyncStartDate
    FROM order_masters as om
    inner join order_details as od
    on od.fby_user_id = om.fby_user_id
    and od.order_no = om.order_no
    INNER JOIN _2_channel as ch
    on ch.channelId = om.fby_user_id 
    and ch.channelId = in_fby_user_id
    WHERE 
		(recipient_name IS NOT NULL AND  recipient_name <> '')
        /*
        AND payment_date IS NOT NULL
        */
		AND om.fby_user_id = in_fby_user_id 
        AND om.fby_send_status = 0
        AND (od.sku is not null and od.sku <> '')
        AND om.purchase_date >= ch.orderSyncStartDate
	Order by om.fby_user_id, om.order_no;
END$$
DELIMITER ;
