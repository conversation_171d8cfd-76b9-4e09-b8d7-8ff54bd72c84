// controllers/warehouseController.js
const warehouseService = require('../../../services/hcl/warehouseService');
const { createWarehouseAddressSchema, updateWarehouseAddressSchema } = require('../hcl/validators/warehouseValidator');

//server / controller / hcl / orderController.js
const warehouseController = {
    // Create a new warehouse address
    async createWarehouseAddress(req, res) {
        try {
            // Validate the request body against the Joi schema
            const { error, value } = createWarehouseAddressSchema.validate(req.body);
            if (error) {
                return res.status(400).json({ success: false, message: 'Validation error', error: error.details });
            }

            // Call the service method to create a warehouse address with the validated data
            let result = await warehouseService.createWarehouseAddress(value, req.user);  // Assuming req.user is populated after authentication

            // Return a successful response
            res.status(201).json({
                success: true,
                message: 'Address created successfully',
                data: result
            });
        } catch (error) {
            // Return error response
            res.status(500).json({
                success: false,
                message: 'Error creating address',
                error: error.message
            });
        }
    },

    async getWarehouseAddressById(req, res) {
        try {
            const addressId = req.params.id;

            let result = await warehouseService.getWarehouseAddressById(addressId);

            if (!result || result.length === 0) {
                return res.status(404).json({ success:false, error: 'Address not found' });
            }

            res.status(200).json({ success:true, data: result });
        } catch (error) {
            res.status(500).json({ success:false, message: 'Error fetching address', error: error.message });
        }
    },

    async getAllWarehouseAddressesWithPaging(req, res) {
        try {
            const {
                limit = 10,
                page = 1,
                addressDetailType,
                consigneeFirstName,
                consigneeLastName,
                city,
                state,
                zip,
                country,
                warehouseId,
                consigneeContact,
                id,
                addressType
            } = req.query;
            userData = req.user;


            const offset = (page - 1) * limit;

            let result = await warehouseService.getAllWarehouseAddressesWithPaging({
                limit: parseInt(limit),
                offset: parseInt(offset),
                addressDetailType,
                consigneeFirstName,
                consigneeLastName,
                city,
                state,
                zip,
                country,
                warehouseId: id? parseInt(id) : warehouseId ? parseInt(warehouseId) : null,
                consigneeContact,
                addressType,
                userData
            });

            // Return addresses and pagination metadata
            res.status(200).json({
                success: true,
                data: result.addresses,
                totalRecords: result.totalRecords,
                currentPage: parseInt(page),
                totalPages: Math.ceil(result.totalRecords / limit)
            });
        } catch (error) {
            res.status(500).json({ success: false, message: 'Error fetching addresses', error: error.message });
        }
    },

    // Update warehouse address by ID
    async updateWarehouseAddress(req, res) {
        try {
            const addressId = req.params.warehouseId;

            // Validate the request body against the Joi schema
            const { error, value } = updateWarehouseAddressSchema.validate(req.body);
            if (error) {
                return res.status(400).json({ success: false, message: 'Validation error', error: error.details });
            }

            let result = await warehouseService.updateWarehouseAddress(addressId, req.body, req.user);

            res.status(200).json({
                success: true,
                message: 'Address updated successfully',
                data: result
            });
        } catch (error) {
            res.status(500).json({success: false, message: 'Error updating address', error: error.message });
        }
    }
};

module.exports = warehouseController;
