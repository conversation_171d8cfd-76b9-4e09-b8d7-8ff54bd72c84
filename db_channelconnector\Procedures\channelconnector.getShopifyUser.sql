DROP PROCEDURE channelconnector.getShopifyUser;

DELIMITER $$
CREATE PROCEDURE channelconnector.`getShopifyUser`(
	IN `in_channelId` VARCHAR(1024)
)
BEGIN
	/*
    
		call channelconnector.`getShopifyUser`(1111);
        
		call channelconnector.`getShopifyUser`(25);	
        
        call channelconnector.`getShopifyUser`(1010);
    */
    DECLARE var_orderSyncStartDate datetime;
    DECLARE var_last_orderSyncDateTime datetime;
    DECLARE var_new_orderSyncDateTime datetime;
    DECLARE var_last_ProductSyncDateTime datetime;
    DECLARE var_channel_name varchar(200);

	SET var_channel_name  = (
		select LOWER(ch.channelName) from _2_channel as ch 
		Where  ch.channelId = in_channelId
		AND ch.isActive = 1 
		limit 1
	);

    
    SET var_orderSyncStartDate = (
			SELECT 
				T1.`orderSyncStartDate`
			FROM
				`channelconnector`.`_2_channel` AS T1
			WHERE
				T1.`channelId` = in_channelId
					AND T1.`isActive` = 1
					AND T1.`isEnabled` = 1
			LIMIT 1
	);
    
    SET var_last_orderSyncDateTime = (
			SELECT 
				#CASE WHEN MAX(T1.`updated_at`) > MAX(T1.`created_at`) THEN MAX(T1.`updated_at`)
                #ELSE 
                MAX(T1.`created_at`) 
                #END 
			FROM
				`channelconnector`.`order_details` AS T1
			WHERE
				T1.fby_user_id = in_channelId
            LIMIT 1
				
	);
    
    set var_last_ProductSyncDateTime = (
			SELECT 
				#CASE WHEN MAX(T1.`updated_at`) > MAX(T1.`created_at`) THEN MAX(T1.`updated_at`)
                #ELSE 
                date_add(MAX(T1.`created_at`) , INTERVAL -2 day) 
                #END 
			FROM
				`channelconnector`.`products` AS T1
			WHERE
				T1.fby_user_id = in_channelId
            LIMIT 1
				
	);
    
    IF(var_last_orderSyncDateTime IS NOT NULL)
    THEN
		/*
		IF(var_channel_name like '%woocom%') THEN
			SET var_new_orderSyncDateTime = var_orderSyncStartDate;
        ELSE
			SET var_new_orderSyncDateTime = date_add(var_last_orderSyncDateTime, INTERVAL -1 day) ;
        END IF;
        */
        IF(date_add(var_last_orderSyncDateTime, INTERVAL -2 day) > var_orderSyncStartDate)
        THEN
			SET var_new_orderSyncDateTime = date_add(var_last_orderSyncDateTime, INTERVAL -2 day) ;
        ELSE
			SET var_new_orderSyncDateTime = var_orderSyncStartDate;
        END IF;
        
    ELSE
		SET var_new_orderSyncDateTime = var_orderSyncStartDate;
    END IF;
        
	SELECT 
			T1.`id` ,
			T1.`channelId` as `fby_user_id`,
			T1.`groupCode` as group_code,
			T1.`currencyCode` as currency_code,
			T1.`ownerCode` as owner_code,
			T1.`channelCode`  as channel_code,
			T1.`channelName` ,
			T1.`domain`  ,
			T1.`username` ,
			T1.`password`  as `api_password`,
            T1.`siteId`  ,
			T1.`compatibilityLevel`  , 
			T1.`ebay_devid`  , 
			T1.`ebay_appid`  , 
			T1.`ebay_certid`  , 
			T1.`ebay_quantity_update_by`  , 
			T1.`apiKey`  as `api_key`,
			T1.`secret` ,
			T1.`token`  ,
			T1.`isActive`  ,
			T1.`isEnabled` ,
            T1.stockUpdate,
			T1.priceUpdate,
			T1.orderSync,
			T1.productPublish,
			CAST(T1.`createdOn` as CHAR) as created_at,
			CAST(T1.`modifiedOn` as CHAR) as updated_at,
            CAST(var_new_orderSyncDateTime as CHAR) as orderSyncStartDate,
            CAST(var_last_ProductSyncDateTime as CHAR) as productSyncDateTime
    	
	FROM `channelconnector`.`_2_channel` as T1
	WHERE 
		T1.`channelId` = in_channelId
        AND T1.`isActive` = 1
        AND T1.`isEnabled` = 1
        #AND 1 = case when T1.`orderSyncStartDate` is null || (T1.`orderSyncStartDate` IS NOT NULL AND T1.`orderSyncStartDate` < NOW()) then 1 else 0 end
        # AND T1.`orderSyncStartDate` IS NOT NULL AND T1.`orderSyncStartDate` < NOW()
        LIMIT 1 ;
        
END$$
DELIMITER ;
