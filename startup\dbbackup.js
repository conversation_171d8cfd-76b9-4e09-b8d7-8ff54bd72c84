const mysql = require('mysql2/promise');
const env = require("./env");
const retry = require('async-retry');
const ccDB = process.env.DB_DATABASE || "channelconnector";
const hclDB = process.env.INITIAL_CATALOG || "hcl";

// Create a connection pool for the users database
let ccDbParams = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: ccDB,
    waitForConnections: true,
    connectionLimit: 50,
    queueLimit: 0,
    connectTimeout: 50000, // 10 seconds
};

  // Create a connection pool for the work database
let hclDbParams = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: hclDB,
    waitForConnections: true,
    connectionLimit: 50,
    queueLimit: 0,
    connectTimeout: 50000, // 10 seconds
  };

let ccDbPool = mysql.createPool(ccDbParams);
let hclDbPool = mysql.createPool(hclDbParams);

const resetDbPool = async (db) => {
    let pool = hclDbPool;
    let params = hclDbParams;
    
    if(db == ccDB) {
        pool = ccDbPool;
        params = ccDbParams;
    }

    try {
        if (pool) {
            console.log("Checking pool health...");
            const [results] = await pool.query("SELECT 1");
            if (!results) throw new Error("Invalid pool state.");
        }
    } catch (error) {
        console.error("Resetting database pool due to error:", error.message);
        try {
            if (pool) await pool.end(); // Close the existing pool
        } catch (endError) {
            console.error("Error closing pool:", endError.message);
        }
       // pool = mysql.createPool(params); // Recreate the pool
       if(db == ccDB) {
            ccDbPool = mysql.createPool(ccDbParams);
       } else {
            hclDbPool = mysql.createPool(hclDbParams);
       }
       
        console.log("Database pool reset successfully.");
    }
};

const shouldResetPool = (errorMessage) => {
    return (
        errorMessage.includes('read etimedout') ||
        errorMessage.includes('add new command when connection is in closed state') ||
        errorMessage.includes('cannot enqueue query after fatal error') ||
        errorMessage.includes('the server closed the connection') ||
        errorMessage.includes('pool is closed') ||
        errorMessage.includes('packets out of order') ||
        errorMessage.includes('read econnreset')
    );
};

const executeProcedure = async (procedureName, paramArray, db = hclDB ) => {
    let connection;
    let pool = hclDbPool;  
    if(db == ccDB) {
        pool = ccDbPool;
    }   

    try {
        connection = await pool.getConnection();
        return await retry(async () => {
            const sql = `CALL ${procedureName}(${paramArray.map(() => '?').join(', ')})`;
            const [result] = await connection.query(sql, paramArray);
            return result;
        }, { retries: 0, minTimeout: 2000, maxTimeout: 5000 });
    } catch (error) {
       // console.error(`Error executing procedure '${procedureName}':`, error);
        // if (shouldResetPool(error.message.toLowerCase())) {
        //     await resetDbPool(db);
        // }
        throw error;
    } finally {
        try{
            if (connection) connection.release(); // Ensure the connection is released
            connection.close();
        } catch(error) {
            console.log(error);
        }    
    }
};

const execute = async (sqlString, paramArray, callback, sendRaw = true) => {
    let connection;
    let pool = ccDbPool;
    try {
        connection = await pool.getConnection();
        return await retry(async () => {
            const [result] = await connection.execute(sqlString, paramArray, { timeout: 30000 });
            if (sendRaw) {
                if (callback) return callback(null, result);
                return { result: result[0], variables: result[0][0] || null };
            }
            return {
                result: result[0] || [],
                variables: result[0]?.[0] || null,
            };
        }, { retries: 0, minTimeout: 2000, maxTimeout: 5000 });
    } catch (error) {
        console.error("Error executing query:", error.message);
        // if (shouldResetPool(error.message.toLowerCase())) {
        //     await resetDbPool(db);
        // }
        throw error;
    } finally {
        try{
            if (connection) connection.release(); // Ensure the connection is released
            connection.close();
        } catch(error) {
            console.log(error);
        }    
    }
};

const executeHclDb = async (sqlString, paramArray, callback, sendRaw = true) => {
    let connection;
    let pool = hclDbPool;
    try {
        connection = await pool.getConnection();
        return await retry(async () => {
            const [result] = await connection.execute(sqlString, paramArray, { timeout: 30000 });
            if (sendRaw) {
                if (callback) return callback(null, result);
                return { result: result[0], variables: result[0][0] || null };
            }
            return {
                result: result[0] || [],
                variables: result[0]?.[0] || null,
            };
        }, { retries: 0, minTimeout: 2000, maxTimeout: 5000 });
    } catch (error) {
        console.error("Error executing query:", error.message);
        // if (shouldResetPool(error.message.toLowerCase())) {
        //     await resetDbPool(db);
        // }
        throw error;
    } finally {
        try{
            if (connection) connection.release(); // Ensure the connection is released
            connection.close();
        } catch(error) {
            console.log(error);
        }    
    }
};

const bulkInsert = async (table, paramArray, db = hclDB) => {
    if (!Array.isArray(paramArray) || paramArray.length === 0) return false;

    let connection;
    let pool = hclDbPool;
    if(db == ccDB) {
        pool = ccDbPool;
    }

    try {
        connection = await pool.getConnection();
        const keys = Object.keys(paramArray[0]);
        const values = paramArray.map(obj => keys.map(key => obj[key]));
        const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES ?`;
        await connection.query(sql, [values]);
        return true;
    } catch (error) {
        console.error(`Bulk insert error for table '${table}':`, error.message);
        // if (shouldResetPool(error.message.toLowerCase())) {
        //     await resetDbPool(db);
        // }
        return false;
    } finally {
        try{
            if (connection) connection.release(); // Ensure the connection is released
            connection.close();
        } catch(error) {
            console.log(error);
        }    
    }
};

const killSleepingSqlProcess = async ( db = hclDB ) => {
    let connection;
    let pool = hclDbPool;    
    if(db == ccDB) {
        pool = ccDbPool;
    }
    try {
        connection = await pool.getConnection();
        const user = env.DB_USER.split('@', 1)[0];
        const sql = `SELECT CONCAT('KILL ', id, ';') AS sqlCmd 
                     FROM information_schema.processlist 
                     WHERE Command='Sleep' AND USER = '${user}' AND TIME_MS > 120000`;
        const [result] = await connection.query(sql);
        for (const { sqlCmd } of result) {
            if (sqlCmd) {
                await connection.execute(sqlCmd);
            }
        }
    } catch (error) {
        console.error("Error killing sleeping SQL processes:", error.message);
    } finally {
        try{
            if (connection) connection.release(); // Ensure the connection is released
            connection.close();
        } catch(error) {
            console.log(error);
        }    
    }
};

module.exports = {
    resetDbPool,
    executeProcedure,
    execute,
    executeHclDb,
    bulkInsert,
    killSleepingSqlProcess,
};
