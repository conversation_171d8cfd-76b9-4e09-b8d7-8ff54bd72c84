DROP PROCEDURE IF EXISTS channelconnector.updateProductStatus;

DELIMITER $$
CREATE PROCEDURE channelconnector.updateProductStatus(

		`in_fby_id` VARCHAR(128), 
		`in_sku` VARCHAR(128), 
		`in_crn_name` VA<PERSON><PERSON><PERSON>(60), 
		`in_crnid` VARCHAR(100), 
		`in_time` DATETIM<PERSON>,
		`in_barcode` VA<PERSON><PERSON><PERSON>(128),
		`in_item_id` VARCHAR(128),
		`in_item_product_id` VARCHAR(128),
		`in_inventory_item_id` VARCHAR(128)
)
BEGIN
	UPDATE products 
	SET 
		status = 1,
		count = 0,
		fby_error_flag = 0,
		cron_name = in_crn_name,
		updated_at = in_time,
		cron_id = in_crnid
	WHERE
		fby_user_id = in_fby_id 
		AND sku = in_sku
		AND 1 = case when `in_barcode` <> '' AND `in_barcode` <> '0' then case when barcode = `in_barcode` then 1 else 0 end else 1 end 
		AND 1 = case when `in_item_id` <> ''  AND `in_item_id` <> '0' then case when item_id = `in_item_id` then 1 else 0 end else 1 end 
		AND 1 = case when `in_item_product_id` <> ''  AND `in_item_product_id` <> '0' then case when item_product_id = `in_item_product_id` then 1 else 0 end else 1 end 
		AND 1 = case when `in_inventory_item_id` <> ''  AND `in_inventory_item_id` <> '0' then case when inventory_item_id = `in_inventory_item_id` then 1 else 0 end else 1 end 
		AND status = 0;
END$$
DELIMITER ;