const dbpool = require('../../startup/db');
const bcrypt = require('bcrypt');
const helpers = require('../../misc/helpers.js');
const logger = require('../../misc/logger.js');
const cache = require('../../misc/cache.js');
const miscConstants = require("../../misc/constants.js");
const common = require("../../server/constants/common.js");
const hcldb = process.env.INITIAL_CATALOG || "hcl";

const registerClient = async (clientData, createdBy, organizationId) => {
  try {
      const sql = `${hcldb}.RegisterClient`;

      const {
          firstName,
          lastName,
          email,
          mobile,
          company,
          monthlyOrders,
          businessType
      } = clientData;

      const [clientResult] = await dbpool.executeProcedure(sql, [
          organizationId,
          firstName,
          lastName,
          email,
          mobile,
          company,
          monthlyOrders,
          businessType,
          createdBy
      ]);

       // Handle stored procedure response
    if (!clientResult || !clientResult[0]) {
      throw new Error('Registration failed - no response from database');
    }

    const result = clientResult[0];
    
    // Check for duplicate registration cases (clientId = 0)
    if (result.clientId === 0) {
      // Return the direct error message from the stored procedure
      return { 
        success: false,
        message: result.message,
        clientId: 0
      };
    }

    // Validate successful registration
    if (!result.clientId || isNaN(result.clientId)) {
      throw new Error('Registration failed - invalid client ID returned');
    }

    const clientId = result.clientId;

    const groupCode = process.env.USERS_GROUP_CODE || 8;
    const roleId = process.env.CLIENT_ADMIN_ROLE_ID || 5; 

    const userData = {
        name: `${firstName} ${lastName}`,
        email,
        mobile,
        groupCode,
        clientId, 
        organizationId,
        roleId,   
        createdBy
    };

    // Common signup to register the user
    const signupResult = await common.signup(userData);
    if (signupResult?.error) {
      // Rollback client registration if user creation fails?
      throw new Error(`User registration failed: ${signupResult.error.data || signupResult.error}`);
    }

    // Clear cache after successful 
    const cacheKey = `miscConstants.CACHE.CLIENTS.KEY.${organizationId}`;
    await cache.delete(cacheKey);

    return {
      success: true,
      message: result.message || 'Client registered successfully',
      clientId,
      ...(signupResult ? { user: signupResult } : {})
    };

  } catch (error) {

    console.error('Error in registerClient:', error);
    logger.logError('Error in registerClient:', error);
    
    // Format error response consistently
    throw new Error(error.message || 'Client registration failed');

  }
};

const getAllClients = async (page, pageSize, organizationId = null, useCache = 1) => {
  try {
    const cacheKey = `miscConstants.CACHE.CLIENTS.KEY.${organizationId}`;
    const cacheTTL = miscConstants.CACHE.CLIENTS.TTL;
    if(pageSize === 0 && useCache === 1) {
      const cachedData = cache.get(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    } 
    // Call the stored procedure
    const sql = `${hcldb}.GetAllClients`;
    const result = await dbpool.executeProcedure(sql, [organizationId, page, pageSize]);
    // Extract the total records and client list
    const totalRecords = result[0][0]?.totalRecords || 0; 
    const clients = result[1]; 
    const clientsData = { clients, totalRecords };

    if(pageSize === 0) {
      cache.set(cacheKey, clientsData, cacheTTL);
    }  
    return clientsData;
  } catch (error) {
    logger.logError(`Error in get All Client organizationId: ${organizationId}`, error);
    throw error;
  }
};

const editClient = async (
    id,
    firstName,
    lastName,
    email,
    mobile,
    company,
    monthlyOrders,
    businessType,
    isActive,
    isWallet,
    isB2BAccess,
    isTat,
    updatedBy,
    organizationId
  ) => {
    try {
   
      if (!id || !email || !mobile) {
        throw new Error('Missing required fields');
      }

      const sql = `${hcldb}.EditClient`;
      
      const result = await dbpool.executeProcedure(sql, [
        id,
        firstName,
        lastName,
        email,
        mobile,
        company,
        monthlyOrders,
        businessType,
        isActive,
        isWallet,
        isB2BAccess,
        isTat,
        updatedBy
      ]);

      // Clear cache after successful update
      const cacheKey = `miscConstants.CACHE.CLIENTS.KEY.${organizationId}`;
      await cache.delete(cacheKey);

      return result[0];
    } catch (error) {
      console.error('Error in editClient: ', error);
      logger.logError('Error in editClient: ', error);
      throw error;
    }
  };

  const getClientById = async (clientId) => {
    try {
        const sql = `${hcldb}.GetClientById`;
        const rows = await dbpool.executeProcedure(sql, [clientId]);
        return rows[0]; // Return the first result set (client details)
    } catch (error) {
      logger.logError(`Error in get Get Client, ClientId: ${clientId}`, error);
      throw error;
    }
  };

  const deleteClientById = async (clientId, userData) => {
    try {
        const sql = `${hcldb}.SoftDeleteClientById`;
        const results = await dbpool.executeProcedure(sql, [clientId, userData.id]);
        if (results && results[0] && results[0].length > 0) {
          const { clientId, message } = results[0][0]; 

          // Clear cache after successful update
          const cacheKey = `miscConstants.CACHE.CLIENTS.KEY.${organizationId}`;
          await cache.delete(cacheKey);

          return { clientId: clientId, message };
        } else {
            throw new Error('No valid response from the database.');
        }
    } catch (error) {
      logger.logError(`Error in clientService.deleteClientById, ClientId: ${clientId}`, error);
      console.error('Error in clientService.deleteClientById:', error.message);
      throw error;
    }
  };
  
// Add or Update Billing Info
const upsertBillingInfo = async (params, userData) => {
    try {
        const sql = `${hcldb}.UpsertClientBillingInfo`;
        const results = await dbpool.executeProcedure(sql, [
            params.id || null, // Pass null for new entries
            params.clientId,
            params.billingDate,
            params.codGap || null,
            params.codDays || null,
            userData.id,
        ]);
        return results[0][0];
    } catch (error) {
        logger.logError('Error in clientService.upsertBillingInfo', error);
        throw error;
    }
};

const getBillingInfo = async (clientId) => {
    try {
        const sql = `${hcldb}.GetClientBillingInfo`;
        const results = await dbpool.executeProcedure(sql, [clientId]);
        return results[0][0];
    } catch (error) {
        logger.logError(`Error in clientService.getBillingInfo Client Id: ${clientId}`, error);
        throw error;
    }    
  };

module.exports = { 
    registerClient, 
    getAllClients, 
    editClient, 
    getClientById, 
    deleteClientById, 
    upsertBillingInfo, 
    getBillingInfo 
};
