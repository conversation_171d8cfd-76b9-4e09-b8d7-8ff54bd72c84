drop procedure if exists channelconnector.getOrderByAccount;

DE<PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE channelconnector.getOrderByAccount(
	IN `in_fby_user_id` VARCHAR(128), 
	IN `in_account_id` VARCHAR(256) 
	-- IN `in_channel_code` VARCHAR(20)
)
BEGIN
/*,
call channelconnector.getOrderByAccount(8,17);
*/
SET SQL_SAFE_UPDATES = 0;
	SELECT * FROM order_masters AS OM 
	WHERE 
		OM.fby_user_id = in_fby_user_id; 
		-- AND OM.account_id = in_account_id 
		-- AND OM.channel_code = in_channel_code;
		
SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;
