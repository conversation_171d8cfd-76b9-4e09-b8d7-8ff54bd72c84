const mysql = require('mysql2/promise');
const os = require('os');
const env = require("./env");
const retry = require('async-retry');
const ccDB = process.env.DB_DATABASE || "channelconnector";
const hclDB = process.env.INITIAL_CATALOG || "hcl";

class DatabaseConnectionManager {
    constructor() {
        this.pools = new Map();
    }

    /**
     * 
     * @param {*} database 
     * @returns 
     */
    getPoolConfig(database = hclDB) {
        // Calculate optimal pool size
        // Formula: connections = number_of_cpu_cores * (1 + avg_async_io_factor)
        const cpuCount = os.cpus().length;
        const asyncIOFactor = 2; // Multiplier for IO-bound operations
        
        // Base connection calculation
        const baseConnections = cpuCount * (1 + asyncIOFactor);
        
        // Account for long-running queries
        // If queries take 30 seconds, we need more connections to handle concurrent requests
        const avgQueryTime = 30; // seconds
        const targetThroughput = 100; // concurrent users
        const queryPerConnection = Math.ceil(60 / avgQueryTime); // queries per minute per connection
        
        // Calculate minimum connections needed
        const minConnections = Math.ceil(targetThroughput / queryPerConnection);
        
        // Use the larger of the two calculations
        const connectionLimit = Math.max(baseConnections, minConnections);
        return {
            host: process.env.DB_HOST,
            user: process.env.DB_USER,
            password: process.env.DB_PASSWORD,
            database: database,
            connectionLimit: connectionLimit,
            maxIdle: Math.floor(connectionLimit * 0.5),
            idleTimeout: 60000,
            queueLimit: 0,
            enableKeepAlive: true,
            keepAliveInitialDelay: 0,
            connectTimeout: 50000,
            waitForConnections: true,
           // queryTimeout: 30000,
           // acquireTimeout: 10000,
            namedPlaceholders: true,
            dateStrings: true,
            // debug: process.env.NODE_ENV !== 'PROD',
            // poolMonitoring: {
            //      frequency: 5000
            // }
        };
    }
   
    /**
     * 
     * @param {*} database 
     * @returns 
     */
    async initializePool(database = hclDB) {
        if (this.pools.has(database)) {
            return this.pools.get(database);
        }

        try {
            const pool = mysql.createPool(this.getPoolConfig(database));

            // Add pool error handler
            pool.on('error', (err) => {
                console.error('Unexpected error on idle client', err);
                process.exit(-1);
            });
            // Add connection monitoring
            pool.on('acquire', () => {
                console.debug('Connection acquired');
            });

            // Monitor pool statistics
            // setInterval(() => {
            //     console.info('Pool statistics:', {
            //         threadId: pool.threadId,
            //         activeConnections: pool.activeConnections(),
            //         idleConnections: pool.idleConnections(),
            //         totalConnections: pool.totalConnections(),
            //         waitingRequests: pool.waitingRequests()
            //     });
            // }, 30000);

            this.pools.set(database, pool);
            return pool;
        } catch (error) {
            console.error('Error initializing pool:', error);
            throw error;
        }
    }

    /**
     * 
     * @param {*} procedureName 
     * @param {*} paramArray 
     * @param {*} database 
     * @returns 
     */
    async executeProcedure(procedureName, paramArray, database = hclDB) {
        let connection;
        try {
            const pool = await this.initializePool(database);
            connection = await pool.getConnection();

            const sql = `CALL ${procedureName}(${paramArray.map(() => '?').join(', ')})`;
            const [result] = await connection.query(sql, paramArray);
            return result;
        } catch (error) {
            console.error(`Error executing procedure '${procedureName}':`, error);
            throw error;
        } finally {
            if (connection) {
                try {
                    await connection.release();
                } catch (error) {
                    console.error('Error releasing connection:', error);
                }
            }
        }
    }

    /**
     * 
     * @param {*} sqlString 
     * @param {*} paramArray 
     * @param {*} callback 
     * @param {*} sendRaw 
     * @returns 
     */
    async execute(sqlString, paramArray, callback, sendRaw = true) {
        let connection;
        try {
            const pool = await this.initializePool(ccDB);
            connection = await pool.getConnection();

            return await retry(async () => {
                const [result] = await connection.execute(sqlString, paramArray, { timeout: 30000 });
                if (sendRaw) {
                    if (callback) return callback(null, result);
                    return { result: result[0], variables: result[0][0] || null };
                }
                return {
                    result: result[0] || [],
                    variables: result[0]?.[0] || null,
                };
            }, { retries: 0, minTimeout: 2000, maxTimeout: 5000 });
        } catch (error) {
            console.error("Error executing query:", error.message);
            // if (shouldResetPool(error.message.toLowerCase())) {
            //     await resetDbPool(db);
            // }
            throw error;
        } finally {
            if (connection) {
                try {
                    await connection.release();
                } catch (error) {
                    console.error('Error releasing connection:', error);
                }
            }
        }
    }
    
    /**
     * 
     * @param {*} sqlString 
     * @param {*} paramArray 
     * @param {*} callback 
     * @param {*} sendRaw 
     * @returns 
     */
    async executeHclDb(sqlString, paramArray, callback, sendRaw = true) {
        let connection;
        try {  
            const pool = await this.initializePool(hclDB);
            connection = await pool.getConnection();

            return await retry(async () => {
                const [result] = await connection.execute(sqlString, paramArray, { timeout: 30000 });
                if (sendRaw) {
                    if (callback) return callback(null, result);
                    return { result: result[0], variables: result[0][0] || null };
                }
                return {
                    result: result[0] || [],
                    variables: result[0]?.[0] || null,
                };
            }, { retries: 0, minTimeout: 2000, maxTimeout: 5000 });
        } catch (error) {
            console.error("Error executing query:", error.message);
            // if (shouldResetPool(error.message.toLowerCase())) {
            //     await resetDbPool(db);
            // }
            throw error;
        } finally {
            if (connection) {
                try {
                    await connection.release();
                } catch (error) {
                    console.error('Error releasing connection:', error);
                }
            }
        }
    }

    /**
     * 
     * @param {*} table 
     * @param {*} paramArray 
     * @param {*} database 
     * @returns 
     */
    async bulkInsert(table, paramArray, database = hclDB) {
        if (!Array.isArray(paramArray) || paramArray.length === 0) return false;
    
        let connection;
        try {  
            const pool = await this.initializePool(database);
            connection = await pool.getConnection();
            
            const keys = Object.keys(paramArray[0]);
            const values = paramArray.map(obj => keys.map(key => obj[key]));
            const sql = `INSERT INTO ${table} (${keys.join(', ')}) VALUES ?`;
            await connection.query(sql, [values]);
            return true;
        } catch (error) {
            console.error(`Bulk insert error for table '${table}':`, error.message);
            return false;
        } finally {
            if (connection) {
                try {
                    await connection.release();
                } catch (error) {
                    console.error('Error releasing connection:', error);
                }
            }
        }
    }
    
    /**
     * 
     * @param {*} database 
     */
    async killSleepingSqlProcess( database = hclDB ) {
        let connection;
        try {  
            const pool = await this.initializePool(database);
            connection = await pool.getConnection();

            const user = env.DB_USER.split('@', 1)[0];
            const sql = `SELECT CONCAT('KILL ', id, ';') AS sqlCmd 
                         FROM information_schema.processlist 
                         WHERE Command='Sleep' AND USER = '${user}' AND TIME_MS > 120000`;
            console.log(sql);
            const [result] = await connection.query(sql);
            for (const { sqlCmd } of result) {
                if (sqlCmd) {
                    await connection.execute(sqlCmd);
                }
            }
        } catch (error) {
            console.error("Error killing sleeping SQL processes:", error.message);
        } finally {
            if (connection) {
                try {
                    await connection.release();
                } catch (error) {
                    console.error('Error releasing connection:', error);
                }
            }
        }
    };

    /**
     * 
     */
    async closeAllPools() {
        for (const [database, pool] of this.pools.entries()) {
            try {
                await pool.end();
                this.pools.delete(database);
            } catch (error) {
                console.error(`Error closing pool for database ${database}:`, error);
            }
        }
    }

    async getConnection( database = hclDB ) {
        let connection;
        try {  
            const pool = await this.initializePool(database);
            connection = await pool.getConnection();
            return connection;
        } catch (error) {
            console.error("Error pool.getConnection():", error.message);
        }  
    };
    
}

// Create and export a single instance
const dbpool = new DatabaseConnectionManager();
module.exports = dbpool;


