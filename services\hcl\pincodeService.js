const dbpool = require('../../startup/db');
const helpers = require('../../misc/helpers.js');
const cache = require('../../misc/cache.js');
const miscConstants = require("../../misc/constants.js");
const ShippingProvideService = require('./shippingProviderService.js');
const ZoneService = require('./zoneService.js');
const StatusService = require('./statusService.js');
const hcldb = process.env.INITIAL_CATALOG || "hcl";

class PincodeService {

    constructor(cache, dbpool, hcldb) {
        this.cache = cache;
        this.dbpool = dbpool;
        this.config = {
          cacheTTL: miscConstants.CACHE.PINCODE.TTL || 24 * 60 * 60, // 24 hours in seconds
          cacheKeyPrefix: miscConstants.CACHE.PINCODE.KEY || 'PINCODE:',
          database: hcldb || 'hcldb',
          validModes: ['air', 'surface', 'dp', 'rail'],
        };
    }

    /**
     * Generate cache keys for a pincode entry
     * @private
    */
    _generateCacheKeys(pincode, providerId) {
        const prefix = this.config.cacheKeyPrefix;
        return {
            provider: `${prefix}${providerId}:${pincode}`,
            pincode: `${prefix}${pincode}:${providerId}`,
            providerAll: `${prefix}${providerId}:all`,
            count: `${prefix}${providerId}:count`
        };
    }

    /**
     * Add or update pincode in both DB and cache
     * @param {*} pincodeData 
     * @param {*} createdBy 
     * @returns 
     */
    async addPincode (pincodeData, createdBy) {
        try {
            const sql = `${hcldb}.AddOrUpdatePincode`;
            const {
                pincode,
                shippingProviderId,
                city,
                state,
                isActive,
                air,
                surface,
                rail,
                dp,
                oda,
                pickup,
                prepaid,
                reverse,
                cod,
                zone
            } = pincodeData;

            const results = await dbpool.executeProcedure(sql, [
                pincode,
                shippingProviderId,
                city,
                state,
                isActive,
                createdBy,
                air,
                surface,
                rail,
                dp,
                oda,
                pickup,
                prepaid,
                reverse,
                cod,
                zone
            ]);

            // Update cache
            const cacheKeys = this._generateCacheKeys(pincode, shippingProviderId);
            const resultData = results[0][0];
          
            await Promise.all([
                this.cache.set(cacheKeys.provider, resultData, this.config.cacheTTL),
                this.cache.set(cacheKeys.pincode, resultData, this.config.cacheTTL)
              ]);
            return resultData;

        } catch (error) {
            console.error('Error in addPincode:', error);
            throw error;
        }
    }

   /**
 * Initialize or refresh cache with all pincodes and organize by multiple access patterns
 */
    async initializePincodeCache() {
        try {
            const sql = `${this.config.database}.GetAllShippingPincodes`;
            const [pincodes, totalCount] = await this.dbpool.executeProcedure(sql, []);
            
            // Initialize data structures for different access patterns
            const providerGroups = new Map();  // Group by provider
            const pincodeGroups = new Map();   // Group by pincode
            const cacheOperations = [];
            
            // Process each pincode entry
            pincodes.forEach(pincode => {
                // 1. Provider-specific grouping
                if (!providerGroups.has(pincode.shippingProviderId)) {
                    providerGroups.set(pincode.shippingProviderId, []);
                }
                providerGroups.get(pincode.shippingProviderId).push(pincode);
                
                // 2. Pincode-specific grouping (multiple providers per pincode)
                if (!pincodeGroups.has(pincode.pincode)) {
                    pincodeGroups.set(pincode.pincode, []);
                }
                pincodeGroups.get(pincode.pincode).push(pincode);
                
                // 3. Individual provider-pincode combinations
                const cacheKeys = this._generateCacheKeys(pincode.pincode, pincode.shippingProviderId);
                cacheOperations.push(
                    this.cache.set(cacheKeys.provider, pincode, this.config.cacheTTL),
                    this.cache.set(cacheKeys.pincode, pincode, this.config.cacheTTL)
                );
            });
            
            // Cache provider groups and their counts
            providerGroups.forEach((providerPincodes, providerId) => {
                cacheOperations.push(
                    // All pincodes for a specific provider
                    this.cache.set(
                        `${this.config.cacheKeyPrefix}${providerId}:all`,
                        providerPincodes,
                        this.config.cacheTTL
                    ),
                    // Count of pincodes for a specific provider
                    this.cache.set(
                        `${this.config.cacheKeyPrefix}${providerId}:count`,
                        providerPincodes.length,
                        this.config.cacheTTL
                    )
                );
            });
            
            // Cache pincode groups (all providers for each pincode)
            pincodeGroups.forEach((providers, pincode) => {
                cacheOperations.push(
                    this.cache.set(
                        `${this.config.cacheKeyPrefix}pincode:${pincode}`,
                        providers,
                        this.config.cacheTTL
                    )
                );
            });
            
            // Cache all pincodes together
            cacheOperations.push(
                // Store complete pincode list
                this.cache.set(
                    `${this.config.cacheKeyPrefix}all`,
                    Array.from(pincodes),
                    this.config.cacheTTL
                ),
                // Store total count
                this.cache.set(
                    `${this.config.cacheKeyPrefix}total_count`,
                    pincodes.length,
                    this.config.cacheTTL
                ),
                // Store unique pincode count
                this.cache.set(
                    `${this.config.cacheKeyPrefix}unique_pincode_count`,
                    pincodeGroups.size,
                    this.config.cacheTTL
                )
            );
            
            // Execute all cache operations
            await Promise.all(cacheOperations);
            
            console.log(`Cache initialized with:
                - Total entries: ${pincodes.length}
                - Unique pincodes: ${pincodeGroups.size}
                - Providers: ${providerGroups.size}`
            );
            
            return true;
        } catch (error) {
            console.error('Error initializing pincode cache:', error);
            throw error;
        }
    }

    /**
     * Get pincodes with pagination support
     */
    async getPincodes(providerId = null, page = 1, pageSize = 0) {
        try {
        
            const cacheKey = providerId 
                ? `${this.config.cacheKeyPrefix}${providerId}:all`
                : `${this.config.cacheKeyPrefix}all`;

            let pincodes = await this.cache.get(cacheKey);
            
            if (!pincodes) {
                await this.initializePincodeCache();
                pincodes = await this.cache.get(cacheKey);
            }

            if (!pincodes) {
                return { pincodes: [], totalRecords: 0 };
            }

            // Apply pagination if pageSize is specified
            const totalRecords = pincodes.length;
            if (pageSize > 0) {
                const start = (page - 1) * pageSize;
                pincodes = pincodes.slice(start, start + pageSize);
            }

            return { pincodes, totalRecords };
        } catch (error) {
            console.error('Error getting pincodes:', error);
            throw error;
        }
    }

    /**
     * Get details for a specific pincode by provider
     */
    async getPincodeDetails(pincode, providerId = 1) {
        try {
            const cacheKeys = this._generateCacheKeys(pincode, providerId);
            let pincodeData = await this.cache.get(cacheKeys.provider);
 
            if (!pincodeData) {
              const sql = `${this.config.database}.GetShippingPincodeDetails`;
              const [results] = await this.dbpool.executeProcedure(sql, [pincode, providerId]);
              //console.log(results);
              if (results?.[0]) {
                pincodeData = results[0];
                await this.cache.set(cacheKeys.provider, pincodeData, this.config.cacheTTL);
              }
            }
            return pincodeData || null;
        } catch (error) {
            console.error('Error getting pincode details:', error);
            throw error;
        }
    }

    /**
     * Get zone details for source and destination pincodes
     */
    async getZoneDetails(sourcePincode, destinationPincode, isCOD = false, providerId = 1 ) {
        try {
            const sourceData = await this.getPincodeDetails(sourcePincode, providerId);
            if (!sourceData || sourceData.pickup !== 1) {
                throw new Error(`Source pincode ${sourcePincode} is not serviceable for pickup.`);
            }

            const destinationData = await this.getPincodeDetails(destinationPincode, providerId);
            if (!destinationData) {
                throw new Error(`Destination pincode ${destinationPincode} is not serviceable.`);
            }

            if (!destinationData.zone) {
                throw new Error('Destination zone not defined');
            }

            return {
                sourcePincodeDetails: sourceData,
                destinationPincodeDetails: destinationData
            };
        } catch (error) {
            console.error('Error getting zone details:', error);
            throw error;
        }
    }

    /**
     * Get all providers for a specific pincode
     */
    async getAllProvidersForPincode(pincode) {
        try {
            const cacheKey = `${this.config.cacheKeyPrefix}pincode:${pincode}`;
            let providers = await this.cache.get(cacheKey);
            
            if (!providers) {
                await this.initializePincodeCache();
                providers = await this.cache.get(cacheKey);
            }
            
            return providers || [];
        } catch (error) {
            console.error('Error getting providers for pincode:', error);
            throw error;
        }
    }

    /**
    * Get pincode details with all available providers
    */
    async getPincodeDetailsWithProviders(pincode) {
        try {
            const providers = await this.getAllProvidersForPincode(pincode);
            
            if (!providers || providers.length === 0) {
                return {
                    pincode,
                    found: false,
                    message: `No provider details found for pincode ${pincode}`,
                    providers: []
                };
            }
            
            return {
                pincode,
                found: true,
                providerCount: providers.length,
                providers: providers.map(provider => ({
                    id: provider.id,
                    pincode: provider.pincode,
                    city: provider.city,
                    state: provider.state,
                    providerName: provider.shippingProviderName,
                    shippingProviderId: provider.shippingProviderId,
                    air: provider.air,
                    surface: provider.surface,
                    rail: provider.rail,
                    dp: provider.dp,
                    oda: provider.oda,
                    pickup: provider.pickup,
                    prepaid: provider.prepaid,
                    reverse: provider.reverse,
                    cod: provider.cod,
                    zone: provider.zone,
                    zoneName: provider.zoneName,
                    zoneCode: provider.zoneCode,
                    tat: provider.tat,
                    isActive: provider.isActive,
                }))
            };
        } catch (error) {
            console.error('Error getting pincode details with providers:', error);
            throw error;
        }
    }

    /**
     * Clear cache for a specific provider
     */
    async clearProviderCache(providerId) {
        try {
            const pattern = `${this.config.cacheKeyPrefix}${providerId}:*`;
            const keys = await cache.keys(pattern);
            if (keys.length > 0) {
                await this.cache.del(keys);
            }
            return keys.length;
        } catch (error) {
            console.error('Error clearing provider cache:', error);
            throw error;
        }
    }

    /**
    * Validate delivery mode
    * @private
    */
    _validateMode(mode) {
        if (!mode) return true;
        return this.config.validModes.includes(mode.toLowerCase());
    }

    /**
     * Format serviceability response
     * @private
     */
    async _formatServiceabilityResponse(pincodeInfo, type, mode) {
        if (!pincodeInfo) {
            return {
                serviceable: false,
                message: `Pincode is not serviceable.`,
                data: null
            };
        }

        const isPickupLocation = type === 'source';
        let serviceable = true;
        let failureReason = [];

        // Check mode-specific serviceability
        if (mode) {
            if (!pincodeInfo[mode]) {
                serviceable = false;
                failureReason.push(`${mode} delivery not available`);
            }
        }

        // Check pickup availability for source
        if (isPickupLocation && !pincodeInfo.pickup) {
            serviceable = false;
            failureReason.push('pickup not available');
        }

        return {
            serviceable,
            message: serviceable ? 
                `Pincode is serviceable${mode ? ` for ${mode}` : ''}.` : 
                `Pincode is not serviceable: ${failureReason.join(', ')}`,
            data: {
                pincode: pincodeInfo.pincode,
                city: pincodeInfo.city,
                state: pincodeInfo.state,
                air: pincodeInfo.air === 1,
                surface: pincodeInfo.surface === 1,
                rail: pincodeInfo.rail === 1,
                dp: pincodeInfo.dp === 1,
                pickup: pincodeInfo.pickup === 1,
                prepaid: pincodeInfo.prepaid === 1,
                cod: pincodeInfo.cod === 1,
                reverse: pincodeInfo.reverse === 1,
                oda: pincodeInfo.oda === 1,
                zone: pincodeInfo.zone,
                zoneName: pincodeInfo.zoneName,
                zoneCode: pincodeInfo.zoneCode,
                tat: pincodeInfo.tat,
                isActive: pincodeInfo.isActive,
            }
        };
    }

    /**
     * Check serviceability for a single pincode
     */
    async checkServiceability(providerId, pincode, type, mode = '') {
        try {
            // Validate mode if provided
            if (mode && !this._validateMode(mode)) {
                throw new Error(`Invalid mode. Allowed values: ${this.config.validModes.join(', ')}`);
            }

            const pincodeInfo = await this.getPincodeDetails(pincode, providerId);
            return await this._formatServiceabilityResponse(pincodeInfo, type, mode?.toLowerCase());
        } catch (error) {
            console.error('Error checking serviceability:', error);
            throw error;
        }
    }

    /**
     * Check serviceability between source and destination pincodes
     */
    async checkDeliveryServiceability(sourcePincode, destinationPincode, mode = '', orderType = 'B2C', providerId = 1) {
        try {
            // Input validation
            if (!sourcePincode || !destinationPincode) {
                throw new Error('Source pincode and destination pincode are required.');
            }

            // Validate mode if provided
            if (mode && !this._validateMode(mode)) {
                throw new Error(`Invalid mode. Allowed values: ${this.config.validModes.join(', ')}`);
            }

            // Check serviceability for both pincodes
            const [sourceCheck, destinationCheck] = await Promise.all([
                this.checkServiceability(providerId, sourcePincode, 'source', mode),
                this.checkServiceability(providerId, destinationPincode, 'destination', mode)
            ]);

            // Calculate combined serviceability
            const combined = {
                air: helpers.areBothTrue(sourceCheck.data.air, destinationCheck.data.air),
                surface: helpers.areBothTrue(sourceCheck.data.surface, destinationCheck.data.surface),
                rail: helpers.areBothTrue(sourceCheck.data.rail, destinationCheck.data.rail),
                dp: helpers.areBothTrue(sourceCheck.data.dp, destinationCheck.data.dp),
                pickup: sourceCheck.data.pickup,
                prepaid: destinationCheck.data.prepaid,
                cod: destinationCheck.data.cod,
                reverse: destinationCheck.data.reverse,
                oda: helpers.areBothTrue(sourceCheck.data.oda, destinationCheck.data.oda),
                zone: destinationCheck.data.zoneName,
                zoneCode: destinationCheck.data.zoneCode,
                isServiceable: helpers.areBothTrue(sourceCheck.serviceable, destinationCheck.serviceable),
                modeServiceable: ''
            };

            // Add mode-specific serviceability if mode is specified
            if (mode) {
                combined.modeServiceable = combined[mode];
            }

            // add estimated delivery by serviability
            const estimatedDeliveryDays = await this._calculateEstimatedDeliveryDays(
                sourceCheck.data,
                destinationCheck.data,
                orderType,
                providerId
            );

            return {
                providerId: providerId,
                source: sourceCheck,
                destination: destinationCheck,
                combined,
              //  isServiceable: helpers.areBothTrue(combined.isServiceable, (!mode || combined[mode])),
                isServiceable: combined.isServiceable,
                estimatedDeliveryDays
            };
        } catch (error) {
            console.error('Error checking delivery serviceability:', error);
            throw error;
        }
    }   

    /**
     * Format provider serviceability option
     * @private
     */
    async _formatProviderOption(sourceCheck, destinationCheck, providerId, providerName, orderType) {
        // Ensure sourceCheck and destinationCheck exist
        if (!sourceCheck?.data || !sourceCheck?.data.length === 0 || !destinationCheck?.data || !destinationCheck?.data.length === 0) {
            return null; // Skip processing if any check is invalid
        }
    
        const isModeAvailable = (mode) => 
            sourceCheck.data[mode] && destinationCheck.data[mode];
    
        return {
            providerId,
            providerName,
            isServiceable: sourceCheck.serviceable && destinationCheck.serviceable,
            modes: {
                air: isModeAvailable('air'),
                surface: isModeAvailable('surface'),
                rail: isModeAvailable('rail'),
                dp: isModeAvailable('dp')
            },
            services: {
                pickup: sourceCheck.data.pickup || false,
                prepaid: destinationCheck.data.prepaid || false,
                cod: destinationCheck.data.cod || false,
                reverse: destinationCheck.data.reverse || false,
                oda: (sourceCheck.data.oda && destinationCheck.data.oda) || false
            },
            zoneDetails: {
                zoneName: destinationCheck.data.zoneName || null,
                zoneCode: destinationCheck.data.zoneCode || null
            },
            estimatedDeliveryDays: await this._calculateEstimatedDeliveryDays(
                sourceCheck.data,
                destinationCheck.data,
                orderType,
                providerId             
            ),
            estimatedDeliveryDaysByZone: null
        };
    }
    
    /**
     * Calculate estimated delivery days based on zone, mode and service rpovider
     * @private
     */
    async _calculateEstimatedDeliveryDays(source, destination, orderType, providerId) {
        try {
            // Get the zone relationship data
            const zoneRelationship = await ZoneService.getDeliveryDays({
                sourceZoneId: source.zone, 
                destinationZoneId: destination.zone, 
                orderType,
                providerId
            });
            
            //console.log(zoneRelationship);

            // Check if we got a valid response
            if (!zoneRelationship.length === 0) {
                return {};
            }
            
            // Extract just the delivery modes and days
            const baseDeliveryDays = {
                air: zoneRelationship[0]?.air,
                surface: zoneRelationship[0]?.surface,
                rail: zoneRelationship[0]?.rail,
                dp: zoneRelationship[0]?.dp
            };
            
            const deliveryEstimates = {};
            Object.entries(baseDeliveryDays).forEach(([mode, days]) => {
                // Only include modes that both source and destination support
                if (source[mode] && destination[mode] && days !== undefined) {
                    // Get ODA days from zone relationship if available, otherwise use default logic
                    const odaAdditionalDays = destination?.oda ? (zoneRelationship[0].oda || 2) : 0;
                    deliveryEstimates[mode] = days + odaAdditionalDays;
                }
            });
            
            return deliveryEstimates;
        } catch (error) {
            console.error('Error calculating delivery days:', error);
            return {};
        }
    }

    /**
     * Check multi-provider serviceability between source and destination
     */
    async checkMultiProviderServiceability(sourcePincode, destinationPincode, mode = '', orderType = 'B2C') {
        try {
            if (!sourcePincode || !destinationPincode) {
                throw new Error('Source pincode and destination pincode are required.');
            }

            if (mode && !this._validateMode(mode)) {
                throw new Error(`Invalid mode. Allowed values: ${this.config.validModes.join(', ')}`);
            }

            // Get all active providers
            const providers = await ShippingProvideService.getShippingProviders();
            
            // Check serviceability for each provider
            const providerChecks = await Promise.all(
                providers.map(async (provider, index) => {
                    try {
                        const [sourceCheck, destinationCheck] = await Promise.all([
                            this.checkServiceability(provider.id, sourcePincode, 'source', mode),
                            this.checkServiceability(provider.id, destinationPincode, 'destination', mode)
                        ]);
            
                        // Skip if serviceability check fails for either source or destination
                        if (!sourceCheck || !destinationCheck) {
                            return null; // Skip provider if no serviceability
                        }
            
                        return await this._formatProviderOption(
                            sourceCheck,
                            destinationCheck,
                            provider.id,
                            `Option ${index + 1}`,
                            orderType
                        );
                    } catch (error) {
                        console.error(`Error checking provider ${provider.id}:`, error);
                        return null; // Skip on error
                    }
                })
            );

            // Filter out failed checks and sort by estimated delivery time
            const availableOptions = providerChecks
                .filter(option => option && option.isServiceable)
                .filter(option => !mode || option.modes[mode]);

            // Sort options based on mode if specified, otherwise by fastest available mode
            const sortedOptions = await this._sortDeliveryOptions(availableOptions, mode);

            return {
                sourcePincode,
                destinationPincode,
                requestedMode: mode || 'any',
                hasServiceableOptions: availableOptions.length > 0,
                availableOptionsCount: availableOptions.length,
                deliveryOptions: sortedOptions
                //deliveryOptions:availableOptions
            };
        } catch (error) {
            console.error('Error checking multi-provider serviceability:', error);
            throw error;
        }
    }

    /**
     * Sort delivery options by speed and price
     * @private
     */
    async _sortDeliveryOptions(options, specificMode) {
        const getModeScore = (option, mode) => {
        if (!option.modes[mode]) return Infinity;
            return option.estimatedDeliveryDays[mode];
        };

        return options.sort((a, b) => {
        if (specificMode) {
            return getModeScore(a, specificMode) - getModeScore(b, specificMode);
        }

        // If no specific mode, compare based on fastest available mode
        const aFastest = Math.min(...Object.values(a.estimatedDeliveryDays));
        const bFastest = Math.min(...Object.values(b.estimatedDeliveryDays));
        return aFastest - bFastest;
        });
    }


    // Insert pincodes in batches
    async _insertPincodesBatch (connection, pincodes, createdBy) {
        const query = `
            INSERT INTO shipping_pincodes (
                shipping_provider_id, pincode, city, state, is_active, air, surface, rail, dp, oda, pickup, prepaid, reverse, cod, zone, created_by
            ) VALUES ? 
                ON DUPLICATE KEY UPDATE
                    city = VALUES(city),
                    state = VALUES(state),
                    is_active = VALUES(is_active),
                    air = VALUES(air),
                    surface = VALUES(surface),
                    rail = VALUES(rail),
                    dp = VALUES(dp),
                    oda = VALUES(oda),
                    pickup = VALUES(pickup),
                    prepaid = VALUES(prepaid),
                    reverse = VALUES(reverse),
                    cod = VALUES(cod),
                    zone = VALUES(zone),
                    updated_by = VALUES(created_by)
        `;
        const values = pincodes.map(pincode => [
            pincode.shipping_provider_id,
            pincode.pincode,
            pincode.city,
            pincode.state,
            pincode.is_active || 1, // Default to 1 if not provided
            pincode.air || 1,
            pincode.surface || 1,
            pincode.rail || 1,
            pincode.dp || 1,
            pincode.oda || 0,
            pincode.pickup || 1,
            pincode.prepaid || 1,
            pincode.reverse || 1,
            pincode.cod || 1,
            pincode.zone,
            createdBy
        ]);
        await connection.query(query, [values]);
    };

    // Main function to import pincodes
    async importPincodes (filePath, createdBy) {
        const pincodes = helpers.parseExcel(filePath);
        const batchSize = 1000; // Process 1000 rows at a time
        const connection = await dbpool.getConnection();

        try {
            await connection.beginTransaction();

            for (let i = 0; i < pincodes.length; i += batchSize) {
                const batch = pincodes.slice(i, i + batchSize);

                // Enrich batch with zone_id and shipping_provider_id
                for (const pincode of batch) {   

                    let provider = await ShippingProvideService.getShippingProviderByName(pincode.shipping_provider_name);
                    // console.log(provider);
                    pincode.shipping_provider_id = provider?.id; 
                    // console.log(pincode.shipping_provider_id);
                    let zone = await ZoneService.getZoneByCode( pincode.shipping_provider_id, pincode.zone_code);         
                    pincode.zone = zone?.id;
                    //console.log(pincode.zone);
                    if (!pincode.zone || !pincode.shipping_provider_id) {
                        throw new Error(`Invalid zone_code or shipping_provider_name for pincode: ${pincode.pincode}`);
                    }
                }

                // Insert the batch
                await this._insertPincodesBatch(connection, batch, createdBy);
            }

            await connection.commit();
        } catch (error) {
            await connection.rollback();
            throw error;
        } finally {
            connection.release();
        }
    };

}

module.exports = new PincodeService(cache, dbpool, hcldb);