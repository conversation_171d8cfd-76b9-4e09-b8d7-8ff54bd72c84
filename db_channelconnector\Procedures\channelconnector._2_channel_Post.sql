DROP PROCEDURE IF EXISTS channelconnector._2_channel_Post ;

DELIMITER $$
CREATE  PROCEDURE channelconnector._2_channel_Post(
			`in_channelId` INT, 
			`in_groupCode` VARCHAR(256), 
			`in_currencyCode` VARCHAR(56), 
            `in_ownerCode` VARCHAR(256), 
            `in_channelCode` VARCHAR(256), 
            `in_channelName` VARCHAR(2048), 
            `in_domain` VARCHAR(2048), 
            `in_username` VA<PERSON><PERSON><PERSON>(2048), 
            `in_password` VARCHAR(2048), 
            `in_apiKey` VARCHAR(2048), 
            `in_secret` VARCHAR(2048),
            `in_token` VARCHAR(2048), 
            `in_isEnabled` INT, 
            `in_orderSyncStartDate` DATETIME,
			`in_ebaycompatibilityLevel` VARCHAR(128), 
			`in_ebaydevId` VARCHAR(256),
            `in_ebayappId` VA<PERSON>HA<PERSON>(256),
            `in_ebaycertId` VARCHAR(256),
            `in_ebaysiteId` VARCHAR(256),
            `in_stockUpdate` TINYINT(4),
            `in_orderSync` TINYINT(4),
            `in_productPublish` TINYINT(4),
            `in_priceUpdate` TINYINT(4),

			`in_amazon_Role` 			VARCHAR(128),
			`in_amazon_MarketPlaceID` 	VARCHAR(128),
			`in_amazon_SellerID` 		VARCHAR(128),
			`in_amazon_region`			VARCHAR(128)
            
            
	)
BEGIN
	/*
		call channelconnector.`_2_channel_Post`(
						1111 ,# `channelId`,
			'AEU' ,# `groupCode` ,
			'EUR' ,# `currencyCode` ,
			'YT' ,# `ownerCode`,
			'SFIT' ,# `channelCode`, 
			'shopify' ,# `channelName`,
			'shopping170.myshopify.com' ,# `domain`, 
			NULL ,# `username`, 
			'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' ,# `p,#sword`, #api_p,#sword
			'2ec972a612088fc392de502d7e4c3887' ,# `apiKey`, #API KEY
			NULL ,# `secret`, 
			NULL ,# `token`,
			1, # `isEnabled`,
            '2022-02-28',
            'test234',
            'test',
            'test2',
            'test3',
            'test4',
            true,
            true,
            false,
            false
        );
        
        call channelconnector._2_channel_Get(1002);
        
        call channelconnector._2_channel_delete(1002);
        
        call channelconnector.`getShopifyUser`(1002);
        
    */
    
    DECLARE isExists TINYINT; 
    DECLARE isClientExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
    #SET `in_orderSyncStartDate` = NULL;
    
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel AS T1 
        WHERE 
			T1.`channelId` = `in_channelId` 
			AND T1.isActive = 1 
		LIMIT 1
    );
    
    SET isClientExists = (
        SELECT 1 FROM channelconnector._1_client AS T 
        WHERE 
			LOWER(T.`ownerCode`) = LOWER(`in_ownerCode`) 
            AND T.isActive = 1 
		LIMIT 1
    );
    
    IF(isDeletedExists = 1)
    THEN
		  
        CALL channelconnector.`_2_channel_put`(
			`in_channelId` ,
			`in_groupCode` ,
			`in_currencyCode` ,
			`in_ownerCode` ,
			`in_channelCode`  ,
			`in_channelName` ,
			`in_domain`  ,
			`in_username` ,
			`in_password`  ,
			`in_apiKey`  ,
			`in_secret` ,
			`in_token`  ,
			`in_isEnabled` ,
            `in_orderSyncStartDate`,
            `in_ebaycompatibilityLevel`,
            `in_ebaydevId`,
            `in_ebayappId`,
            `in_ebaycertId`,
            `in_ebaysiteId`,
			`in_stockUpdate` ,
            `in_orderSync` ,
            `in_productPublish` ,
            `in_priceUpdate`
        );
       
        
    ELSE
    IF (isClientExists = 0 OR isClientExists IS NULL)
    THEN
		SELECT 1 AS isErrorClientNotFound;
        
    ELSE IF isExists = 1
    THEN
		SELECT 1 AS isErrorAlreadyExists;
	ELSE
			   INSERT INTO `channelconnector`.`_2_channel`
				(
					`channelId` ,
					`groupCode` ,
					`currencyCode` ,
					`ownerCode` ,
					`channelCode`  ,
					`channelName` ,
					`domain`  ,
					`username` ,
					`password`  ,
					`apiKey`  ,
					`secret` ,
					`token`  ,
					`isEnabled` ,
                    `isActive`,
					`orderSyncStartDate` ,
                    `compatibilityLevel`,
					`ebay_devid`,
					`ebay_appid`,
					`ebay_certid`,
					`siteId`,
                    `stockUpdate`,
					`priceUpdate`,
					`orderSync`,
					`productPublish`
				)
				VALUES
				(
					`in_channelId` ,
					`in_groupCode` ,
					`in_currencyCode` ,
					`in_ownerCode` ,
					`in_channelCode`  ,
					`in_channelName` ,
					`in_domain`  ,
					`in_username` ,
					`in_password`  ,
					`in_apiKey`  ,
					`in_secret` ,
					`in_token`  ,
					`in_isEnabled` ,
                     1 ,
                    `in_orderSyncStartDate`,
                    `in_ebaycompatibilityLevel`,
					`in_ebaydevId`,
					`in_ebayappId`,
					`in_ebaycertId`,
					`in_ebaysiteId`,
					`in_stockUpdate` ,
					`in_orderSync` ,
					`in_productPublish` ,
					`in_priceUpdate`
				);
				
				call channelconnector._2_channel_Get(`in_channelId`);
                
			END IF;
		END IF;
    END IF;
    
END$$
DELIMITER ;
