USE channelconnector;

DROP  PROCEDURE IF EXISTS channelconnector.getPaymentMethod;

DELIMITER $$
CREATE  PROCEDURE channelconnector.getPaymentMethod(
	IN `in_payment_method` VARCHAR(100), 
    IN `in_chanel` VARCHAR(50)
)
BEGIN
/*
	CALL getPaymentMethod('NaN','Shopify IT');
	CALL getPaymentMethod('credit_card','Shopify IT');

*/
IF EXISTS (
			SELECT * FROM map_channel_payment_method AS mcp 
            WHERE mcp.ch_payment_method = in_payment_method
				and mcp.channel = in_chanel
) 
THEN 
		SELECT 
			mcp.id,
			mcp.channel,
			mcp.ch_payment_method,
			mcp.fby_payment_method
		FROM
			map_channel_payment_method AS mcp
		WHERE
			mcp.ch_payment_method = in_payment_method
				AND mcp.channel = in_chanel
		ORDER BY id DESC
		LIMIT 1;
 ELSE
	SELECT 
		0 as id,
		in_chanel as channel,
		in_payment_method as ch_payment_method,
		in_payment_method as by_payment_method;
  END IF;          
  
END$$
DELIMITER ;
