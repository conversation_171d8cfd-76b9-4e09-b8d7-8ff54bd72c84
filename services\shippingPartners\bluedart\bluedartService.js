//shippingPartners/delhivery/bluedartService.js
const BaseService = require('../baseService');
const OrderService = require('../../hcl/orderService.js');
const { getBluedartConfig } = require('../../../misc/shippingConfig');
const { PAYMENT_MODES, SHIPMENT_TYPES, ORDER_TYPES } = require('../../../misc/enums/orderStatusEnum');
const logger = require('../../../misc/logger.js');
const cache = require('../../../misc/cache.js');
const helpers = require('../../../misc/helpers.js');
const miscConstants = require('../../../misc/constants.js');
const axios = require('axios');
const xml2js = require('xml2js');

class BluedartService extends BaseService {
   
    constructor( providerDetails, type ) {
       
        const config = getBluedartConfig();
        super(config, type, providerDetails);
        
        if (!config.apiType || !config.verNo || !config.clientID || !config.clientSecret) {
            logger.logError(`Bluedart: Missing required API configuration.`);
            throw new Error("Missing required API configuration.");
        }

        this.shippingProviderId = providerDetails?.id || config.shippingProviderId; // Example value
        //this.trackLicKey = config.trackLicKey;
        this.licKey = config.licKey;
        this.loginId = config.loginId;
        this.apiType =  config.apiType,
        this.verNo = config.verNo;
        this.originArea = config.originArea;
        this.clientId = config.clientID;
        this.clientSecret = config.clientSecret;
        this.customerCode = config.customerCode;
        this.productTypes = config.productTypes;
        // this.productType = 1;
        // this.productCode = 'A';
        // this.subProductCode = 'P';
        // this.category = ORDER_TYPES.B2C;
        this.sender = config.sender;
        this.getUrls = config.getUrls(this.environment);
        this.cacheKey = 'bluedart_jwt_token';
        this.cacheExpiry = 3600; // 1 hour in seconds
        // profile information for bluedart
        this.profile = {    
            "Api_type": this.apiType,
            "LicenceKey":  this.licKey,
            "LoginID":  this.loginId
        };
    }
    
    setServiceType(order, isReverse) {

        const serviceType = isReverse
                            ? 'RVP'
                            : order?.orderType?.toUpperCase() === ORDER_TYPES.B2B
                            ? ORDER_TYPES.B2B
                            : ORDER_TYPES.B2C;
    
        const serviceMode = order?.shipmentDetails?.express?.toUpperCase() || miscConstants.SHIPPING.MODE.AIR.toUpperCase();
      
        // const serviceMode = expressMode === miscConstants.SHIPPING.MODE.AIR.toUpperCase() 
        //                     ? miscConstants.SHIPPING.MODE.AIR.toUpperCase()
        //                     : miscConstants.SHIPPING.MODE.SURFACE.toUpperCase();

        const service = this.productTypes[serviceType][serviceMode];

        if (!service) {
            throw new Error(`Invalid BlueDart service type: ${serviceType}`);
        }
        
        this.currentServiceType = `${serviceType}_${serviceMode}`;
        this.productCode = service.code;
        this.productName = service.name;
        this.subProductCode = service.subProductCode;
        this.productType = service.productType;
        this.packType = service.packType;
        this.category = service.category;
  
    }

     // Common consignee/return address structure
    async buildAddressEntity(address, contact, entityType = 'Consignee') {
        return {
            [`${entityType}Address1`]: this.cleanAddress(address.addressLine1),
            [`${entityType}Address2`]: this.cleanAddress(address.addressLine2),
            [`${entityType}Address3`]: this.formatAddress(address),
            [`${entityType}Addressinfo`]: "",
            [`${entityType}Attention`]: "ABCD",
            [`${entityType}EmailID`]: contact.email,
            [`${entityType}FullAddress`]: this.formatAddress(address),
            [`${entityType}GSTNumber`]: "",
            [`${entityType}Latitude`]: "",
            [`${entityType}Longitude`]: "",
            [`${entityType}MaskedContactNumber`]: "",
            [`${entityType}Mobile`]: this.formatContactNumber(contact.consigneeContact),
            [`${entityType}Name`]: `${address.consigneeFirstName} ${address.consigneeLastName}`,
            [`${entityType}Pincode`]: address.zip,
            [`${entityType}Telephone`]: this.formatContactNumber(contact.alternateContact)
        };
    }

    // Common shipper structure
    async buildShipperEntity(pickUpAddress, contact) {
        return {
            "CustomerAddress1": this.cleanAddress(pickUpAddress.address.addressLine1),
            "CustomerAddress2": this.cleanAddress(pickUpAddress.address.addressLine2),
            "CustomerAddress3": this.formatAddress(pickUpAddress.address),
            "CustomerAddressinfo": "",
            "CustomerBusinessPartyTypeCode": "",
            "CustomerCode": this.customerCode,
            "CustomerEmailID": contact.email,
            "CustomerGSTNumber": pickUpAddress?.gstNumber || '',
            "CustomerLatitude": "",
            "CustomerLongitude": "",
            "CustomerMaskedContactNumber": "",
            "CustomerMobile": this.formatContactNumber(contact.consigneeContact),
            "CustomerName": `${pickUpAddress.consigneeFirstName} ${pickUpAddress.consigneeLastName}`,
            "CustomerPincode": pickUpAddress.address.zip,
            "CustomerTelephone": this.formatContactNumber(contact.alternateContact),
            "IsToPayCustomer": false,
            "OriginArea": this.originArea,
            "Sender": this.sender,
            "VendorCode": ""
        };
    }

    // Common services structure
    async buildServicesEntity(order, packages, isReverse = false) {
        const isCod = helpers.isPaymentModeCOD(order.paymentMode);
        const codAmount = order.shipmentDetails.order.collectibleCod && isCod 
                        ? parseFloat(order.shipmentDetails.order.collectibleCod) : 0;

        // Calculate total weight and prepare dimensions array
        let totalWeight = 0;
        const dimensions = packages.map(pkg => {
            totalWeight += parseFloat(pkg.weight);
            return {
                "Breadth": parseFloat(pkg.width),
                "Count": 1,
                "Height": parseFloat(pkg.height),
                "Length": parseFloat(pkg.length)
            };
        });

        // Prepare item details
        const itemDetails = packages.map((pkg, index) => ({
            "ActualWeight": parseFloat(pkg.weight),
            "DeclaredValue": parseFloat(pkg.value),
            "Description": pkg.productName || `Item ${index + 1}`,
            "Length": parseFloat(pkg.length),
            "Width": parseFloat(pkg.width),
            "Height": parseFloat(pkg.height),
            "IsFragile": false,
            "PieceCount": 1,
            "PieceIdentificationNo": pkg.packageId || (index + 1),         
        }));

        const PieceDetails = packages.map((pkg, i) => ({
            "PieceNumber": i + 1,
            "PieceIdentificationNo": pkg.packageId || `${order.orderId}-${i+1}`,
            "ActualWeight": pkg.weight,
            "Length": pkg.length,
            "Width": pkg.width,
            "Height": pkg.height,
            "DeclaredValue": pkg.value,
            "Description": pkg.description || `Item ${i+1}`,
            // B2B-specific:
            ...(this.isB2B() && {
                "HSNCode": pkg.hsnCode || '',
                "CountryOfOrigin": pkg.originCountry || 'India'
            })
        }));


        // Set pickup date to next day
        const today = new Date();
        const nextDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
        const pickupDate = nextDay.getTime();

        return {
            "AWBNo": "",
            "ActualWeight": totalWeight,
            "CollectableAmount": isReverse ? 0 : codAmount, // No COD for reverse shipping
            "CreditReferenceNo": order.shipmentDetails.invoiceNo,
            "CreditReferenceNo2": "",
            "CreditReferenceNo3": "",
            "DeclaredValue": parseFloat(order.shipmentDetails.order.totalAmount),
            "DeliveryTimeSlot": "",
            "Dimensions": dimensions,
            "FavouringName": "",
            "IsDedicatedDeliveryNetwork": false,
            "IsDutyTaxPaidByShipper": false,
            "IsForcePickup": false,
            "IsPartialPickup": false,
            "IsReversePickup": isReverse,
            "RegisterPickup": true,
            "SpecialInstruction": "",
            "ItemCount": packages.length,
            "Officecutofftime": "",
            "PDFOutputNotRequired": true,
            "PackType": this.packType,
            "ParcelShopCode": "",
            "PayableAt": "",
            "PickupDate": `/Date(${pickupDate})/`,
            "PickupMode": "",
            "PickupTime": 1600, // 4 PM
            "PickupType": "",
            "PieceCount": packages.length.toString(),
            "PreferredPickupTimeSlot": "",
            "ProductCode": this.productCode,
            "ProductType": this.productType,
            "SubProductCode": this.getSubProductCode(order, isReverse),
            "ProductFeature": this.isB2B() ? ORDER_TYPES.B2B : ORDER_TYPES.B2C,
            "TotalCashPaytoCustomer": 0,
            "itemdtl": itemDetails,
            //"PieceDetails": PieceDetails, 
            "noOfDCGiven": 0
        };
    }

    getSubProductCode(order, isReverse) {
        if (this.isB2B() || isReverse) return this.subProductCode;
        if (helpers.isPaymentModeCOD(order.paymentMode)) return 'C';   
        return this.subProductCode; // From serviceType config
    }

    isB2B() {
        return this.category === ORDER_TYPES.B2B;
    }

    buildB2BComponents(order) {
        return {
            "CommercialInvoice": {
                "InvoiceNumber": order.invoiceNo,
                "InvoiceDate": this.formatDate(order.createdOn),
                "GSTNumber": order?.gstNumber || '',
                "EWayBill": order?.awb || ''
            },
            "CustomsDeclaration": {
                "ItemDetails": order.shipmentDetails.productList.map(item => ({
                    "Description": item.name,
                    "HSNCode": item.hsnCode || '',
                    "Quantity": item.quantity,
                    "Value": item.value
                }))
            }
        };
    }

    async buildConsigneeEntity(addresses, isReverse) {
        const targetAddress = isReverse ? addresses.returnAddress : addresses.deliveryAddress;
        const entity = await this.buildAddressEntity(targetAddress.address, targetAddress.contact, 'Consignee');
        
        // B2B-specific fields
        if (this.isB2B()) {
            entity.ConsigneeGSTNumber = targetAddress.gstNumber || 'GST987654A';
            entity.ConsigneeBusinessPartyTypeCode = 'B'; // B for Business
        }
        
        return entity;
    }
    
    async buildReturnEntity(addresses, isReverse) {
        const targetAddress = isReverse ? addresses.deliveryAddress : addresses.returnAddress;
        return this.buildAddressEntity(targetAddress.address, targetAddress.contact, 'Return');
    }


    async checkServiceability(pincode) {
        // Bluedart-specific API call to check serviceability
    }

    async generateJWTToken() {
        let jwtToken = '';

        try {
            const cachedToken = cache.get(this.cacheKey);
            if (cachedToken) {
                return cachedToken;
            }
            // Get the authentication URL based on environment
            const authUrl = `${this.baseUrl}${this.getUrls.authentication}`;
            // Set up request headers
            const headers = {
                'ClientID': this.clientId,
                'ClientSecret': this.clientSecret
            };

            // Make the request
            const response = await axios({
                method: 'GET',
                url: authUrl,
                headers: headers
            });

            if (response.status === 200 && response.data) {
                jwtToken = response.data.JWTToken || '';
                
                if (jwtToken) {
                    // Store token in cache
                    cache.set(this.cacheKey, jwtToken, this.cacheExpiry);
                    logger.logInfo('Bluedart: Token generated successfully!');
                } else {
                    logger.logError('Bluedart: Token not found in response');
                }
            } else {
                logger.logError('Bluedart: Token not generated!');
            }

        } catch (error) {
            logger.logError(`Bluedart: ${error.message}`);
            throw new Error(`JWT Token Generation Error: ${error.message}`);
        }

        return jwtToken;
    }

    async generateShipmentObject(order, isReverse = false) {
        try {

             // Determine Service Type (B2B/B2C + Surface/Air)
            this.setServiceType(order);
            
            // const serviceType = order.orderType + '_' + order.shipmentDetails.express;
            // this.setServiceType(serviceType);

            const packages = await OrderService.getPackageDetails(order);
            const addresses = await OrderService.getAddressFromOrder(order);

            //console.log(addresses);

            const shipment = {
                "Request": {
                    "Consignee": await this.buildConsigneeEntity(addresses, isReverse),
                    "Returnadds": await this.buildReturnEntity(addresses, isReverse),
                    "Services": await this.buildServicesEntity(order, packages, isReverse),
                    "Shipper": await this.buildShipperEntity(addresses.pickUpAddress, addresses.pickUpAddress.contact),
                     // B2B-specific additions
                    ...(this.isB2B() && this.buildB2BComponents(order))
                },
                "Profile": this.profile
            };
            return shipment;
        } catch (error) {
            logger.logError(`Error in creating shipment object: ${error.message}`, error);
            throw new Error(`Error in creating shipment object: ${error.message}`);
        }
    }

    // Specific method for forward shipping
     async createForwardShipment(order) {
        return this.createShipment(order, false);
    }

    // Specific method for reverse shipping
    async createReverseShipment(order) {
        return this.createShipment(order, true);
    }

    async createShipment(order) {         
        try {
            let success = false;
            let awbNumber = '';
            let tokenNumber = '';
            let referenceCode = '';
            let message = '';
            let isReverse = false;
            let waybillInfo = null;
        
            logger.logInfo(`Bluedart: Start ${isReverse ? 'Reverse' : 'Forward'} Pickup Request.`, order);
                 
            try {

                const shipmentObject = await this.generateShipmentObject(order, isReverse);
                // Prepare API request parameters
                const params = {
                    body: JSON.stringify(shipmentObject).replace(/\\\//g, '/'),
                    headers: {
                        JWTToken: await this.generateJWTToken(),
                        'Content-Type': 'application/json',
                    },
                };

                logger.logInfo(`Bluedart: Request Payload.  Order Id: ${order.orderId}`, params.body);
                console.log(params.body);

                const url = `${this.baseUrl}${this.getUrls.generateWayBill}`;
                const responseBody = await this.makeRequest( url, params.headers, 'POST', params.body );

                console.log(JSON.stringify(responseBody));
                logger.logInfo(`Bluedart: Response body. Order Id: ${order.orderId}`, JSON.stringify(responseBody));
                
                //var responseBody = response.data;
                if (responseBody.GenerateWayBillResult) {
                    const result = responseBody.GenerateWayBillResult;
                    waybillInfo = result;
                    success = !result.IsError;
                    awbNumber = result.AWBNo || '';
                    tokenNumber = result.TokenNumber || '';
                    referenceCode = result.CCRCRDREF || '';
                    message = result.Status[0]?.StatusInformation || 'Request processed successfully.';
                }

                logger.logInfo(`Bluedart: Response - AWB: ${awbNumber}, Invoice Number; ${referenceCode}, Message: ${message}`, waybillInfo);
        
            } catch(error) {

                const errorResponse = error.response?.data || {};
                logger.logError('Bluedart: Error Details', errorResponse);
                console.error('Bluedart API Error:', error);

                if(errorResponse && errorResponse?.['error-response']?.length > 0) {
                    success = !errorResponse?.['error-response']?.[0]?.IsError || false;
                    message = errorResponse?.['error-response']?.[0]?.Status?.[0]?.StatusInformation || 'An unknown error occurred.';
                    referenceCode = errorResponse?.['error-response']?.[0]?.CCRCRDREF || '';
                } else {
                    success = false;
                    message = error.message || 'An unknown error occurred.';
                    referenceCode = '500';
                }
             
                // console.error('Request Failed:', error.config?.data || 'No request data available');
                console.error(error.message);
                // console.error(errorResponse?.['error-response']?.[0]?.Status);
                logger.logError(`Bluedart: Error - ${errorResponse?.['error-response']?.[0]?.Status}`);

            }
            logger.logInfo('Bluedart: End Forward Pickup Request.');       
            return { success, awbNumber, tokenNumber, referenceCode, message, waybillInfo};
        } catch(error) {
            logger.logError(`Error in Bluedart shipment function : ${error.message}`, error);
            throw new Error(`Error in shipment function : ${error.message}`);
        }     
    }

    /**
     * 
     * @param {*} numbers 
     * @param {*} format (xml)
     * @param {*} queryType ('awb', 'ref')
     * @returns 
     */

    async trackShipment( numbers, format = 'xml', queryType = 'awb' ) {
        try {

            logger.logInfo(`Bluedart: Start Shipment Tracking. ${queryType} :`, numbers);
           
            const params =  { 
                    handler: 'tnt',
                    loginid: this.loginId,
                    numbers: numbers,
                    format: format,
                    lickey: this.licKey,
                    scan: 1,
                    action: 'custawbquery',
                    verno: this.verNo,
                    awb: queryType, 
                };

            const headers = {
                    JWTToken: await this.generateJWTToken(),
                    'Content-Type': 'application/json',
                    'accept': 'text/plain'
                }

            const url = `${this.baseUrl}${this.getUrls.tracking}?${new URLSearchParams(params).toString()}`
            logger.logInfo('Bluedart: Request Payload', url);
            
            logger.logInfo('Bluedart: Request Headers', headers);
            console.log(headers);
            
            // let state = '';
            // let status = '';
            // let deliveredDate = null;
            // let expectedDelivery = null;
            // let dateTime = new Date().toISOString();
            try {

                //const xmlResponse = await this.makeRequest( url, params.headers, 'GET' );
                const response = await axios({
                    method: 'GET',
                    url: url,
                    headers: params.headers
                });
                const xmlResponse = response.data;
                // // Save raw API response
                // await AwbStatus.updateOrCreate(
                //     { awb_mapping_id: awbId },
                //     { api_response: xmlResponse }
                // );
        
                // // Convert XML to JSON
                const xmlToJson = await xml2js.parseStringPromise(xmlResponse, { explicitArray: false });
                // const shipment = xmlToJson.Shipment || {};
        
                // const statusType = shipment.StatusType || '';
                // const shipmentStatus = shipment.Status || '';
                // logger.info(`Shipment Status: ${shipmentStatus}`);
        
                // if (statusType === 'IT' || statusType === 'UD') {
                //     dateTime = shipment.Scans?.ScanDetail?.[0]?.ScanDate || dateTime;
                // } else {
                //     const statusDate = shipment.StatusDate || new Date().toISOString().split('T')[0];
                //     const statusTime = shipment.StatusTime || new Date().toISOString().split('T')[1];
                //     dateTime = new Date(`${statusDate}T${statusTime}`).toISOString();
                // }
        
                // if (statusType !== 'PU' && statusType !== 'UD' && type === 'reverse') {
                //     expectedDelivery = shipment.ExpectedDeliveryDate
                //         ? new Date(shipment.ExpectedDeliveryDate).toISOString()
                //         : new Date().toISOString();
                // }
        
                // if (statusType === 'DL') {
                //     deliveredDate = dateTime;
                // }
        
                // if (type === 'reverse') {
                //     const statusCode = shipment.Scans?.ScanDetail?.[0]?.ScanCode || '';
                //     status = process.env[`STATUS_REVERSE_BLUEDART_${statusType}${statusCode}`];
                //     state = '';
                // } else {
                //     status = process.env[`STATUS_BLUEDART_${statusType}`];
                //     state = process.env[`STATUS_STATE_${status}`];
                // }
        
                // const finalResponse = {
                //     state,
                //     status,
                //     delivered_date: deliveredDate,
                //     expected_delivery: expectedDelivery,
                // };
        
                const finalResponse = {
                    success: true,
                    data: xmlToJson
                };
                logger.logInfo(`Response: ${JSON.stringify(finalResponse)}`);
                logger.logInfo('Bluedart: End Shipment Tracking.');
        
                return finalResponse;
            } catch (error) {
                let msg = '';
                console.log(error);
                console.log(error.response?.data);
                if (error.response?.data) {
                    const response = error.response.data;
                    msg = { title: response.title || 'Unknown',
                            status: error.response.status || 'Bad Request',
                            message: response['error-response'][0]
                        };
                } else {
                    msg = 'Unknown error occurred.';
                }
                console.log(msg);
                logger.logError(`Bluedart Trach shipment API Error: ${msg}`);
                logger.logInfo('Bluedart: End Shipment Tracking.');
        
                return { success: false, message: msg };
            }
        } catch(error) {
            logger.logError(`Bluedart: Track shipment: ${error.message}`, error);
            throw new Error(`Error Track shipment: ${error.message}`);
        }    
    }

}

module.exports = BluedartService;



   







