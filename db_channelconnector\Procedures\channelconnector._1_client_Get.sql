DROP PROCEDURE IF EXISTS channelconnector._1_client_Get;

DE<PERSON><PERSON><PERSON>ER $$
CREATE PROCEDURE channelconnector.`_1_client_Get`(
  `in_clientId` varchar(1024)
  )
BEGIN
	/*
    
		call channelconnector.`_1_client_Get`(
			''
        );
        
		call channelconnector.`_1_client_Get`(
			'Id-001'
        );
        
    */
   SET in_clientId = LOWER(`in_clientId`);
   
   IF(in_clientId IS NULL OR in_clientId ='')
   THEN
		SELECT   
			clientId,
			`name`,
			ownerCode,
			CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			isActive = 1;
   
   ELSE
		SELECT   
			clientId,
			`name`,
			ownerCode,
			CAST(createdOn as CHAR) as createdOn,
			CAST(modifiedOn as CHAR) as modifiedOn
		FROM 
			`channelconnector`.`_1_client`
		WHERE 
			LOWER(clientId) = in_clientId
			AND isActive = 1;
	END IF;
END$$
DELIMITER ;
