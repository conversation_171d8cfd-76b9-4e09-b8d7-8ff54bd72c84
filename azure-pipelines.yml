trigger:
  branches:
    include:
      - dev  # backend deployment when pushing to the develop branch

pool:
  name: logisticsVMpool  # Use a self-hosted agent pool

variables:
  - group: LogisticsVM  # Reference the variable group containing your secure file info
  - name: AZURE_PAT  # Securely reference the Personal Access Token variable for authentication
  - name: AZURE_DEVOPS_ORG  # Define the Azure DevOps organization name
  - name: logistics_frontend_URL  # Define the repository path (for the frontend)

stages:
  - stage: DeployToVM
    displayName: 'Deploy Backend to Azure VM'
    jobs:
      - job: DeployToVM
        displayName: 'Deploy Backend to Azure VM'
        steps:
          # Step 1: Print Only the Variables Being Used
          - script: |
              echo "Printing variables used in the pipeline:"
              echo "AZURE_DEVOPS_ORG: $(AZURE_DEVOPS_ORG)"  # Print Azure DevOps organization
              echo "AZURE_PAT: $(AZURE_PAT)"  # Print Azure PAT (for debugging purposes)
              echo "logistics_frontend_URL: $(logistics_frontend_URL)"  # Print the specific variable for frontend repo
              echo "LogisticsVMKeyFile: $(LogisticsVMKeyFile)"  # Print the specific variable for the key file
              echo "Agent Temp Directory: $(Agent.TempDirectory)"  # Print Temp Directory path
            displayName: 'Print Variables Being Used'
            continueOnError: true  # Continue even if this step fails

          # Step 2: Check if the agent is available and SSH is working
          - script: |
              echo "Checking if the agent is available and SSH is properly configured..."
              uname -a  # Check system info (verify agent availability)
              echo "Checking SSH availability..."
              ssh -V  # Verify if SSH is installed on the agent
              echo "Checking if the private key file is present..."
              ls -l $(Agent.TempDirectory)/sandeepkumarLogisticsVM.pem  # Ensure SSH key is available
            displayName: 'Verify Agent and SSH Availability'
            continueOnError: true  # Continue even if this step fails

          # Step 3: Download Secure Key File (Ensure you have the correct key file name in the library)
          - task: DownloadSecureFile@1
            inputs:
              secureFile: 'sandeepkumarLogisticsVM.pem'  # Ensure this is the exact name of your secure file in the library
            displayName: 'Download SSH Key File'
            continueOnError: true  # Continue even if this step fails

          # Step 4: Set Permissions for SSH Key (Needed to ensure proper permissions on the key file)
          - script: |
              sudo chmod 600 $(Agent.TempDirectory)/sandeepkumarLogisticsVM.pem  # Set the correct permissions on the SSH key
            displayName: 'Set Permissions for SSH Key'
            continueOnError: true  # Continue even if this step fails

          # Step 5: Test SSH Connection to VM (Verifying that SSH works with the downloaded key)
          - task: SSH@0
            inputs:
              sshEndpoint: 'AzureVM_SSH_ServiceConnection'  # Ensure this service connection is correctly configured
              privateKey: $(Agent.TempDirectory)/sandeepkumarLogisticsVM.pem  # Use the secure file's path
              runOptions: 'inline'
              inline: |
                echo "Testing SSH connection..."
                sudo hostname  # Verify SSH connection by getting the hostname
            displayName: 'Test SSH Connection to VM'
            continueOnError: true  # Continue even if this step fails

          # Step 6: Install Node.js (Ensure version 18.x is installed)
          - task: UseNode@1  # Using version 1 of UseNode task to install Node.js
            inputs:
              versionSpec: '18.x'  # Install Node.js version 18.x
              addToPath: true  # Ensure Node.js is added to the system PATH
            displayName: 'Install Node.js'
            continueOnError: true  # Continue even if this step fails

          # Step 7: Checkout the repository (logistics_backend)
          - checkout: self  # Checkout the default repository (This will checkout the logistics_backend repository)
            displayName: 'Checkout Repository'
            continueOnError: true  # Continue even if this step fails

          # Step 8: Deploy backend to VM
          - task: SSH@0
            inputs:
              sshEndpoint: 'AzureVM_SSH_ServiceConnection'  # Ensure SSH service connection is correct
              privateKey: $(Agent.TempDirectory)/sandeepkumarLogisticsVM.pem  # Use the SSH private key
              runOptions: 'inline'
              inline: |
                echo "Deploying backend..."
                # Use StrictHostKeyChecking=no to bypass host key verification
                sudo bash -c "cd /var/www/hclbackend && GIT_SSH_COMMAND='ssh -o StrictHostKeyChecking=no' git pull https://DigiSpin-Technologies:<EMAIL>/DigiSpin-Technologies/Yocabe/_git/logistics_backend dev && npm install"
            displayName: 'Deploy backend to VM'
            continueOnError: true  # Continue even if this step fails

          # Step 9: Ensure PM2 is Running
          - task: SSH@0
            inputs:
              sshEndpoint: 'AzureVM_SSH_ServiceConnection'
              privateKey: $(Agent.TempDirectory)/sandeepkumarLogisticsVM.pem
              runOptions: 'inline'
              inline: |
                echo "Checking if PM2 is managing processes..."
                export PM2_HOME=/home/<USER>/.pm2  # Adjust this path based on your user
                pm2 list  # List all running PM2 processes
                if [ $? -ne 0 ]; then
                  echo "No processes found, starting the backend app..."
                  pm2 start /var/www/hclbackend/app.js --name hclbackend  # Adjust path to your app.js
                  pm2 save  # Save the PM2 process list
                fi
            displayName: 'Ensure PM2 is Running'
            continueOnError: true

          # Step 10: Restart Nginx and PM2 services
          - task: SSH@0
            inputs:
              sshEndpoint: 'AzureVM_SSH_ServiceConnection'  # Ensure SSH service connection is correct
              privateKey: $(Agent.TempDirectory)/sandeepkumarLogisticsVM.pem  # Use the private key
              runOptions: 'inline'
              inline: |
                echo "Restarting Nginx and PM2..."
                sudo -E systemctl restart nginx  # Restart Nginx while preserving the user's environment
                export PM2_HOME=/home/<USER>/.pm2  # Ensure PM2 environment is set
                sudo -E pm2 restart hclbackend  # Restart PM2 processes with the user's environment
            displayName: 'Restart Nginx and PM2 Services'
            continueOnError: true  # Continue even if this step fails
