DROP PROCEDURE IF EXISTS channelconnector.fbyOrderErrorManage;

DELIMITER $$
CREATE PROCEDURE `fbyOrderErrorManage`(
	IN `in_fby_user_id` VARCHAR(128), 
	IN `in_order_no` VARCHAR(256), 
	IN `in_exist` TINYINT(4) UNSIGNED, 
	IN `in_cron_name` VARCHAR(60), 
	IN `in_cron_id` VARCHAR(100), 
	IN `in_error_type` VARCHAR(60), 
	IN `in_error_msg` TEXT, 
	IN `in_time` DATETIME
)
BEGIN
SET SQL_SAFE_UPDATES = 0;
    IF in_exist = 1 THEN
      UPDATE order_masters AS OM 
	  SET 
	  	OM.count=OM.count+1,
		OM.updated_at=in_time 
	  WHERE 
	  	OM.cron_id=in_cron_id 
		AND OM.order_no=in_order_no;
      
      UPDATE cron_error_log AS cl 
	  SET
	  	cl.type_error=in_error_type,
		cl.error_message=in_error_msg
	  WHERE cl.cron_id=in_cron_id;
    ELSE
     UPDATE order_masters AS OM 
	 SET 
	 	OM.fby_error_flag=1,
		OM.count=1,
		OM.cron_name=in_cron_name,
		OM.cron_id=in_cron_id,
		OM.updated_at=in_time 
	 WHERE 
	 	OM.fby_user_id=in_fby_user_id 
		AND OM.order_no=in_order_no;
    END IF;
SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;