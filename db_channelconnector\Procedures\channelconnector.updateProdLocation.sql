DROP PROCEDURE IF EXISTS channelconnector.updateProdLocation;

DEL<PERSON>ITER $$ 
CREATE PROCEDURE channelconnector.`updateProdLocation` (
	`in_fby_id` VARCHAR(128),
	`in_inventry_id` VARCHAR(127),
	`in_loc_id` VARCHAR(256),
	`in_crn_name` VARCHAR(60),
	`in_crn_id` VARCHAR(100),
	`in_time` DATETIME
) 
BEGIN
	/*
		CALL channelconnector.updateProdLocation(8,'44437908324610',66372763906,'Get_Shopify_Location','feceac14-421c-411c-9dac-188cb43fb4ba','2022-02-28 14:26:57');
    */
    SET SQL_SAFE_UPDATES = 0;
	UPDATE
		products AS p
	SET
		p.location_id = case when in_loc_id > 0 then in_loc_id else p.location_id  end,
		p.cron_name = in_crn_name,
		p.count = 0,
		p.fby_error_flag = 0,
		p.cron_id = in_crn_id,
		p.updated_at = in_time
	WHERE
		p.inventory_item_id = in_inventry_id
		AND p.fby_user_id = in_fby_id
		AND p.location_id = 0
        AND in_loc_id > 0;
	SET SQL_SAFE_UPDATES = 1;
END$$
DELIMITER ;