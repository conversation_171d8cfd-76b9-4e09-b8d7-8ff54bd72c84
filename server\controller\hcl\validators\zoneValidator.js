const Joi = require('joi');
const CONSTANT = require('../../../../misc/constants.js');
const ENUMCONSTANT = require('../../../../misc/enums/orderStatusEnum.js');

const orderTypes = ENUMCONSTANT.ORDER_TYPES_ARRAY;

const validateDeliveryDaysSchema = Joi.object({
    sourceZoneId: Joi.number().integer().positive().required()
      .messages({
        'number.base': 'Source zone ID must be a number',
        'number.integer': 'Source zone ID must be an integer',
        'number.positive': 'Source zone ID must be positive',
        'any.required': 'Source zone ID is required'
      }),
    destinationZoneId: Joi.number().integer().positive().required()
      .messages({
        'number.base': 'Destination zone ID must be a number',
        'number.integer': 'Destination zone ID must be an integer',
        'number.positive': 'Destination zone ID must be positive',
        'any.required': 'Destination zone ID is required'
      }),
    orderType: Joi.string().valid(...orderTypes).insensitive().required()
      .messages({
        'string.base': 'Order type must be a string',
        'any.only': 'Order type must be either B2C or B2B',
        'any.required': 'Order type is required'
      }),
    providerId: Joi.number().integer().positive().required()
      .messages({
        'number.base': 'Provider ID must be a number',
        'number.integer': 'Provider ID must be an integer',
        'number.positive': 'Provider ID must be positive',
        'any.required': 'Provider ID is required'
      }),
    surface: Joi.number().integer().min(0).allow(null)
      .messages({
        'number.base': 'Surface days must be a number',
        'number.integer': 'Surface days must be an integer',
        'number.min': 'Surface days cannot be negative'
      }),
    air: Joi.number().integer().min(0).allow(null)
      .messages({
        'number.base': 'Air days must be a number',
        'number.integer': 'Air days must be an integer',
        'number.min': 'Air days cannot be negative'
      }),
    rail: Joi.number().integer().min(0).allow(null)
      .messages({
        'number.base': 'Rail days must be a number',
        'number.integer': 'Rail days must be an integer',
        'number.min': 'Rail days cannot be negative'
      }),
    dp: Joi.number().integer().min(0).allow(null)
      .messages({
        'number.base': 'DP days must be a number',
        'number.integer': 'DP days must be an integer',
        'number.min': 'DP days cannot be negative'
      }),
    oda: Joi.number().integer().min(0).allow(null)
      .messages({
        'number.base': 'ODA days must be a number',
        'number.integer': 'ODA days must be an integer',
        'number.min': 'ODA days cannot be negative'
      })
  })
  .custom((value, helpers) => {
    // At least one delivery mode must be provided
    if (value.surface === null && 
        value.air === null && 
        value.rail === null && 
        value.dp === null) {
      return helpers.error('custom.delivery', {
        message: 'At least one delivery mode (surface, air, rail, or dp) must be provided'
      });
    }
    return value;
});
  
const validateDeliveryDays = (data) => {
    return validateDeliveryDaysSchema.validate(data, { abortEarly: false });
};

module.exports = { validateDeliveryDays };