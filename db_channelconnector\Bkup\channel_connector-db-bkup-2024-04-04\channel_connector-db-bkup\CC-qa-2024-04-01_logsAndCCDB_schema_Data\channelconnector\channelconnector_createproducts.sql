-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `createproducts`
--

DROP TABLE IF EXISTS `createproducts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `createproducts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) COLLATE utf8mb3_bin DEFAULT NULL,
  `channel` varchar(20) COLLATE utf8mb3_bin DEFAULT NULL,
  `domain` varchar(64) COLLATE utf8mb3_bin DEFAULT NULL,
  `owner_code` varchar(20) COLLATE utf8mb3_bin DEFAULT NULL,
  `sku` varchar(128) COLLATE utf8mb3_bin DEFAULT NULL,
  `barcode` varchar(128) COLLATE utf8mb3_bin DEFAULT NULL,
  `item_id` varchar(127) COLLATE utf8mb3_bin DEFAULT NULL,
  `title` varchar(128) COLLATE utf8mb3_bin DEFAULT NULL,
  `item_product_id` varchar(127) COLLATE utf8mb3_bin DEFAULT NULL,
  `inventory_item_id` varchar(127) COLLATE utf8mb3_bin DEFAULT NULL,
  `location_id` varchar(256) COLLATE utf8mb3_bin NOT NULL DEFAULT '0',
  `previous_inventory_quantity` int DEFAULT NULL,
  `inventory_quantity` int DEFAULT NULL,
  `image` text COLLATE utf8mb3_bin,
  `price` decimal(10,2) DEFAULT NULL,
  `count` int unsigned NOT NULL DEFAULT '0',
  `fby_error_flag` int unsigned NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `cron_name` varchar(60) COLLATE utf8mb3_bin DEFAULT NULL,
  `cron_id` varchar(100) COLLATE utf8mb3_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `isChanged` tinyint DEFAULT '0',
  `description` text COLLATE utf8mb3_bin,
  `specialPrice` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `createproducts_id` (`fby_user_id`,`sku`,`item_product_id`,`title`,`inventory_item_id`,`item_id`)
) ENGINE=InnoDB AUTO_INCREMENT=238 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `createproducts`
--

LOCK TABLES `createproducts` WRITE;
/*!40000 ALTER TABLE `createproducts` DISABLE KEYS */;
INSERT INTO `createproducts` VALUES (6,'1002','Shopify IT-QA','https://shopping190.myshopify.com/','YT','BN_BRL_WFQ14479','','0','MY PRODUCT','0','0','0',-1,0,'',NULL,0,0,0,'get_Fby_Products','453a6ad9-90b7-4c2a-a490-fbb0e35600a5','2022-07-07 08:43:28','2023-03-02 07:14:34',0,'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Malesuada fames ac turpis egestas maecenas pharetra convallis posuere morbi. Vitae tortor condimentum lacinia quis vel eros donec ac odio. Ut etiam sit amet nisl purus in mollis nunc. Quam adipiscing vitae proin sagittis nisl rhoncus mattis rhoncus. Vestibulum lectus mauris ultrices eros in cursus. Venenatis tellus in metus vulputate eu. Metus vulputate eu scelerisque felis imperdiet proin. Porta lorem mollis aliquam ut porttitor. Ac felis donec et odio. Sed sed risus pretium quam vulputate dignissim suspendisse. Sagittis aliquam malesuada bibendum arcu vitae elementum curabitur. Accumsan tortor posuere ac ut consequat semper viverra nam libero. Nibh tortor id aliquet lectus proin nibh. Lacus vestibulum sed arcu non odio euismod lacinia. Non sodales neque sodales ut etiam sit amet nisl purus.',NULL),(7,'1002','Shopify IT-QA','https://shopping190.myshopify.com/','YT','BN_BRL_WFS14421','','0','Binda Breil 1','0','0','0',-1,0,'',NULL,0,0,0,'get_Fby_Products','453a6ad9-90b7-4c2a-a490-fbb0e35600a5','2022-07-07 08:43:28','2023-03-02 07:14:34',0,'',NULL),(8,'1002','Shopify IT-QA','https://shopping190.myshopify.com/','YT','BN_BRL_WFR14426','','0','Bina Breil CF0','0','0','0',-1,0,'',NULL,0,0,0,'get_Fby_Products','453a6ad9-90b7-4c2a-a490-fbb0e35600a5','2022-07-07 08:43:28','2023-03-02 07:14:34',0,'',NULL),(9,'1002','Shopify IT-QA','https://shopping190.myshopify.com/','YT','BN_BRL_WFR14426','','0','Binda Breil CF1','0','0','0',-1,0,'',NULL,0,0,0,'get_Fby_Products','453a6ad9-90b7-4c2a-a490-fbb0e35600a5','2022-07-07 08:43:28','2023-03-02 07:14:34',1,'',NULL),(10,'1002','Shopify IT-QA','https://shopping190.myshopify.com/','YT','BN_BRL_WFR14426','','0','Binda Breil CF2','0','0','0',-1,0,'',NULL,0,0,0,'get_Fby_Products','453a6ad9-90b7-4c2a-a490-fbb0e35600a5','2022-07-07 08:43:28','2023-03-02 07:14:33',1,'',NULL),(124,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','redmi','','0','Redmi','0','0','67998187763',-1,0,'',0.00,0,0,0,'','','2024-01-10 05:05:51','2024-03-06 09:27:43',0,'',NULL),(125,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','realmi','','0','Realmi','0','0','67998187763',-1,0,'',0.00,0,0,0,'','','2024-01-10 05:05:52',NULL,0,'Nice RealMi Phone',NULL),(130,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','swift','','0','Acer Laptop','0','0','67998187763',-1,0,'',0.00,0,0,0,'','','2024-01-12 03:41:20','2024-01-22 08:09:51',1,'Nice Laptop',NULL),(140,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','wood','','0','Cricket Ball','0','0','67998187763',-1,0,'',0.00,0,0,0,'','','2024-01-16 03:28:05',NULL,0,'Nice Cricket Ball',NULL),(157,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','iphone14-pro-max','','0','Iphone14-pro-max','0','0','67998187763',-1,0,'',0.00,0,0,0,'','','2024-01-29 05:41:51','2024-03-04 05:00:02',0,'',NULL),(158,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','iphone14-pro','','0','Iphone14-pro','0','0','67998187763',-1,0,'',0.00,0,0,0,'','','2024-01-29 05:41:52',NULL,0,'Nice Iphone Phone',NULL),(209,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','Test SKU','Barcode###','0','Test Test','0','0','67998187763',-1,10,'https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG',10.00,0,0,0,'','','2024-03-17 16:41:05','2024-03-17 19:40:55',0,'',NULL),(221,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','New sku','bar##','0','New Product','0','0','67998187763',-1,5,'https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG',50.00,0,0,0,'','','2024-03-21 05:18:11','2024-03-21 05:40:53',0,'',NULL),(227,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','Bulk-1','','0','Bul product','0','0','67998187763',-1,8,'https://www.91-img.com/gallery_images_uploads/5/8/588d43a38403fdceb3dfc30b8d537974ef88699e.JPG',50.00,0,0,0,'','','2024-03-21 06:35:37','2024-03-21 06:53:14',0,'',NULL);
/*!40000 ALTER TABLE `createproducts` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:53:02
