DROP PROCEDURE IF EXISTS channelconnector._2_channel_Put ;

DEL<PERSON><PERSON>ER $$
CREATE PROCEDURE channelconnector._2_channel_Put (
			`in_channelId` INT, 
            `in_groupCode` VARCHAR(256), 
            `in_currencyCode` VARCHAR(56), 
            `in_ownerCode` VARCHAR(256), 
            `in_channelCode` VARCHAR(256), 
            `in_channelName` VARCHAR(2048), 
            `in_domain` VARCHAR(2048), 
            `in_username` VA<PERSON><PERSON><PERSON>(2048), 
            `in_password` VARCHAR(2048), 
            `in_apiKey` VARCHAR(2048), 
            `in_secret` VARCHAR(2048), 
            `in_token` VARCHAR(2048), 
            `in_isEnabled` INT, 
            `in_orderSyncStartDate` DATETIME,
			`in_ebaycompatibilityLevel` VARCHAR(128), 
			`in_ebaydevId` VARCHAR(256),
            `in_ebayappId` VA<PERSON>HA<PERSON>(256),
            `in_ebaycertId` VARCHAR(256), 
            `in_ebaysiteId` VARCHAR(256),
            `in_stockUpdate` TINYINT(4),
            `in_orderSync` TINYINT(4),
            `in_productPublish` TINYINT(4),
            `in_priceUpdate` TINYINT(4),

			`in_amazon_Role` 			VARCHAR(128),
			`in_amazon_MarketPlaceID` 	VARCHAR(128),
			`in_amazon_SellerID` 		VARCHAR(128),
			`in_amazon_region`			VARCHAR(128)
)
BEGIN
	/*
		call channelconnector.`_2_channel_Put`(
						1030 ,# `channelId`,
			'AEU' ,# `groupCode` ,
			'EUR' ,# `currencyCode` ,
			'YT' ,# `ownerCode`,
			'SFIT' ,# `channelCode`, 
			'shopify' ,# `channelName`,
			'shopping170.myshopify.com' ,# `domain`, 
			NULL ,# `username`, 
			'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' ,# `p,#sword`, #api_p,#sword
			'2ec972a612088fc392de502d7e4c3887' ,# `apiKey`, #API KEY
			NULL ,# `secret`, 
			NULL ,# `token`,
			1, # `isEnabled`,
            '2022-02-28',
            'test234',
            'test',
            'test2',
            'test3',
            'test4',
             true,
            true,
            false,
            false
        );
        
        call channelconnector._2_channel_Get(1002);
        
        call channelconnector._2_channel_delete(1002);
        
        call channelconnector.`getShopifyUser`(1002);
        
        
    */
    DECLARE isExists TINYINT; 
    DECLARE isClientExists TINYINT; 
    DECLARE isDeletedExists TINYINT; 
    
    SET isDeletedExists = (
        SELECT 1 FROM channelconnector._2_channel   
        WHERE 
			`channelId` = `in_channelId` 
			AND `isActive` = 0
			LIMIT 1
    );
    
    IF(isDeletedExists = 1)
    THEN
		SET SQL_SAFE_UPDATES = 0;
        UPDATE `channelconnector`.`_2_channel` 
		SET 
			`isActive` = 1,
			`isEnabled` = 1,
			`modifiedOn` = NOW()
		WHERE
			`channelId` = `in_channelId`
			AND `isActive` = 0;
		SET SQL_SAFE_UPDATES = 1;
    END IF;
    
	SET isExists = (
        SELECT 1 FROM channelconnector._2_channel
        WHERE 
			`channelId` = `in_channelId`
			AND isActive = 1 
       LIMIT 1     
    );
    
	SET isClientExists = (
        SELECT 1 FROM channelconnector._1_client
        WHERE 
			LOWER(`ownerCode`) = LOWER(`in_ownerCode`) 
            AND isActive = 1 
		LIMIT 1
    );
   
    
	IF (isClientExists = 0 OR isClientExists IS NULL)
	THEN
		SELECT 1 AS isErrorClientNotFound;
        
	ELSE IF (isExists = 0 OR isExists IS NULL)
	THEN
		CALL channelconnector.`_2_channel_post`
		(
			`in_channelId` ,
			`in_groupCode` ,
			`in_currencyCode` ,
			`in_ownerCode` ,
			`in_channelCode`  ,
			`in_channelName` ,
			`in_domain`  ,
			`in_username` ,
			`in_password`  ,
			`in_apiKey`  ,
			`in_secret` ,
			`in_token`  ,
			`in_isEnabled` ,
            `in_orderSyncStartDate`,
            `in_ebaycompatibilityLevel`,
            `in_ebaydevId`,
            `in_ebayappId`,
            `in_ebaycertId`,
            `in_ebaysiteId`,
            `in_stockUpdate` ,
            `in_orderSync` ,
            `in_productPublish` ,
            `in_priceUpdate`,
            `in_amazon_Role` 			,
			`in_amazon_MarketPlaceID` 	,
			`in_amazon_SellerID` 		,
			`in_amazon_region`			
		);
        
	ELSE
		SET SQL_SAFE_UPDATES = 0;

		UPDATE `channelconnector`.`_2_channel`
		SET
			groupCode = `in_groupCode` ,
			currencyCode = `in_currencyCode` ,
			ownerCode = `in_ownerCode` ,
			channelCode = `in_channelCode`  ,
			channelName = `in_channelName` ,
			domain = `in_domain`  ,
			username = `in_username` ,
			`password` = `in_password`  ,
			apiKey = `in_apiKey`  ,
			secret = `in_secret` ,
			token = `in_token`  ,
			isEnabled = `in_isEnabled` ,
            orderSyncStartDate = `in_orderSyncStartDate`,
            compatibilityLevel = `in_ebaycompatibilityLevel`,
            ebay_devid = `in_ebaydevId`,
            ebay_appid = `in_ebayappId`,
            ebay_certid = `in_ebaycertId`,
            siteId = `in_ebaysiteId`,
			`stockUpdate` = `in_stockUpdate`,
			`priceUpdate` = `in_priceUpdate`,
			`orderSync`= `in_orderSync`,
			`productPublish` = `in_productPublish`,
			`modifiedOn` = NOW()
		WHERE
			`channelId` = `in_channelId` 
			AND isActive = 1;
		
		SET SQL_SAFE_UPDATES = 1;

		call channelconnector._2_channel_Get(`in_channelId`);
             
	END IF;
    END IF;
    
END$$
DELIMITER ;