-- MySQL dump 10.13  Distrib 8.0.26, for Win64 (x86_64)
--
-- Host: ************    Database: channelconnector
-- ------------------------------------------------------
-- Server version	8.0.36-0ubuntu0.20.04.1

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `createdproducts`
--

DROP TABLE IF EXISTS `createdproducts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `createdproducts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `fby_user_id` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `channel` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `domain` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `owner_code` varchar(20) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `sku` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `barcode` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `item_id` varchar(127) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `title` varchar(128) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `item_product_id` varchar(127) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `inventory_item_id` varchar(127) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `location_id` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '0',
  `previous_inventory_quantity` int DEFAULT NULL,
  `inventory_quantity` int DEFAULT NULL,
  `image` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin,
  `price` decimal(10,2) DEFAULT NULL,
  `count` int unsigned NOT NULL DEFAULT '0',
  `fby_error_flag` int unsigned NOT NULL DEFAULT '0',
  `status` tinyint NOT NULL DEFAULT '0',
  `cron_name` varchar(60) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `cron_id` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `isChanged` tinyint DEFAULT '0',
  `description` text,
  `specialPrice` decimal(10,0) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `createdproducts_fby_user_id` (`fby_user_id`,`sku`)
) ENGINE=InnoDB AUTO_INCREMENT=116 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `createdproducts`
--

LOCK TABLES `createdproducts` WRITE;
/*!40000 ALTER TABLE `createdproducts` DISABLE KEYS */;
INSERT INTO `createdproducts` VALUES (4,'1002','Shopify IT-QA','shopping190.myshopify.com','YT','BN_BRL_WFS14421',NULL,'7731702595827','Binda Breil 1','7731702595827','45053222945011','0',-1,0,'',0.00,0,0,0,'create_Products_Shopify','43c77a2a-7015-4e54-aad3-c56f178233fc','2022-07-07 08:44:20',NULL,0,NULL,NULL),(5,'1002','Shopify IT-QA','shopping190.myshopify.com','YT','BN_BRL_WFQ14479',NULL,'7731702628595','MY PRODUCT','7731702628595','45053222977779','0',-1,0,'',0.00,0,0,0,'create_Products_Shopify','43c77a2a-7015-4e54-aad3-c56f178233fc','2022-07-07 08:44:20',NULL,0,NULL,NULL),(6,'1002','Shopify IT-QA','shopping190.myshopify.com','YT','BN_BRL_WFR14426',NULL,'7731702661363','Bina Breil CF0','7731702661363','45053223010547','0',-1,0,'',0.00,0,0,0,'create_Products_Shopify','43c77a2a-7015-4e54-aad3-c56f178233fc','2022-07-07 08:44:21',NULL,0,NULL,NULL),(81,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','redmi','','8874364469583','Redmi','8874364469583','8874364469583','0',-1,0,'https://cdn.shopify.com/s/files/1/0632/0645/7587/files/Redmi_Note_10_Pro_Back__283_29.jpg?v=1704867951',0.00,0,0,0,'GET_PRODUCT_FROM_CHANNEL','51b42318-2096-4453-8788-a488cbb67673','2024-01-10 05:07:37',NULL,0,'',NULL),(82,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','realmi','','8874364502351','Realmi','8874364502351','8874364502351','0',-1,0,'',0.00,0,0,0,'GET_PRODUCT_FROM_CHANNEL','51b42318-2096-4453-8788-a488cbb67673','2024-01-10 05:07:37',NULL,0,'',NULL),(87,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','swift','','8877828276559','Acer Laptop','8877828276559','8877828276559','0',-1,0,'https://cdn.shopify.com/s/files/1/0632/0645/7587/files/Acer_Swift_7_20201101a_41b11d72-1650-4a24-a0b3-c79911959fe6.jpg?v=1705030990',0.00,0,0,0,'GET_PRODUCT_FROM_CHANNEL','155b6d7d-95a9-473c-a63a-82d305d01eee','2024-01-12 03:41:31',NULL,0,'',NULL),(94,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','wood','','8882907677007','Cricket ball','8882907677007','8882907677007','0',-1,0,'',0.00,0,0,0,'GET_PRODUCT_FROM_CHANNEL','3aca04c4-ac1c-4855-9531-4cabf9b328f9','2024-01-16 03:28:21',NULL,0,'',NULL),(107,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','iphone14-pro-max','','8904497725775','Iphone14-pro-max','8904497725775','8904497725775','0',-1,0,'https://cdn.shopify.com/s/files/1/0632/0645/7587/files/588d43a38403fdceb3dfc30b8d537974ef88699e_fe921c2c-cb25-4a92-9dd4-a1f6f2379ad2.jpg?v=1706508991',0.00,0,0,0,'GET_PRODUCT_FROM_CHANNEL','bdf6980f-033d-443a-ae78-051779023524','2024-01-29 05:41:56',NULL,0,'',NULL),(108,'8','Shopify IT','https://shopping190.myshopify.com/','FDM','iphone14-pro','','8904497758543','Iphone14-pro','8904497758543','8904497758543','0',-1,0,'',0.00,0,0,0,'GET_PRODUCT_FROM_CHANNEL','bdf6980f-033d-443a-ae78-051779023524','2024-01-29 05:41:56',NULL,0,'',NULL);
/*!40000 ALTER TABLE `createdproducts` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-04-02 19:41:01
